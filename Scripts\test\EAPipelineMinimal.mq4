//+------------------------------------------------------------------+
//|                                          EAPipelineMinimal.mq4 |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

// 包含最基本的測試框架
#include "../../Projects/EA_Wizard/module/EAPipeline/test/TestFramework.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== EAPipeline 最小測試開始 ===");
    
    // 測試基本的 TestFramework 功能
    TestBasicFramework();
    
    Print("=== EAPipeline 最小測試完成 ===");
}

//+------------------------------------------------------------------+
//| 測試基本框架功能                                                 |
//+------------------------------------------------------------------+
void TestBasicFramework()
{
    Print("開始測試基本框架功能...");
    
    int totalTests = 0;
    int passedTests = 0;
    int failedTests = 0;
    
    // 測試 Assert::AssertTrue
    TestResult* result1 = Assert::AssertTrue("測試_真值", true);
    if(result1 != NULL)
    {
        totalTests++;
        if(result1.IsPassed())
        {
            passedTests++;
            Print(StringFormat("[通過] %s", result1.GetTestName()));
        }
        else
        {
            failedTests++;
            Print(StringFormat("[失敗] %s: %s", result1.GetTestName(), result1.GetMessage()));
        }
        delete result1;
    }
    
    // 測試 Assert::AssertFalse
    TestResult* result2 = Assert::AssertFalse("測試_假值", false);
    if(result2 != NULL)
    {
        totalTests++;
        if(result2.IsPassed())
        {
            passedTests++;
            Print(StringFormat("[通過] %s", result2.GetTestName()));
        }
        else
        {
            failedTests++;
            Print(StringFormat("[失敗] %s: %s", result2.GetTestName(), result2.GetMessage()));
        }
        delete result2;
    }
    
    // 測試 Assert::AssertEquals (字符串)
    TestResult* result3 = Assert::AssertEquals("測試_字符串相等", "test", "test");
    if(result3 != NULL)
    {
        totalTests++;
        if(result3.IsPassed())
        {
            passedTests++;
            Print(StringFormat("[通過] %s", result3.GetTestName()));
        }
        else
        {
            failedTests++;
            Print(StringFormat("[失敗] %s: %s", result3.GetTestName(), result3.GetMessage()));
        }
        delete result3;
    }
    
    // 測試 Assert::AssertEquals (整數)
    TestResult* result4 = Assert::AssertEquals("測試_整數相等", 123, 123);
    if(result4 != NULL)
    {
        totalTests++;
        if(result4.IsPassed())
        {
            passedTests++;
            Print(StringFormat("[通過] %s", result4.GetTestName()));
        }
        else
        {
            failedTests++;
            Print(StringFormat("[失敗] %s: %s", result4.GetTestName(), result4.GetMessage()));
        }
        delete result4;
    }
    
    // 測試 Assert::AssertNotNull
    string testString = "not null";
    TestResult* result5 = Assert::AssertNotNull("測試_不為空", GetPointer(testString));
    if(result5 != NULL)
    {
        totalTests++;
        if(result5.IsPassed())
        {
            passedTests++;
            Print(StringFormat("[通過] %s", result5.GetTestName()));
        }
        else
        {
            failedTests++;
            Print(StringFormat("[失敗] %s: %s", result5.GetTestName(), result5.GetMessage()));
        }
        delete result5;
    }
    
    // 測試 Assert::AssertNull
    TestResult* result6 = Assert::AssertNull("測試_為空", NULL);
    if(result6 != NULL)
    {
        totalTests++;
        if(result6.IsPassed())
        {
            passedTests++;
            Print(StringFormat("[通過] %s", result6.GetTestName()));
        }
        else
        {
            failedTests++;
            Print(StringFormat("[失敗] %s: %s", result6.GetTestName(), result6.GetMessage()));
        }
        delete result6;
    }
    
    // 顯示測試摘要
    Print("=== 測試摘要 ===");
    Print(StringFormat("總測試數: %d", totalTests));
    Print(StringFormat("通過: %d", passedTests));
    Print(StringFormat("失敗: %d", failedTests));
    
    if(totalTests > 0)
    {
        double successRate = (double)passedTests / totalTests * 100.0;
        Print(StringFormat("成功率: %.2f%%", successRate));
    }
    else
    {
        Print("成功率: 0.00%");
    }
    
    Print("基本框架測試完成");
}
