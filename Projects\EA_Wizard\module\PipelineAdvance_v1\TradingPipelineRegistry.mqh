//+------------------------------------------------------------------+
//|                                      TradingPipelineRegistry.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "TradingPipelineContainerManager.mqh"
#include "TradingPipeline.mqh"
#include "TradingEvent.mqh"
#include "../mql4-lib-master/Collection/HashMap.mqh"

//+------------------------------------------------------------------+
//| 交易流水線註冊器                                                 |
//| 負責管理和註冊交易階段和事件到容器管理器                         |
//+------------------------------------------------------------------+
class TradingPipelineRegistry
{
private:
    TradingPipelineContainerManager* m_manager;         // 容器管理器指針
    HashMap<int, TradingPipelineContainer*> m_registeredStages; // 已註冊的交易階段
    HashMap<int, TradingPipelineContainer*> m_registeredEvents; // 已註冊的交易事件
    string m_name;                                       // 註冊器名稱
    string m_type;                                       // 註冊器類型
    int m_maxRegistrations;                              // 最大註冊數量
    bool m_isEnabled;                                    // 是否啟用
    bool m_owned;                                        // 是否擁有流水線
    PipelineResult* m_last_result;                       // 執行結果

public:
    // 構造函數
    TradingPipelineRegistry(TradingPipelineContainerManager* manager,
                           string name = "TradingPipelineRegistry",
                           string type = "PipelineRegistry",
                           int maxRegistrations = 50,
                           bool owned = true)
        : m_manager(manager),
          m_name(name),
          m_type(type),
          m_maxRegistrations(maxRegistrations),
          m_isEnabled(true),
          m_owned(owned),
          m_registeredStages(NULL, false),
          m_registeredEvents(NULL, false),
          m_last_result(new PipelineResult(false, "註冊器尚未執行註冊", name, ERROR_LEVEL_INFO))
    {
        // 驗證管理器指針
        if(m_manager == NULL)
        {
            m_last_result = new PipelineResult(false, "管理器指針不能為 NULL", name, ERROR_LEVEL_ERROR);
        }
        else
        {
            m_last_result = new PipelineResult(true, "註冊器構造完成", name, ERROR_LEVEL_INFO);
        }
    }

    // 析構函數
    virtual ~TradingPipelineRegistry()
    {
        Clear();
        if(m_last_result != NULL)
        {
            delete m_last_result;
            m_last_result = NULL;
        }
    }

    // 註冊交易階段
    bool Register(ENUM_TRADING_STAGE stage, TradingPipelineContainer* pipeline)
    {
        // 檢查註冊器是否啟用
        if(!m_isEnabled)
        {
            m_last_result = new PipelineResult(false, "註冊器已禁用，無法註冊階段: " + TradingEventUtils::StageToString(stage), GetName(), ERROR_LEVEL_WARNING);
            return false;
        }

        // 檢查管理器是否有效
        if(m_manager == NULL)
        {
            m_last_result = new PipelineResult(false, "管理器指針為 NULL，無法註冊階段: " + TradingEventUtils::StageToString(stage), GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        // 檢查容器指針是否有效
        if(pipeline == NULL)
        {
            m_last_result = new PipelineResult(false, "容器指針不能為 NULL，無法註冊階段: " + TradingEventUtils::StageToString(stage), GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        // 檢查是否已達到最大註冊數量
        if(GetTotalRegistrations() >= m_maxRegistrations)
        {
            m_last_result = new PipelineResult(false, StringFormat("已達到最大註冊數量 %d", m_maxRegistrations), GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        // 檢查階段是否有效（檢查階段是否在有效範圍內）
        if(!IsValidStage(stage))
        {
            m_last_result = new PipelineResult(false, StringFormat("無效的交易階段 %d", (int)stage), GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        // 檢查是否已經註冊
        int stageKey = (int)stage;
        if(m_registeredStages.contains(stageKey))
        {
            m_last_result = new PipelineResult(false, "階段已經註冊: " + TradingEventUtils::StageToString(stage), GetName(), ERROR_LEVEL_WARNING);
            return false;
        }

        // 獲取階段對應的事件
        ENUM_TRADING_EVENT event = GetStageEvent(stage);

        // 直接設置容器到管理器
        if(!m_manager.SetContainer(event, pipeline))
        {
            m_last_result = new PipelineResult(false, "無法設置容器到管理器", GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        // 註冊階段
        m_registeredStages.set(stageKey, pipeline);

        m_last_result = new PipelineResult(true, "階段註冊成功: " + TradingEventUtils::StageToString(stage), GetName(), ERROR_LEVEL_INFO);
        return true;
    }

    // 註冊交易事件
    bool Register(ENUM_TRADING_EVENT event, TradingPipelineContainer* pipeline)
    {
        // 檢查註冊器是否啟用
        if(!m_isEnabled)
        {
            m_last_result = new PipelineResult(false, "註冊器已禁用，無法註冊事件: " + TradingEventUtils::EventToString(event), GetName(), ERROR_LEVEL_WARNING);
            return false;
        }

        // 檢查管理器是否有效
        if(m_manager == NULL)
        {
            m_last_result = new PipelineResult(false, "管理器指針為 NULL，無法註冊事件: " + TradingEventUtils::EventToString(event), GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        // 檢查容器指針是否有效
        if(pipeline == NULL)
        {
            m_last_result = new PipelineResult(false, "容器指針不能為 NULL，無法註冊事件: " + TradingEventUtils::EventToString(event), GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        // 檢查是否已達到最大註冊數量
        if(GetTotalRegistrations() >= m_maxRegistrations)
        {
            m_last_result = new PipelineResult(false, StringFormat("已達到最大註冊數量 %d", m_maxRegistrations), GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        // 檢查事件是否有效
        if(!TradingEventUtils::IsValidEvent(event))
        {
            m_last_result = new PipelineResult(false, StringFormat("無效的交易事件 %d", (int)event), GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        // 檢查是否已經註冊
        int eventKey = (int)event;
        if(m_registeredEvents.contains(eventKey))
        {
            m_last_result = new PipelineResult(false, "事件已經註冊: " + TradingEventUtils::EventToString(event), GetName(), ERROR_LEVEL_WARNING);
            return false;
        }

        // 直接設置容器到管理器
        if(!m_manager.SetContainer(event, pipeline))
        {
            m_last_result = new PipelineResult(false, "無法設置容器到管理器", GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        // 註冊事件
        m_registeredEvents.set(eventKey, pipeline);

        m_last_result = new PipelineResult(true, "事件註冊成功: " + TradingEventUtils::EventToString(event), GetName(), ERROR_LEVEL_INFO);
        return true;
    }

    // 註冊 TradingPipeline 到對應的容器中
    bool Register(TradingPipeline* pipeline)
    {
        // 檢查註冊器是否啟用
        if(!m_isEnabled)
        {
            m_last_result = new PipelineResult(false, "註冊器已禁用，無法註冊流水線", GetName(), ERROR_LEVEL_WARNING);
            return false;
        }

        // 檢查管理器是否有效
        if(m_manager == NULL)
        {
            m_last_result = new PipelineResult(false, "管理器指針為 NULL，無法註冊流水線", GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        // 檢查流水線指針是否有效
        if(pipeline == NULL)
        {
            m_last_result = new PipelineResult(false, "流水線指針不能為 NULL", GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        // 獲取流水線的階段
        ENUM_TRADING_STAGE stage = pipeline.GetStage();

        // 獲取階段對應的事件
        ENUM_TRADING_EVENT event = GetStageEvent(stage);

        // 檢查管理器中是否已有對應事件的容器
        TradingPipelineContainer* existingContainer = m_manager.GetContainer(event);
        if(existingContainer != NULL)
        {
            // 將流水線添加到現有容器中
            if(!existingContainer.AddPipeline(pipeline))
            {
                m_last_result = new PipelineResult(false, "無法將流水線添加到容器", GetName(), ERROR_LEVEL_ERROR);
                return false;
            }
        }
        else
        {
            m_last_result = new PipelineResult(false, "沒有對應的容器來容納流水線，階段: " + TradingEventUtils::StageToString(stage), GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        m_last_result = new PipelineResult(true, "成功註冊流水線 " + pipeline.GetName() + " 到階段 " + TradingEventUtils::StageToString(stage), GetName(), ERROR_LEVEL_INFO);
        return true;
    }

    // 檢查階段是否已註冊
    bool IsStageRegistered(ENUM_TRADING_STAGE stage) const
    {
        return m_registeredStages.contains((int)stage);
    }

    // 檢查事件是否已註冊
    bool IsEventRegistered(ENUM_TRADING_EVENT event) const
    {
        return m_registeredEvents.contains((int)event);
    }

    // 獲取已註冊的階段容器
    TradingPipelineContainer* GetRegisteredStageContainer(ENUM_TRADING_STAGE stage) const
    {
        return m_registeredStages.get((int)stage, NULL);
    }

    // 獲取已註冊的事件容器
    TradingPipelineContainer* GetRegisteredEventContainer(ENUM_TRADING_EVENT event) const
    {
        return m_registeredEvents.get((int)event, NULL);
    }

    // 取消註冊階段
    bool UnregisterStage(ENUM_TRADING_STAGE stage)
    {
        int stageKey = (int)stage;
        if(!m_registeredStages.contains(stageKey))
        {
            return false;
        }

        // 獲取已註冊的容器
        TradingPipelineContainer* container = m_registeredStages.get(stageKey, NULL);
        if(container != NULL)
        {
            // 從管理器中移除容器
            if(!m_manager.RemoveContainer(container))
            {
                m_last_result = new PipelineResult(false, "無法從管理器中移除容器", GetName(), ERROR_LEVEL_WARNING);
            }

            // 如果擁有容器，則刪除它
            if(m_owned)
            {
                delete container;
            }
        }

        bool result = m_registeredStages.remove(stageKey);
        if(result)
        {
            m_last_result = new PipelineResult(true, "成功取消註冊階段: " + TradingEventUtils::StageToString(stage), GetName(), ERROR_LEVEL_INFO);
        }

        return result;
    }

    // 取消註冊事件
    bool UnregisterEvent(ENUM_TRADING_EVENT event)
    {
        int eventKey = (int)event;
        if(!m_registeredEvents.contains(eventKey))
        {
            return false;
        }

        // 獲取已註冊的容器
        TradingPipelineContainer* container = m_registeredEvents.get(eventKey, NULL);
        if(container != NULL)
        {
            // 從管理器中移除容器
            if(!m_manager.RemoveContainer(container))
            {
                m_last_result = new PipelineResult(false, "無法從管理器中移除容器", GetName(), ERROR_LEVEL_WARNING);
            }

            // 如果擁有容器，則刪除它
            if(m_owned)
            {
                delete container;
            }
        }

        bool result = m_registeredEvents.remove(eventKey);
        if(result)
        {
            m_last_result = new PipelineResult(true, "成功取消註冊事件: " + TradingEventUtils::EventToString(event), GetName(), ERROR_LEVEL_INFO);
        }

        return result;
    }

    // 取消註冊 TradingPipeline
    bool Unregister(TradingPipeline* pipeline)
    {
        // 檢查流水線指針是否有效
        if(pipeline == NULL)
        {
            m_last_result = new PipelineResult(false, "流水線指針不能為 NULL", GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        // 檢查管理器是否有效
        if(m_manager == NULL)
        {
            m_last_result = new PipelineResult(false, "管理器指針為 NULL", GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        // 獲取流水線的階段
        ENUM_TRADING_STAGE stage = pipeline.GetStage();

        // 獲取階段對應的事件
        ENUM_TRADING_EVENT event = GetStageEvent(stage);

        // 檢查管理器中是否有對應事件的容器
        TradingPipelineContainer* existingContainer = m_manager.GetContainer(event);
        if(existingContainer != NULL)
        {
            // 從容器中移除流水線
            if(!existingContainer.RemovePipeline(pipeline))
            {
                m_last_result = new PipelineResult(false, "無法從容器中移除流水線", GetName(), ERROR_LEVEL_WARNING);
                return false;
            }
        }
        else
        {
            m_last_result = new PipelineResult(false, "沒有找到對應的容器，階段: " + TradingEventUtils::StageToString(stage), GetName(), ERROR_LEVEL_WARNING);
            return false;
        }

        m_last_result = new PipelineResult(true, "成功取消註冊流水線 " + pipeline.GetName() + " 從階段 " + TradingEventUtils::StageToString(stage), GetName(), ERROR_LEVEL_INFO);
        return true;
    }

    // 清理所有註冊
    void Clear()
    {
        int totalCleared = GetTotalRegistrations();

        // 如果擁有容器，先刪除所有已註冊的容器
        if(m_owned)
        {
            // 清理階段容器
            foreachm(int, stageKey, TradingPipelineContainer*, container, m_registeredStages)
            {
                if(container != NULL)
                {
                    delete container;
                }
            }

            // 清理事件容器
            foreachm(int, eventKey, TradingPipelineContainer*, container, m_registeredEvents)
            {
                if(container != NULL)
                {
                    delete container;
                }
            }
        }

        m_registeredStages.clear();
        m_registeredEvents.clear();

        // 安全地更新結果
        if(m_last_result != NULL)
        {
            delete m_last_result;
        }
        m_last_result = new PipelineResult(true, StringFormat("已清理 %d 個註冊項目", totalCleared), GetName(), ERROR_LEVEL_INFO);
    }

    // 獲取已註冊階段數量
    int GetRegisteredStageCount() const
    {
        return m_registeredStages.size();
    }

    // 獲取已註冊事件數量
    int GetRegisteredEventCount() const
    {
        return m_registeredEvents.size();
    }

    // 獲取總註冊數量
    int GetTotalRegistrations() const
    {
        return m_registeredStages.size() + m_registeredEvents.size();
    }

    // 獲取最大註冊數量
    int GetMaxRegistrations() const
    {
        return m_maxRegistrations;
    }

    // 獲取管理器指針
    TradingPipelineContainerManager* GetManager() const
    {
        return m_manager;
    }

    // 獲取註冊器名稱
    string GetName() const
    {
        return m_name;
    }

    // 獲取註冊器類型
    string GetType() const
    {
        return m_type;
    }

    // 設置啟用狀態
    void SetEnabled(bool enabled)
    {
        m_isEnabled = enabled;
    }

    // 檢查是否啟用
    bool IsEnabled() const
    {
        return m_isEnabled;
    }

    // 檢查是否已滿
    bool IsFull() const
    {
        return GetTotalRegistrations() >= m_maxRegistrations;
    }

    // 檢查是否為空
    bool IsEmpty() const
    {
        return GetTotalRegistrations() == 0;
    }

    // 檢查是否擁有流水線
    bool IsOwned() const
    {
        return m_owned;
    }

    // 設置擁有狀態
    void SetOwned(bool owned)
    {
        m_owned = owned;
    }

    // 獲取註冊器狀態信息
    string GetStatusInfo() const
    {
        string info = StringFormat(
            "註冊器名稱: %s\n"
            "類型: %s\n"
            "狀態: %s\n"
            "擁有流水線: %s\n"
            "已註冊階段: %d\n"
            "已註冊事件: %d\n"
            "總註冊數: %d/%d\n"
            "管理器: %s",
            m_name,
            m_type,
            m_isEnabled ? "啟用" : "禁用",
            m_owned ? "是" : "否",
            m_registeredStages.size(),
            m_registeredEvents.size(),
            GetTotalRegistrations(),
            m_maxRegistrations,
            (m_manager != NULL) ? m_manager.GetName() : "NULL"
        );
        return info;
    }

    // 獲取執行結果
    PipelineResult* GetResult() const
    {
        return m_last_result;
    }
    // 檢查階段是否有效
    bool IsValidStage(ENUM_TRADING_STAGE stage) const
    {
        return (stage >= INIT_START && stage <= DEINIT_COMPLETE);
    }

    // 根據階段獲取對應的事件
    ENUM_TRADING_EVENT GetStageEvent(ENUM_TRADING_STAGE stage) const
    {
        // 初始化階段
        if(stage >= INIT_START && stage <= INIT_COMPLETE)
        {
            return TRADING_INIT;
        }
        // 交易階段
        else if(stage >= TICK_DATA_FEED && stage <= TICK_LOGGING)
        {
            return TRADING_TICK;
        }
        // 清理階段
        else if(stage >= DEINIT_CLEANUP && stage <= DEINIT_COMPLETE)
        {
            return TRADING_DEINIT;
        }

        // 無效階段，返回默認值
        return TRADING_INIT;
    }
};
