#property strict

#include "../TestFramework.mqh"
#include "../../ObjectRegistry.mqh"

//+------------------------------------------------------------------+
//| TestObjectRegistry 類別                                         |
//| 為 ObjectRegistry 類別提供完整的單元測試覆蓋                     |
//+------------------------------------------------------------------+
class TestObjectRegistry : public TestCase
{
private:
    ObjectRegistry* m_registry;
    TestRunner* m_runner;

public:
    // 構造函數
    TestObjectRegistry(TestRunner* runner = NULL) : TestCase("TestObjectRegistry")
    {
        m_registry = NULL;
        m_runner = runner;
    }

    // 析構函數
    ~TestObjectRegistry()
    {
        if(m_registry != NULL)
        {
            delete m_registry;
            m_registry = NULL;
        }
    }

    // 運行所有測試
    virtual void RunTests() override
    {
        Print("開始執行 ObjectRegistry 單元測試...");

        TestConstructor();
        TestBasicProperties();
        TestRegisterObject();
        TestUnregisterObject();
        TestGetObject();
        TestContains();

        Print("ObjectRegistry 單元測試完成");
    }

protected:
    // 測試構造函數
    void TestConstructor()
    {
        Print("測試 ObjectRegistry 構造函數...");

        // 測試默認構造函數
        ObjectRegistry* registry1 = new ObjectRegistry();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertNotNull("TestObjectRegistry::TestConstructor - 默認構造函數創建對象", registry1, "默認構造函數應該創建有效對象"));
            m_runner.RecordResult(Assert::AssertEquals("TestObjectRegistry::TestConstructor - 默認名稱", OBJECT_REGISTRY_DEFAULT_NAME, registry1.GetName(), "默認名稱應該正確"));
            m_runner.RecordResult(Assert::AssertEquals("TestObjectRegistry::TestConstructor - 默認類型", OBJECT_REGISTRY_DEFAULT_TYPE, registry1.GetType(), "默認類型應該正確"));
            m_runner.RecordResult(Assert::AssertFalse("TestObjectRegistry::TestConstructor - 默認所有權", registry1.IsOwned(), "默認不應該擁有對象所有權"));
            m_runner.RecordResult(Assert::AssertTrue("TestObjectRegistry::TestConstructor - 默認為空", registry1.IsEmpty(), "新創建的註冊器應該為空"));
        }
        delete registry1;

        // 測試帶參數構造函數
        ObjectRegistry* registry2 = new ObjectRegistry("TestRegistry", "TestType", true);
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertNotNull("TestObjectRegistry::TestConstructor - 帶參數構造函數創建對象", registry2, "帶參數構造函數應該創建有效對象"));
            m_runner.RecordResult(Assert::AssertEquals("TestObjectRegistry::TestConstructor - 自定義名稱", "TestRegistry", registry2.GetName(), "自定義名稱應該正確"));
            m_runner.RecordResult(Assert::AssertEquals("TestObjectRegistry::TestConstructor - 自定義類型", "TestType", registry2.GetType(), "自定義類型應該正確"));
            m_runner.RecordResult(Assert::AssertTrue("TestObjectRegistry::TestConstructor - 自定義所有權", registry2.IsOwned(), "應該擁有對象所有權"));
        }
        delete registry2;

        Print("✅ ObjectRegistry 構造函數測試通過");
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("測試 ObjectRegistry 基本屬性...");

        m_registry = new ObjectRegistry("TestRegistry", "TestType", false);

        // 測試基本屬性
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertEquals("TestObjectRegistry::TestBasicProperties - 名稱", "TestRegistry", m_registry.GetName(), "名稱應該正確"));
            m_runner.RecordResult(Assert::AssertEquals("TestObjectRegistry::TestBasicProperties - 類型", "TestType", m_registry.GetType(), "類型應該正確"));
            m_runner.RecordResult(Assert::AssertFalse("TestObjectRegistry::TestBasicProperties - 所有權", m_registry.IsOwned(), "所有權標誌應該正確"));
            m_runner.RecordResult(Assert::AssertTrue("TestObjectRegistry::TestBasicProperties - 為空", m_registry.IsEmpty(), "新註冊器應該為空"));
            m_runner.RecordResult(Assert::AssertEquals("TestObjectRegistry::TestBasicProperties - 對象數量", 0, m_registry.GetCount(), "對象數量應該為0"));
            m_runner.RecordResult(Assert::AssertEquals("TestObjectRegistry::TestBasicProperties - 最後註冊鍵", "", m_registry.GetLastRegisteredKey(), "最後註冊的鍵應該為空"));
        }

        // 測試設置屬性
        m_registry.SetName("NewName");
        m_registry.SetType("NewType");
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertEquals("TestObjectRegistry::TestBasicProperties - 設置名稱", "NewName", m_registry.GetName(), "設置名稱應該生效"));
            m_runner.RecordResult(Assert::AssertEquals("TestObjectRegistry::TestBasicProperties - 設置類型", "NewType", m_registry.GetType(), "設置類型應該生效"));
        }

        delete m_registry;
        m_registry = NULL;

        Print("✅ ObjectRegistry 基本屬性測試通過");
    }

    // 測試註冊對象
    void TestRegisterObject()
    {
        Print("測試 ObjectRegistry 註冊對象...");

        m_registry = new ObjectRegistry("TestRegistry", "TestType", false);

        // 創建測試對象（使用對象指針作為測試對象）
        ObjectRegistry* testObj1 = new ObjectRegistry("Test1", "Type1", false);
        ObjectRegistry* testObj2 = new ObjectRegistry("Test2", "Type2", false);
        void* testPtr1 = testObj1;
        void* testPtr2 = testObj2;

        // 測試成功註冊
        bool result1 = m_registry.Register("key1", testPtr1, "Object1", "Test object 1", "ObjectRegistry");
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("TestObjectRegistry::TestRegisterObject - 註冊成功", result1, "註冊應該成功"));
            m_runner.RecordResult(Assert::AssertFalse("TestObjectRegistry::TestRegisterObject - 註冊後不為空", m_registry.IsEmpty(), "註冊後不應該為空"));
            m_runner.RecordResult(Assert::AssertEquals("TestObjectRegistry::TestRegisterObject - 對象數量1", 1, m_registry.GetCount(), "對象數量應該為1"));
            m_runner.RecordResult(Assert::AssertEquals("TestObjectRegistry::TestRegisterObject - 最後註冊鍵", "key1", m_registry.GetLastRegisteredKey(), "最後註冊的鍵應該正確"));
        }

        // 測試重複註冊
        bool result2 = m_registry.Register("key1", testPtr2, "Object2", "Test object 2", "ObjectRegistry");
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertFalse("TestObjectRegistry::TestRegisterObject - 重複註冊失敗", result2, "重複註冊應該失敗"));
            m_runner.RecordResult(Assert::AssertEquals("TestObjectRegistry::TestRegisterObject - 重複註冊不增加數量", 1, m_registry.GetCount(), "重複註冊不應該增加對象數量"));
        }

        // 測試註冊第二個對象
        bool result3 = m_registry.Register("key2", testPtr2, "Object2", "Test object 2", "ObjectRegistry");
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("TestObjectRegistry::TestRegisterObject - 註冊第二個對象", result3, "註冊第二個對象應該成功"));
            m_runner.RecordResult(Assert::AssertEquals("TestObjectRegistry::TestRegisterObject - 對象數量2", 2, m_registry.GetCount(), "對象數量應該為2"));
        }

        // 清理
        delete testObj1;
        delete testObj2;
        delete m_registry;
        m_registry = NULL;

        Print("✅ ObjectRegistry 註冊對象測試通過");
    }

    // 測試移除對象
    void TestUnregisterObject()
    {
        Print("測試 ObjectRegistry 移除對象...");

        m_registry = new ObjectRegistry("TestRegistry", "TestType", false);

        // 創建測試對象
        ObjectRegistry* testObj = new ObjectRegistry("TestObj", "TestType", false);
        void* testPtr = testObj;

        // 先註冊對象
        m_registry.Register("key1", testPtr, "Object1", "Test object", "ObjectRegistry");
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertEquals("TestObjectRegistry::TestUnregisterObject - 註冊後數量", 1, m_registry.GetCount(), "註冊後對象數量應該為1"));
        }

        // 測試成功移除
        bool result1 = m_registry.Unregister("key1");
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("TestObjectRegistry::TestUnregisterObject - 移除成功", result1, "移除應該成功"));
            m_runner.RecordResult(Assert::AssertEquals("TestObjectRegistry::TestUnregisterObject - 移除後數量", 0, m_registry.GetCount(), "移除後對象數量應該為0"));
            m_runner.RecordResult(Assert::AssertTrue("TestObjectRegistry::TestUnregisterObject - 移除後為空", m_registry.IsEmpty(), "移除後應該為空"));
        }

        // 測試移除不存在的鍵
        bool result2 = m_registry.Unregister("nonexistent");
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertFalse("TestObjectRegistry::TestUnregisterObject - 移除不存在鍵", result2, "移除不存在的鍵應該失敗"));
        }

        // 測試移除空鍵
        bool result3 = m_registry.Unregister("");
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertFalse("TestObjectRegistry::TestUnregisterObject - 移除空鍵", result3, "移除空鍵應該失敗"));
        }

        // 清理
        delete testObj;
        delete m_registry;
        m_registry = NULL;

        Print("✅ ObjectRegistry 移除對象測試通過");
    }

    // 測試獲取對象
    void TestGetObject()
    {
        Print("測試 ObjectRegistry 獲取對象...");

        m_registry = new ObjectRegistry("TestRegistry", "TestType", false);

        // 創建測試對象
        ObjectRegistry* testObj = new ObjectRegistry("TestObj", "TestType", false);
        void* testPtr = testObj;

        // 先註冊對象
        m_registry.Register("key1", testPtr, "Object1", "Test object", "ObjectRegistry");

        // 測試獲取存在的對象
        void* retrievedObj = m_registry.GetObject("key1");
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertNotNull("TestObjectRegistry::TestGetObject - 獲取存在對象", retrievedObj, "應該能獲取到對象"));
            m_runner.RecordResult(Assert::AssertEquals("TestObjectRegistry::TestGetObject - 對象相等", testPtr, retrievedObj, "獲取的對象應該是原對象"));
        }

        // 測試獲取不存在的對象
        void* nonExistentObj = m_registry.GetObject("nonexistent");
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertNull("TestObjectRegistry::TestGetObject - 不存在對象", nonExistentObj, "不存在的對象應該返回NULL"));
        }

        // 測試獲取空鍵
        void* emptyKeyObj = m_registry.GetObject("");
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertNull("TestObjectRegistry::TestGetObject - 空鍵", emptyKeyObj, "空鍵應該返回NULL"));
        }

        // 清理
        delete testObj;
        delete m_registry;
        m_registry = NULL;

        Print("✅ ObjectRegistry 獲取對象測試通過");
    }

    // 測試包含檢查
    void TestContains()
    {
        Print("測試 ObjectRegistry 包含檢查...");

        m_registry = new ObjectRegistry("TestRegistry", "TestType", false);

        // 創建測試對象
        ObjectRegistry* testObj = new ObjectRegistry("TestObj", "TestType", false);
        void* testPtr = testObj;

        // 測試空註冊器
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertFalse("TestObjectRegistry::TestContains - 空註冊器", m_registry.Contains("key1"), "空註冊器不應該包含任何鍵"));
        }

        // 註冊對象
        m_registry.Register("key1", testPtr, "Object1", "Test object", "ObjectRegistry");

        // 測試包含已註冊的鍵
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("TestObjectRegistry::TestContains - 包含已註冊鍵", m_registry.Contains("key1"), "應該包含已註冊的鍵"));
            m_runner.RecordResult(Assert::AssertFalse("TestObjectRegistry::TestContains - 不包含未註冊鍵", m_registry.Contains("key2"), "不應該包含未註冊的鍵"));
            m_runner.RecordResult(Assert::AssertFalse("TestObjectRegistry::TestContains - 不包含空鍵", m_registry.Contains(""), "不應該包含空鍵"));
        }

        // 清理
        delete testObj;
        delete m_registry;
        m_registry = NULL;

        Print("✅ ObjectRegistry 包含檢查測試通過");
    }
};
