/*
 * NumericIdentifier.mqh
 *
 * 描述: 數字識別系統配置結構、介面和基礎類
 * 版本: 1.0.0
 * 作者: EA_Wizard
 * 日期: 2023-11-03
 */

#property strict

// 防止重複包含
#ifndef _NUMERIC_IDENTIFIER_MQH_
#define _NUMERIC_IDENTIFIER_MQH_

//+------------------------------------------------------------------+
//| 識別系統配置結構                                                   |
//+------------------------------------------------------------------+

// 定義每個部分的配置
struct NumericIdentifierPartConfig
{
    string name;        // 部分名稱
    int digits;         // 數位大小
    int minValue;       // 最小值
    int maxValue;       // 最大值
    int defaultValue;   // 默認值
};

// 默認配置常量
#define DEFAULT_COMPONENTS_COUNT 5
#define MAX_COMPONENTS_COUNT 10
#define MAX_TOTAL_DIGITS 10

//+------------------------------------------------------------------+
//| 數字識別系統介面                                                    |
//+------------------------------------------------------------------+
class INumericIdentifier
{
public:
    // 虛擬析構函數
    virtual ~INumericIdentifier() {};
    // 初始化配置
    virtual bool Initialize(NumericIdentifierPartConfig &components[], int count) = 0;

    // 獲取部分值
    virtual int GetComponentValue(int identifier, int componentIndex) = 0;

    // 獲取部分數量
    virtual int GetComponentsCount() = 0;

    // 獲取部分配置
    virtual NumericIdentifierPartConfig GetComponentConfig(int componentIndex) = 0;

    // 設置部分配置
    virtual bool SetComponentConfig(int componentIndex, NumericIdentifierPartConfig &config) = 0;

    // 添加新部分
    virtual bool AddComponent(NumericIdentifierPartConfig &config) = 0;

    // 移除部分
    virtual bool RemoveComponent(int componentIndex) = 0;

    // 檢查配置是否有效
    virtual bool ValidateConfig() = 0;

    // 計算部分的位置
    virtual int CalculatePosition(int componentIndex) = 0;

    // 檢查是否已初始化
    virtual bool IsInitialized() = 0;

    // 生成識別碼
    virtual int GenerateIdentifier(int &values[]) = 0;

    // 解析識別碼
    virtual void ParseIdentifier(int identifier, int &values[]) = 0;
};

//+------------------------------------------------------------------+
//| 數字識別系統基礎類                                                  |
//+------------------------------------------------------------------+
class NumericIdentifierBase : public INumericIdentifier
{
protected:
    // 靈活配置
    NumericIdentifierPartConfig m_components[];  // 部分配置數組
    int m_componentsCount;                       // 部分數量
    int m_defaultValues[];                       // 默認值數組
    bool m_isInitialized;                        // 是否已初始化

public:
    // 構造函數
    NumericIdentifierBase()
    {
        m_componentsCount = 0;
        m_isInitialized = false;
    }

    // 計算部分的位置
    int CalculatePosition(int componentIndex)
    {
        int position = 1;
        for(int i = componentIndex + 1; i < m_componentsCount; i++)
        {
            position *= (int)MathPow(10, m_components[i].digits);
        }
        return position;
    }

    // 檢查配置是否有效
    bool ValidateConfig()
    {
        // 檢查部分數量
        if(m_componentsCount <= 0 || m_componentsCount > MAX_COMPONENTS_COUNT)
            return false;

        // 檢查總數位數
        int totalDigits = 0;
        for(int i = 0; i < m_componentsCount; i++)
        {
            totalDigits += m_components[i].digits;
        }

        if(totalDigits > MAX_TOTAL_DIGITS)
            return false;

        // 檢查每個部分的配置
        for(int i = 0; i < m_componentsCount; i++)
        {
            // 檢查數位大小
            if(m_components[i].digits <= 0)
                return false;

            // 檢查最小值和最大值
            if(m_components[i].minValue > m_components[i].maxValue)
                return false;

            // 檢查默認值
            if(m_components[i].defaultValue < m_components[i].minValue ||
               m_components[i].defaultValue > m_components[i].maxValue)
                return false;
        }

        return true;
    }

    // 初始化配置
    bool Initialize(NumericIdentifierPartConfig &components[], int count)
    {
        // 檢查參數
        if(count <= 0 || count > MAX_COMPONENTS_COUNT)
            return false;

        // 複製配置
        m_componentsCount = count;
        ArrayResize(m_components, count);
        ArrayResize(m_defaultValues, count);

        for(int i = 0; i < count; i++)
        {
            m_components[i] = components[i];
            m_defaultValues[i] = components[i].defaultValue;
        }

        // 驗證配置
        if(!ValidateConfig())
        {
            m_componentsCount = 0;
            return false;
        }

        m_isInitialized = true;
        return true;
    }

    // 獲取部分值
    int GetComponentValue(int identifier, int componentIndex)
    {
        // 檢查是否已初始化
        if(!m_isInitialized)
            return 0;

        // 檢查索引是否有效
        if(componentIndex < 0 || componentIndex >= m_componentsCount)
            return 0;

        // 計算部分的位置
        int position = CalculatePosition(componentIndex);

        // 計算部分的值
        int value = (identifier / position) % (int)MathPow(10, m_components[componentIndex].digits);

        return value;
    }

    /**
     * 獲取部分數量
     * @return 部分數量
     */
    int GetComponentsCount()
    {
        return m_componentsCount;
    }

    /**
     * 獲取部分配置
     * @param componentIndex 部分索引
     * @return 部分配置
     */
    NumericIdentifierPartConfig GetComponentConfig(int componentIndex)
    {
        NumericIdentifierPartConfig config;

        // 檢查是否已初始化
        if(!m_isInitialized)
            return config;

        // 檢查索引是否有效
        if(componentIndex < 0 || componentIndex >= m_componentsCount)
            return config;

        return m_components[componentIndex];
    }

    /**
     * 設置部分配置
     * @param componentIndex 部分索引
     * @param config 部分配置
     * @return 是否成功
     */
    bool SetComponentConfig(int componentIndex, NumericIdentifierPartConfig &config)
    {
        // 檢查是否已初始化
        if(!m_isInitialized)
            return false;

        // 檢查索引是否有效
        if(componentIndex < 0 || componentIndex >= m_componentsCount)
            return false;

        // 更新配置
        m_components[componentIndex] = config;
        m_defaultValues[componentIndex] = config.defaultValue;

        // 驗證配置
        return ValidateConfig();
    }

    /**
     * 添加新部分
     * @param config 部分配置
     * @return 是否成功
     */
    bool AddComponent(NumericIdentifierPartConfig &config)
    {
        // 檢查是否已初始化
        if(!m_isInitialized)
            return false;

        // 檢查是否已達到最大部分數量
        if(m_componentsCount >= MAX_COMPONENTS_COUNT)
            return false;

        // 添加新部分
        m_componentsCount++;
        ArrayResize(m_components, m_componentsCount);
        ArrayResize(m_defaultValues, m_componentsCount);

        m_components[m_componentsCount - 1] = config;
        m_defaultValues[m_componentsCount - 1] = config.defaultValue;

        // 驗證配置
        return ValidateConfig();
    }

    /**
     * 移除部分
     * @param componentIndex 部分索引
     * @return 是否成功
     */
    bool RemoveComponent(int componentIndex)
    {
        // 檢查是否已初始化
        if(!m_isInitialized)
            return false;

        // 檢查索引是否有效
        if(componentIndex < 0 || componentIndex >= m_componentsCount)
            return false;

        // 檢查是否至少有一個部分
        if(m_componentsCount <= 1)
            return false;

        // 移除部分
        for(int i = componentIndex; i < m_componentsCount - 1; i++)
        {
            m_components[i] = m_components[i + 1];
            m_defaultValues[i] = m_defaultValues[i + 1];
        }

        m_componentsCount--;
        ArrayResize(m_components, m_componentsCount);
        ArrayResize(m_defaultValues, m_componentsCount);

        // 驗證配置
        return ValidateConfig();
    }

    /**
     * 檢查是否已初始化
     * @return 是否已初始化
     */
    bool IsInitialized()
    {
        return m_isInitialized;
    }

    /**
     * 生成識別碼
     * @param values 各部分的值數組
     * @return 生成的識別碼
     */
    int GenerateIdentifier(int &values[])
    {
        // 檢查是否已初始化
        if(!m_isInitialized)
            return 0;

        int identifier = 0;

        // 生成識別碼
        for(int i = 0; i < m_componentsCount; i++)
        {
            // 確保值在有效範圍內
            int value = MathMax(m_components[i].minValue, MathMin(values[i], m_components[i].maxValue));

            // 計算部分的位置
            int position = CalculatePosition(i);

            // 添加部分的值
            identifier += value * position;
        }

        return identifier;
    }

    /**
     * 解析識別碼
     * @param identifier 識別碼
     * @param values 輸出參數，各部分的值數組
     */
    void ParseIdentifier(int identifier, int &values[])
    {
        // 檢查是否已初始化
        if(!m_isInitialized)
            return;

        // 調整數組大小
        ArrayResize(values, m_componentsCount);

        // 解析識別碼
        for(int i = 0; i < m_componentsCount; i++)
        {
            // 計算部分的位置
            int position = CalculatePosition(i);

            // 計算部分的值
            int value = (identifier / position) % (int)MathPow(10, m_components[i].digits);

            // 存儲部分的值
            values[i] = value;
        }
    }
};

#endif // _NUMERIC_IDENTIFIER_MQH_
