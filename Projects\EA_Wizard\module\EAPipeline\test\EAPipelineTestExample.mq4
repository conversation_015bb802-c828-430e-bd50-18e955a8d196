//+------------------------------------------------------------------+
//|                                           EAPipelineTestExample.mq4 |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

// 包含測試套件
#include "RunAllTests.mqh"
#include "TestRunnerWithDocuments.mqh"

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== EAPipeline 測試套件示例 ===");
    
    // 顯示測試套件信息
    ShowEAPipelineTestInfo();
    
    // 根據不同的測試需求選擇執行方式
    
    // 1. 基本測試執行
    Print("\n--- 執行基本測試 ---");
    RunBasicTests();
    
    // 2. 增強版測試執行
    Print("\n--- 執行增強版測試 ---");
    RunAdvancedTests();
    
    // 3. 文檔生成測試
    Print("\n--- 執行文檔生成測試 ---");
    RunDocumentTests();
    
    // 4. 特定場景測試
    Print("\n--- 執行特定場景測試 ---");
    RunSpecificScenarios();
    
    // 5. CI/CD 測試
    Print("\n--- 執行 CI/CD 測試 ---");
    RunCITests();
    
    Print("\n=== EAPipeline 測試套件示例完成 ===");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("EAPipeline 測試示例結束");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 測試示例不需要處理tick事件
}

//+------------------------------------------------------------------+
//| 運行基本測試                                                     |
//+------------------------------------------------------------------+
void RunBasicTests()
{
    Print("開始基本測試...");
    
    // 快速檢查
    bool quickResult = QuickEAPipelineCheck();
    Print(StringFormat("快速檢查結果: %s", quickResult ? "通過" : "失敗"));
    
    if(!quickResult)
    {
        Print("快速檢查失敗，執行完整測試...");
        RunAllEAPipelineTests();
    }
    else
    {
        Print("快速檢查通過，跳過完整測試");
    }
}

//+------------------------------------------------------------------+
//| 運行增強版測試                                                   |
//+------------------------------------------------------------------+
void RunAdvancedTests()
{
    Print("開始增強版測試...");
    
    // 執行增強版測試，包含詳細信息收集
    RunAllEAPipelineTestsAdvanced();
}

//+------------------------------------------------------------------+
//| 運行文檔生成測試                                                 |
//+------------------------------------------------------------------+
void RunDocumentTests()
{
    Print("開始文檔生成測試...");
    
    // 基本文檔生成
    Print("--- 基本文檔生成 ---");
    RunAllEAPipelineTestsWithDocs();
    
    // 分類文檔生成
    Print("--- 分類文檔生成 ---");
    RunEAPipelineUnitTestsWithDocs();
    RunEAPipelineIntegrationTestsWithDocs();
    
    // 自定義文檔選項
    Print("--- 自定義文檔選項 ---");
    RunEAPipelineTestsWithCustomDocs(
        true,                               // 生成完整報告
        true,                               // 生成摘要
        "TestReports\\EAPipeline\\Custom"   // 自定義目錄
    );
}

//+------------------------------------------------------------------+
//| 運行特定場景測試                                                 |
//+------------------------------------------------------------------+
void RunSpecificScenarios()
{
    Print("開始特定場景測試...");
    
    // 測試各個組件
    Print("--- 測試 EACompoundPipeline ---");
    RunSpecificEAPipelineTest("TestEACompoundPipeline");
    
    Print("--- 測試 EAPipelineManager ---");
    RunSpecificEAPipelineTest("TestEAPipelineManager");
    
    Print("--- 測試整合場景 ---");
    RunSpecificEAPipelineTest("TestEAPipelineIntegration");
    
    // 帶文檔的特定測試
    Print("--- 帶文檔的特定測試 ---");
    RunSpecificEAPipelineTestWithDocs("TestEACompoundPipeline");
}

//+------------------------------------------------------------------+
//| 運行 CI/CD 測試                                                  |
//+------------------------------------------------------------------+
void RunCITests()
{
    Print("開始 CI/CD 測試...");
    
    // CI 測試
    Print("--- CI 測試 ---");
    CIEAPipelineTests();
    
    // 帶文檔的 CI 測試
    Print("--- 帶文檔的 CI 測試 ---");
    CIEAPipelineTestsWithDocs();
    
    // 回歸測試
    Print("--- 回歸測試 ---");
    RunEAPipelineRegressionTests();
    
    // 性能測試
    Print("--- 性能測試 ---");
    RunEAPipelinePerformanceTests();
}

//+------------------------------------------------------------------+
//| 開發環境測試示例                                                 |
//+------------------------------------------------------------------+
void DevelopmentTestExample()
{
    Print("=== 開發環境測試示例 ===");
    
    // 開發過程中的快速驗證
    if(!QuickEAPipelineCheck())
    {
        Print("快速檢查失敗，需要詳細調試");
        
        // 運行詳細測試
        RunAllEAPipelineTestsAdvanced();
        
        // 生成調試文檔
        RunEAPipelineTestsWithCustomDocs(
            true,                           // 完整報告
            true,                           // 摘要
            "TestReports\\Debug"            // 調試目錄
        );
    }
    else
    {
        Print("快速檢查通過，代碼狀態良好");
    }
}

//+------------------------------------------------------------------+
//| 生產環境測試示例                                                 |
//+------------------------------------------------------------------+
void ProductionTestExample()
{
    Print("=== 生產環境測試示例 ===");
    
    // 生產環境的完整驗證
    datetime startTime = TimeCurrent();
    
    // 執行完整測試套件
    CIEAPipelineTestsWithDocs();
    
    datetime endTime = TimeCurrent();
    int duration = (int)(endTime - startTime);
    
    Print(StringFormat("生產環境測試完成，耗時: %d秒", duration));
    
    // 如果測試時間過長，發出警告
    if(duration > 120) // 超過2分鐘
    {
        Print("警告: 測試執行時間較長，可能影響生產環境性能");
    }
}

//+------------------------------------------------------------------+
//| 自動化測試示例                                                   |
//+------------------------------------------------------------------+
void AutomatedTestExample()
{
    Print("=== 自動化測試示例 ===");
    
    // 模擬自動化測試流程
    bool testsPassed = true;
    
    // 階段1: 快速檢查
    Print("階段1: 快速檢查");
    if(!QuickEAPipelineCheck())
    {
        Print("快速檢查失敗");
        testsPassed = false;
    }
    
    // 階段2: 單元測試
    if(testsPassed)
    {
        Print("階段2: 單元測試");
        TestRunnerAdvanced* runner = new TestRunnerAdvanced(false);
        RunEAPipelineUnitTests(runner);
        
        if(!runner.AllTestsPassed())
        {
            Print("單元測試失敗");
            testsPassed = false;
        }
        
        delete runner;
    }
    
    // 階段3: 整合測試
    if(testsPassed)
    {
        Print("階段3: 整合測試");
        TestRunnerAdvanced* runner = new TestRunnerAdvanced(false);
        RunEAPipelineIntegrationTests(runner);
        
        if(!runner.AllTestsPassed())
        {
            Print("整合測試失敗");
            testsPassed = false;
        }
        
        delete runner;
    }
    
    // 結果報告
    if(testsPassed)
    {
        Print("自動化測試全部通過");
        
        // 生成成功報告
        RunEAPipelineTestsWithCustomDocs(
            false,                          // 不需要完整報告
            true,                           // 只要摘要
            "TestReports\\Automated\\Success"
        );
    }
    else
    {
        Print("自動化測試失敗，生成詳細報告");
        
        // 生成失敗報告
        RunAllEAPipelineTestsWithDocs();
    }
}

//+------------------------------------------------------------------+
//| 測試配置示例                                                     |
//+------------------------------------------------------------------+
void TestConfigurationExample()
{
    Print("=== 測試配置示例 ===");
    
    // 不同配置的測試執行
    
    // 配置1: 最小化測試（只要結果）
    Print("--- 配置1: 最小化測試 ---");
    RunEAPipelineTestsWithCustomDocs(
        false,                              // 不生成完整報告
        true,                               // 只生成摘要
        "TestReports\\Minimal"
    );
    
    // 配置2: 完整測試（所有信息）
    Print("--- 配置2: 完整測試 ---");
    RunEAPipelineTestsWithCustomDocs(
        true,                               // 生成完整報告
        true,                               // 生成摘要
        "TestReports\\Complete"
    );
    
    // 配置3: 調試模式（詳細信息）
    Print("--- 配置3: 調試模式 ---");
    RunAllEAPipelineTestsAdvanced();
}
