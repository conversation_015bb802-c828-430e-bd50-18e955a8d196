//+------------------------------------------------------------------+
//|                                       EACompoundPipelineStateRunning.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "EACompoundPipelineState.mqh"
#include "EACompoundPipelineStateCompleted.mqh"
#include "EACompoundPipelineStateFailed.mqh"

//+------------------------------------------------------------------+
//| 流水線執行中狀態 - 實現執行中狀態的行為                           |
//+------------------------------------------------------------------+

class EACompoundPipelineStateRunning : public EACompoundPipelineState
{
public:
    // 構造函數
    EACompoundPipelineStateRunning()
    : EACompoundPipelineState(EA_PIPELINE_STATE_RUNNING, "執行中") {}
    
    // 析構函數
    ~EACompoundPipelineStateRunning() {}
    
    // 執行流水線
    void Execute(EACompoundPipeline_A* pipeline) override;
    
    // 重置流水線狀態
    void Restore(EACompoundPipeline_A* pipeline) override;
};


void EACompoundPipelineStateRunning::Execute(EACompoundPipeline_A* pipeline)
{
    // 執行中狀態下不應該再次執行
    Print("警告: 流水線 '", pipeline.GetName(), "' 已經在執行中，不能重複執行");
}


void EACompoundPipelineStateRunning::Restore(EACompoundPipeline_A* pipeline)
{
    // 將狀態轉換為待執行
    pipeline.SetState(new EACompoundPipelineStatePending());
    pipeline.GetComposite().Restore();
}
