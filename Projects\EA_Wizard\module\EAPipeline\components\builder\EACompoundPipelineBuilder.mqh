#property strict

#include "../factory/EACompoundPipelineFactory.mqh" // 包含裝飾者工廠類型

class EACompoundPipelineTemplateBuilder
{
protected:
    EACompoundPipelineDecoratorFactory* m_decorators[];

    EACompoundPipeline* m_pipeline;

public:
    EACompoundPipelineTemplateBuilder()
    {
        Reset();
    }
    
    virtual void Reset() {
        m_pipeline = NULL;
        ArrayResize(m_decorators, 0);
    }

    virtual void AddDecorator(EACompoundPipelineDecoratorFactory* factory)
    {
        ArrayResize(m_decorators, ArraySize(m_decorators) + 1);
        m_decorators[ArraySize(m_decorators) - 1] = factory;
    }
    
    virtual EACompoundPipeline* Build() = 0;
};

class EACompoundPipelineBuilder : public EACompoundPipelineTemplateBuilder
{
    private:
        string m_name;
        string m_type;
        int m_maxItems;

    public:
        EACompoundPipelineBuilder()
        {
            Reset();
        }

        virtual void Reset() override
        {
            EACompoundPipelineTemplateBuilder::Reset();
            m_name = "EACompoundPipeline";
            m_type = "EACompoundPipeline";
            m_maxItems = 100;
        }

        void SetName(string name)
        {
            m_name = name;
        }

        void SetType(string type)
        {
            m_type = type;
        }

        void SetMaxItems(int maxItems = 100)
        {
            m_maxItems = maxItems;
        }

        virtual EACompoundPipeline* Build() override
        {
            if(m_pipeline != NULL) {
                return m_pipeline;
            }

            m_pipeline = new EACompoundPipeline(m_name, m_type, m_maxItems);
            for(int i = 0; i < ArraySize(m_decorators); i++) {
                m_pipeline = m_decorators[i].CreateCompundPipeline(m_pipeline);
            }
            return m_pipeline;
        }
};

class EACompoundPipelineAssistingBuilder : public EACompoundPipelineTemplateBuilder
{
    private:
        EACompoundPipeline* m_original_pipeline;
        
    public:
        EACompoundPipelineAssistingBuilder()
        {
            Reset();
        }

        virtual void Reset() override
        {
            EACompoundPipelineTemplateBuilder::Reset();
            m_original_pipeline = NULL;
        }

        void SetOriginalPipeline(EACompoundPipeline* pipeline)
        {
            m_original_pipeline = pipeline;
        }

        virtual EACompoundPipeline* Build() override
        {
            if(m_pipeline != NULL) {
                return m_pipeline;
            }

            m_pipeline = m_original_pipeline;
            for(int i = 0; i < ArraySize(m_decorators); i++) {
                m_pipeline = m_decorators[i].CreateCompundPipeline(m_pipeline);
            }
            return m_pipeline;
        }
};