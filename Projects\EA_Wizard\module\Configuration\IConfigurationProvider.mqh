//+------------------------------------------------------------------+
//| Module: Configuration/IConfigurationProvider.mqh                 |
//| This file is part of the EA_Wizard project.                      |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

// 防止重複包含
#ifndef _ICONFIGURATION_PROVIDER_MQH_
#define _ICONFIGURATION_PROVIDER_MQH_

#include "ConfigurationTypes.mqh"
#include "../mql4-lib-master/Format/Json.mqh"

//+------------------------------------------------------------------+
//| 配置提供者接口                                                    |
//+------------------------------------------------------------------+
interface IConfigurationProvider
{
public:
    // 基本類型獲取方法
    virtual string getString(string key, string defaultValue="") = 0;
    virtual double getNumber(string key, double defaultValue=0.0) = 0;
    virtual bool getBoolean(string key, bool defaultValue=false) = 0;
    
    // 複雜類型獲取方法
    virtual JsonArray* getArray(string key) = 0;
    virtual JsonObject* getObject(string key) = 0;
    
    // 工具方法
    virtual bool hasKey(string key) = 0;
    virtual ENUM_CONFIG_VALUE_TYPE getValueType(string key) = 0;
};

#endif // _ICONFIGURATION_PROVIDER_MQH_
