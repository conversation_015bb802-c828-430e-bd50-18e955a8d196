# 配置模組 (Configuration Module)

這是一個用於 EA_Wizard 專案的配置模組，提供了一套完整的 JSON 配置文件讀取和管理工具。此模組允許 EA 從 JSON 文件中讀取配置，並提供類型安全的訪問方法，使得 EA 的配置更加靈活和可維護。

## 功能特點

- 支持從 JSON 文件讀取配置
- 提供類型安全的配置訪問方法
- 支持配置驗證和默認值
- 支持配置架構定義和驗證
- 支持配置的動態更新和保存
- 使用單例模式管理配置實例
- 自動創建默認配置文件（如果不存在）
- 支持嵌套對象和數組
- 提供錯誤處理和日誌記錄

## 文件結構

- `ConfigurationTypes.mqh` - 配置相關的枚舉、常量和錯誤處理
- `IConfigurationProvider.mqh` - 配置提供者接口
- `JsonConfigurationManager.mqh` - JSON 配置管理器（主要實現類）
- `ConfigurationReader.mqh` - 配置文件讀取和寫入工具
- `ConfigurationValidator.mqh` - 配置驗證工具
- `ConfigurationSchema.mqh` - 配置架構定義
- `JsonHelper.mqh` - JSON 對象訪問輔助工具
- `JsonExtensions.mqh` - JSON 序列化擴展
- `ConfigurationExample.mqh` - 使用示例
- `ConfigurationTest.mq4` - 測試腳本

## 架構設計

本模組採用了以下設計模式：

1. **單例模式**：`JsonConfigurationManager` 使用單例模式確保整個 EA 中只有一個配置管理器實例。

2. **接口模式**：通過 `IConfigurationProvider` 接口定義配置提供者的行為，使得未來可以輕鬆擴展支持其他格式的配置。

3. **工廠模式**：`ConfigurationSchema` 提供了創建默認 EA 配置架構的工廠方法。

4. **策略模式**：配置驗證和訪問使用不同的策略處理不同類型的值。

## 使用方法

### 在 EA 中初始化和使用配置

在 EA 的 `OnInit()` 函數中初始化配置管理器：

```cpp
#include <Projects\EA_Wizard\module\Configuration\JsonConfigurationManager.mqh>

// 全局配置管理器
JsonConfigurationManager* g_config = NULL;

int OnInit()
{
    // 初始化配置管理器
    g_config = JsonConfigurationManager::GetInstance("myea_config.json");

    // 加載配置
    if(!g_config.load())
    {
        Print("配置加載失敗: ", ConfigGetLastError());
        return INIT_FAILED;
    }

    // 訪問配置值並設置 EA 參數
    double takeProfit = g_config.getNumber("takeProfit", 50.0);
    double stopLoss = g_config.getNumber("stopLoss", 30.0);
    bool isEnabled = g_config.getBoolean("enabled", true);
    string symbol = g_config.getString("symbol", Symbol());

    // 使用配置值...

    return INIT_SUCCEEDED;
}
```

### 訪問基本配置值

配置管理器提供了類型安全的方法來訪問不同類型的配置值：

```cpp
// 訪問字符串值（帶默認值）
string eaName = g_config.getString("name", "Default EA");

// 訪問數值（帶默認值）
double takeProfit = g_config.getNumber("takeProfit", 50.0);
double stopLoss = g_config.getNumber("stopLoss", 30.0);

// 訪問布爾值（帶默認值）
bool isEnabled = g_config.getBoolean("enabled", true);

// 檢查配置項是否存在
if(g_config.hasKey("maxOrders"))
{
    int maxOrders = (int)g_config.getNumber("maxOrders", 5);
}
```

### 訪問嵌套配置

配置管理器支持嵌套對象和數組：

```cpp
// 訪問嵌套對象
JsonObject* riskSettings = g_config.getObject("riskManagement");
if(riskSettings != NULL)
{
    // 使用 JsonHelper 類訪問嵌套對象的值
    bool riskEnabled = JsonHelper::getBoolean(riskSettings, "enabled", true);
    double maxRiskPercent = JsonHelper::getNumber(riskSettings, "maxRiskPercent", 2.0);

    Print("風險管理啟用: ", riskEnabled);
    Print("最大風險百分比: ", maxRiskPercent, "%");
}

// 訪問數組
JsonArray* indicators = g_config.getArray("indicators");
if(indicators != NULL)
{
    Print("指標數量: ", indicators.length());

    for(int i = 0; i < indicators.length(); i++)
    {
        JsonObject* indicator = dynamic_cast<JsonObject*>(indicators[i]);
        if(indicator != NULL)
        {
            string name = JsonHelper::getString(indicator, "name", "");
            bool enabled = JsonHelper::getBoolean(indicator, "enabled", false);
            int period = (int)JsonHelper::getNumber(indicator, "period", 14);

            Print("指標 ", i, ": ", name, " (", (enabled ? "啟用" : "禁用"), "), 週期: ", period);
        }
    }
}
```

### 更新和保存配置

配置管理器支持動態更新和保存配置：

```cpp
// 更新基本配置值
g_config.setString("name", "My Updated EA");
g_config.setNumber("takeProfit", 60.0);
g_config.setBoolean("enabled", false);

// 更新嵌套對象
JsonObject* riskSettings = g_config.getObject("riskManagement");
if(riskSettings != NULL)
{
    // 使用 JsonObject 的方法更新嵌套對象的值
    riskSettings.setBoolean("enabled", true);
    riskSettings.setNumber("maxRiskPercent", 3.0);
}

// 保存配置到文件
if(!g_config.save())
{
    Print("配置保存失敗: ", ConfigGetLastError());
}
```

### 重置配置

配置管理器支持重置配置到默認值：

```cpp
// 重置配置到默認值
if(!g_config.reset())
{
    Print("配置重置失敗: ", ConfigGetLastError());
    return;
}

Print("配置已重置為默認值");
```

### 自定義配置架構

可以創建自定義配置架構來定義配置的結構和默認值：

```cpp
// 創建自定義配置架構
ConfigurationSchema* schema = new ConfigurationSchema();

// 添加基本屬性
schema.addString("name", true, "My Custom EA");
schema.addNumber("version", true, 1.0);
schema.addBoolean("enabled", true, true);

// 添加嵌套對象
JsonObject* riskProperties = new JsonObject();
ConfigurationSchema riskSchema;
riskSchema.addBoolean("enabled", true, true);
riskSchema.addNumber("maxRiskPercent", true, 2.0);

schema.addObject("riskManagement", false, riskSchema.getSchema());

// 設置配置架構
g_config.setSchema(schema);

// 重置配置以應用新架構
g_config.reset();
```

### 錯誤處理

配置模組提供了自定義錯誤處理機制：

```cpp
// 加載配置
if(!g_config.load())
{
    int errorCode = ConfigGetLastError();
    string errorMessage = "";

    switch(errorCode)
    {
        case CONFIG_ERROR_FILE_NOT_FOUND:
            errorMessage = "配置文件不存在";
            break;
        case CONFIG_ERROR_FILE_READ:
            errorMessage = "配置文件讀取錯誤";
            break;
        case CONFIG_ERROR_PARSE:
            errorMessage = "配置文件解析錯誤";
            break;
        case CONFIG_ERROR_VALIDATION:
            errorMessage = "配置驗證失敗";
            break;
        default:
            errorMessage = "未知錯誤";
            break;
    }

    Print("配置加載失敗: ", errorMessage, " (", errorCode, ")");
    return INIT_FAILED;
}
```

## 配置文件示例

以下是一個完整的 EA 配置文件示例：

```json
{
  "name": "My EA",
  "version": 1.0,
  "enabled": true,
  "symbol": "EURUSD",
  "timeframe": 15,
  "takeProfit": 50.0,
  "stopLoss": 30.0,
  "riskManagement": {
    "enabled": true,
    "maxRiskPercent": 2.0,
    "useATR": true,
    "atrPeriod": 14,
    "atrMultiplier": 2.0
  },
  "tradingHours": {
    "enabled": true,
    "sessions": [
      {
        "name": "London",
        "startHour": 8,
        "startMinute": 0,
        "endHour": 16,
        "endMinute": 0
      },
      {
        "name": "New York",
        "startHour": 13,
        "startMinute": 30,
        "endHour": 21,
        "endMinute": 0
      }
    ]
  },
  "indicators": [
    {
      "name": "Moving Average",
      "enabled": true,
      "period": 20,
      "method": "SMA",
      "appliedPrice": "PRICE_CLOSE"
    },
    {
      "name": "RSI",
      "enabled": true,
      "period": 14,
      "overbought": 70,
      "oversold": 30
    }
  ],
  "filters": {
    "minSpread": 2.0,
    "maxSpread": 10.0,
    "newsFilter": true,
    "volatilityFilter": {
      "enabled": true,
      "minAtr": 10.0,
      "maxAtr": 100.0
    }
  }
}
```

## 與其他模組的集成

配置模組可以與 EA_Wizard 專案中的其他模組集成：

### 與驗證模組集成

配置模組可以與驗證模組集成，以提供更強大的配置驗證功能：

```cpp
#include <Projects\EA_Wizard\module\Configuration\JsonConfigurationManager.mqh>
#include <Projects\EA_Wizard\module\Validation\ValidationGroup.mqh>
#include <Projects\EA_Wizard\module\Validation\Validators\ValidatorCollection.mqh>

// 創建驗證組
CValidationGroup* validationGroup = new CValidationGroup("ConfigValidation");

// 添加驗證器
validationGroup.AddValidator(new CRangeValidator("takeProfit", 10.0, 200.0));
validationGroup.AddValidator(new CRangeValidator("stopLoss", 10.0, 200.0));
validationGroup.AddValidator(new CRequiredValidator("symbol", "Symbol"));

// 獲取配置值
double takeProfit = g_config.getNumber("takeProfit", 50.0);
double stopLoss = g_config.getNumber("stopLoss", 30.0);
string symbol = g_config.getString("symbol", Symbol());

// 驗證配置值
CValidationResult* result = validationGroup.Validate();
if(!result.IsValid())
{
    Print("配置驗證失敗: ", result.GetErrorMessage());
    return INIT_FAILED;
}
```

### 與流水線模組集成

配置模組可以與流水線模組集成，作為流水線的一個階段：

```cpp
#include <Projects\EA_Wizard\module\Configuration\JsonConfigurationManager.mqh>
#include <Projects\EA_Wizard\module\Pipeline\PipelineManager.mqh>
#include <Projects\EA_Wizard\module\Pipeline\Pipeline.mqh>

// 創建配置加載階段
class ConfigurationStage : public IPipelineStage<ENUM_INIT_RETCODE, void*>
{
private:
    JsonConfigurationManager* m_config;
    string m_filename;

public:
    ConfigurationStage(string filename = "config.json")
        : m_filename(filename), m_config(NULL) {}

    ENUM_INIT_RETCODE Process(void* input)
    {
        // 獲取配置管理器實例
        m_config = JsonConfigurationManager::GetInstance(m_filename);

        // 加載配置
        if(!m_config.load())
        {
            Print("配置加載失敗: ", ConfigGetLastError());
            return INIT_FAILED;
        }

        return INIT_SUCCEEDED;
    }
};

// 在流水線中使用配置階段
PipelineManager* manager = new PipelineManager();
Pipeline<ENUM_INIT_RETCODE, void*>* pipeline = manager.CreatePipeline<ENUM_INIT_RETCODE, void*>("OnInitPipeline");

// 添加配置階段
pipeline.AddStage(new ConfigurationStage("myea_config.json"));

// 執行流水線
ENUM_INIT_RETCODE result = pipeline.Execute(NULL);
```

## 未來擴展

配置模組可以進一步擴展以支持更多功能：

1. **支持更多配置格式**：通過實現 `IConfigurationProvider` 接口，可以添加對 XML、INI 等其他配置格式的支持。

2. **配置加密**：添加對配置文件加密的支持，以保護敏感信息。

3. **配置版本控制**：添加對配置版本控制的支持，以便在配置格式變更時進行自動遷移。

4. **配置 UI**：開發一個圖形界面，用於編輯和管理配置文件。

5. **遠程配置**：支持從遠程服務器加載配置，以便集中管理多個 EA 的配置。

6. **配置監控**：添加對配置文件變更的監控，以便在配置文件變更時自動重新加載。

7. **配置備份**：添加對配置文件的自動備份功能，以便在配置文件損壞時進行恢復。

## 結論

配置模組提供了一套完整的 JSON 配置文件讀取和管理工具，使得 EA 的配置更加靈活和可維護。通過使用此模組，EA 開發者可以輕鬆地從 JSON 文件中讀取配置，並提供類型安全的訪問方法，從而提高 EA 的可配置性和可維護性。
