//+------------------------------------------------------------------+
//|                                              TestDriverAccess.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../TradingPipelineDriver.mqh"

//+------------------------------------------------------------------+
//| 測試驅動器方法訪問                                               |
//+------------------------------------------------------------------+
void TestDriverMethodAccess()
{
    Print("=== 測試 TradingPipelineDriver 方法訪問 ===");
    
    // 獲取驅動器實例
    TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
    
    if(driver == NULL)
    {
        Print("❌ 無法獲取驅動器實例");
        return;
    }
    
    Print("✅ 成功獲取驅動器實例");
    
    // 測試公共方法訪問
    Print("測試公共方法訪問:");
    
    // 測試 GetName
    string name = driver.GetName();
    Print("  GetName(): ", name);
    
    // 測試 GetType
    string type = driver.GetType();
    Print("  GetType(): ", type);
    
    // 測試 IsInitialized
    bool isInit = driver.IsInitialized();
    Print("  IsInitialized(): ", isInit ? "true" : "false");
    
    // 測試 SetupDefaultConfiguration - 這是問題所在
    Print("測試 SetupDefaultConfiguration 方法:");
    bool configResult = driver.SetupDefaultConfiguration();
    Print("  SetupDefaultConfiguration(): ", configResult ? "成功" : "失敗");
    
    // 清理
    TradingPipelineDriver::ReleaseInstance();
    
    Print("=== 測試完成 ===");
}

//+------------------------------------------------------------------+
//| 主函數                                                           |
//+------------------------------------------------------------------+
void OnStart()
{
    TestDriverMethodAccess();
}
