#property strict

#include "../mql4-lib-master/Collection/HashMap.mqh"

#define OBJECT_REGISTRY_DEFAULT_NAME "ObjectRegistry"
#define OBJECT_REGISTRY_DEFAULT_TYPE "ObjectRegistry"

//+------------------------------------------------------------------+
//| ObjectDetail 類 - 對象詳細信息，作為 HashMap 的 Value           |
//| 參考 RegistryItem.mqh 設計                                      |
//+------------------------------------------------------------------+
class ObjectDetail
{
private:
    string m_key;           // 對象鍵
    string m_name;          // 對象名稱
    string m_description;   // 對象描述
    void* m_object;         // 對象指針
    datetime m_createTime;  // 創建時間
    datetime m_updateTime;  // 更新時間
    string m_type;          // 對象類型

public:
    // 構造函數
    ObjectDetail(const string key, const string name, const string description,
                void* object, const string type = "")
        : m_key(key), m_name(name), m_description(description),
          m_object(object), m_type(type)
    {
        m_createTime = TimeCurrent();
        m_updateTime = m_createTime;
    }

    // 析構函數
    ~ObjectDetail()
    {
        // 不釋放對象指針，由註冊器的 owned 標誌決定
    }

    // 獲取對象鍵
    string GetKey() const { return m_key; }

    // 獲取對象名稱
    string GetName() const { return m_name; }

    // 獲取對象描述
    string GetDescription() const { return m_description; }

    // 獲取對象指針
    void* GetObject() const { return m_object; }

    // 獲取創建時間
    datetime GetCreateTime() const { return m_createTime; }

    // 獲取更新時間
    datetime GetUpdateTime() const { return m_updateTime; }

    // 獲取對象類型
    string GetType() const { return m_type; }

    // 設置對象名稱
    void SetName(const string name) { m_name = name; }

    // 設置對象描述
    void SetDescription(const string description) { m_description = description; }

    // 設置對象類型
    void SetType(const string type) { m_type = type; }

    // 更新對象指針
    void UpdateObject(void* object)
    {
        m_object = object;
        m_updateTime = TimeCurrent();
    }

    // 轉換為字符串（用於調試）
    string ToString() const
    {
        return StringFormat("Key: %s, Name: %s, Description: %s, Type: %s, CreateTime: %s, UpdateTime: %s",
                           m_key, m_name, m_description, m_type,
                           TimeToString(m_createTime), TimeToString(m_updateTime));
    }
};

//+------------------------------------------------------------------+
//| ObjectResult 類 - 對象操作結果                                  |
//| 參考 RegistryResult.mqh 設計                                    |
//+------------------------------------------------------------------+
class ObjectResult
{
private:
    bool m_success;         // 操作是否成功
    string m_message;       // 結果消息
    string m_key;           // 操作的鍵
    string m_source;        // 結果來源
    datetime m_timestamp;   // 操作時間戳

public:
    // 構造函數
    ObjectResult(bool success, const string message, const string key, const string source = "ObjectRegistry")
        : m_success(success), m_message(message), m_key(key), m_source(source)
    {
        m_timestamp = TimeCurrent();
    }

    // 析構函數
    ~ObjectResult() {}

    // 檢查是否成功
    bool IsSuccess() const { return m_success; }

    // 獲取消息
    string GetMessage() const { return m_message; }

    // 獲取鍵
    string GetKey() const { return m_key; }

    // 獲取來源
    string GetSource() const { return m_source; }

    // 獲取時間戳
    datetime GetTimestamp() const { return m_timestamp; }

    // 轉換為字符串
    string ToString() const
    {
        return StringFormat("Success: %s, Message: %s, Key: %s, Source: %s, Time: %s",
                            m_success ? "true" : "false", m_message, m_key, m_source,
                            TimeToString(m_timestamp));
    }
};

//+------------------------------------------------------------------+
//| ObjectRegistry 類 - 以 HashMap 作為核心成員的對象註冊器         |
//| 核心方法：Register 和 Unregister                                |
//+------------------------------------------------------------------+
class ObjectRegistry
{
private:
    HashMap<string, ObjectDetail*> m_objects;   // 核心 HashMap 成員實體，存儲 ObjectDetail
    string m_name;                              // 註冊器名稱
    string m_type;                              // 註冊器類型
    bool m_owned;                               // 是否擁有對象的所有權
    string m_lastRegisteredKey;                 // 最後註冊的鍵
    ObjectResult* m_last_result;                // 最後操作結果

public:
    // 構造函數
    ObjectRegistry(string name = OBJECT_REGISTRY_DEFAULT_NAME,
                   string type = OBJECT_REGISTRY_DEFAULT_TYPE,
                   bool owned = false)
        : m_objects(NULL, true), // 總是擁有 ObjectDetail 的所有權
          m_name(name),
          m_type(type),
          m_owned(owned),
          m_lastRegisteredKey(""),
          m_last_result(NULL)
    {
        // HashMap 已在初始化列表中初始化
    }

    // 析構函數
    virtual ~ObjectRegistry()
    {
        // m_objects 是實體，會自動析構
        // 只需要清理 m_last_result
        if(m_last_result != NULL)
        {
            delete m_last_result;
            m_last_result = NULL;
        }
    }

    // 核心方法：註冊對象
    virtual bool Register(const string key, void* object, const string name = "",
                         const string description = "", const string type = "")
    {
        // 更新最後結果
        if(m_last_result != NULL)
        {
            delete m_last_result;
        }

        if(key == "" || object == NULL)
        {
            m_last_result = new ObjectResult(false, "鍵為空或對象為 NULL", key);
            return false;
        }

        // 檢查是否已存在
        if(m_objects.contains(key))
        {
            m_last_result = new ObjectResult(false, "鍵已存在", key);
            return false;
        }

        // 建立 ObjectDetail
        ObjectDetail* detail = new ObjectDetail(key, name, description, object, type);

        // 註冊對象
        m_objects.set(key, detail);
        m_lastRegisteredKey = key;

        m_last_result = new ObjectResult(true, "對象註冊成功", key);
        return true;
    }

    // 核心方法：移除對象
    virtual bool Unregister(const string key)
    {
        // 更新最後結果
        if(m_last_result != NULL)
        {
            delete m_last_result;
        }

        if(key == "")
        {
            m_last_result = new ObjectResult(false, "鍵為空", key);
            return false;
        }

        // 檢查是否存在
        if(!m_objects.contains(key))
        {
            m_last_result = new ObjectResult(false, "鍵不存在", key);
            return false;
        }

        // 如果擁有對象所有權，需要先刪除對象
        if(m_owned)
        {
            ObjectDetail* detail = m_objects.get(key, NULL);
            if(detail != NULL && detail.GetObject() != NULL)
            {
                delete detail.GetObject();
            }
        }

        // 移除對象
        bool result = m_objects.remove(key);
        if(result)
        {
            m_last_result = new ObjectResult(true, "對象移除成功", key);
        }
        else
        {
            m_last_result = new ObjectResult(false, "對象移除失敗", key);
        }

        return result;
    }

    // 獲取對象
    virtual void* GetObject(const string key)
    {
        if(key == "")
        {
            return NULL;
        }

        if(!m_objects.contains(key))
        {
            return NULL;
        }

        ObjectDetail* detail = m_objects.get(key, NULL);
        if(detail != NULL)
        {
            return detail.GetObject();
        }

        return NULL;
    }

    // 獲取對象詳細信息
    virtual ObjectDetail* GetObjectDetail(const string key)
    {
        if(key == "")
        {
            return NULL;
        }

        if(!m_objects.contains(key))
        {
            return NULL;
        }

        return m_objects.get(key, NULL);
    }

    // 檢查是否包含指定鍵
    virtual bool Contains(const string key)
    {
        return m_objects.contains(key);
    }

    // 獲取對象數量
    virtual int GetCount()
    {
        return m_objects.size();
    }

    // 清空所有對象
    virtual void Clear()
    {
        m_objects.clear();
        m_lastRegisteredKey = "";
    }

    // 獲取所有鍵
    virtual int GetAllKeys(string &keys[])
    {
        int count = m_objects.size();
        if(count == 0)
        {
            ArrayResize(keys, 0);
            return 0;
        }

        ArrayResize(keys, count);

        // 使用迭代器獲取所有鍵
        MapIterator<string, ObjectDetail*>* iterator = m_objects.iterator();
        int index = 0;

        while(!iterator.end() && index < count)
        {
            keys[index] = iterator.key();
            iterator.next();
            index++;
        }

        delete iterator;
        return index;
    }

    // 獲取註冊器名稱
    virtual string GetName()
    {
        return m_name;
    }

    // 獲取註冊器類型
    virtual string GetType()
    {
        return m_type;
    }

    // 檢查是否為空
    virtual bool IsEmpty()
    {
        return m_objects.isEmpty();
    }

    // 獲取最後註冊的鍵
    virtual string GetLastRegisteredKey()
    {
        return m_lastRegisteredKey;
    }

    // 檢查是否擁有對象所有權
    virtual bool IsOwned()
    {
        return m_owned;
    }

    // 設置註冊器名稱
    virtual void SetName(const string name)
    {
        m_name = name;
    }

    // 設置註冊器類型
    virtual void SetType(const string type)
    {
        m_type = type;
    }

    // 更新對象（如果鍵存在）
    virtual bool UpdateObject(const string key, void* object)
    {
        // 更新最後結果
        if(m_last_result != NULL)
        {
            delete m_last_result;
        }

        if(key == "" || object == NULL)
        {
            m_last_result = new ObjectResult(false, "鍵為空或對象為 NULL", key);
            return false;
        }

        // 檢查是否存在
        if(!m_objects.contains(key))
        {
            m_last_result = new ObjectResult(false, "鍵不存在", key);
            return false;
        }

        // 獲取現有的 ObjectDetail 並更新對象
        ObjectDetail* detail = m_objects.get(key, NULL);
        if(detail != NULL)
        {
            // 如果擁有對象所有權，先刪除舊對象
            if(m_owned && detail.GetObject() != NULL)
            {
                delete detail.GetObject();
            }
            detail.UpdateObject(object);
            m_last_result = new ObjectResult(true, "對象更新成功", key);
            return true;
        }

        m_last_result = new ObjectResult(false, "無法獲取對象詳細信息", key);
        return false;
    }

    // 註冊或更新對象
    virtual bool RegisterOrUpdate(const string key, void* object, const string name = "",
                                 const string description = "", const string type = "")
    {
        // 更新最後結果
        if(m_last_result != NULL)
        {
            delete m_last_result;
        }

        if(key == "" || object == NULL)
        {
            m_last_result = new ObjectResult(false, "鍵為空或對象為 NULL", key);
            return false;
        }

        // 如果存在則更新，不存在則新增
        if(m_objects.contains(key))
        {
            return UpdateObject(key, object);
        }
        else
        {
            return Register(key, object, name, description, type);
        }
    }

    // 獲取 HashMap 實例（用於高級操作）
    virtual HashMap<string, ObjectDetail*>* GetHashMap()
    {
        return GetPointer(m_objects);
    }

    // 獲取最後操作結果
    virtual ObjectResult* GetLastResult()
    {
        return m_last_result;
    }
};
