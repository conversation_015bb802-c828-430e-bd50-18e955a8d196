//+------------------------------------------------------------------+
//|                                     EACompoundPipelineStateCompleted.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "EACompoundPipelineState.mqh"
#include "EACompoundPipelineStatePending.mqh"

//+------------------------------------------------------------------+
//| 流水線已完成狀態 - 實現已完成狀態的行為                           |
//+------------------------------------------------------------------+

class EACompoundPipelineStateCompleted : public EACompoundPipelineState
{
public:
    // 構造函數
    EACompoundPipelineStateCompleted()
    : EACompoundPipelineState(EA_PIPELINE_STATE_COMPLETED, "已完成") {}
    
    // 析構函數
    ~EACompoundPipelineStateCompleted() {}
    
    // 執行流水線
    void Execute(EACompoundPipeline_A* pipeline) override;
    
    // 重置流水線狀態
    void Restore(EACompoundPipeline_A* pipeline) override;
};


void EACompoundPipelineStateCompleted::Execute(EACompoundPipeline_A* pipeline)
{
    // 已完成狀態下不應該再次執行
    Print("警告: 流水線 '", pipeline.GetName(), "' 已經完成執行，不能重複執行");
}


void EACompoundPipelineStateCompleted::Restore(EACompoundPipeline_A* pipeline)
{
    // 將狀態轉換為待執行
    pipeline.SetState(new EACompoundPipelineStatePending());
    pipeline.GetComposite().Restore();
}
