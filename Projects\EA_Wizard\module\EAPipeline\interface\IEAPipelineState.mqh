//+------------------------------------------------------------------+
//|                                               IEAPipelineState.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../enum/EAPipelineStateEnum.mqh"

//+------------------------------------------------------------------+
//| 流水線狀態介面 - 定義流水線狀態的基本行為                         |
//+------------------------------------------------------------------+
template <typename EAPipelineObj>
interface IEAPipelineState
{
public:
    // 執行流水線
    void Execute(EAPipelineObj pipeline);
    
    // 重置流水線狀態
    void Restore(EAPipelineObj pipeline);
    
    // 獲取狀態類型
    ENUM_EA_PIPELINE_STATE GetState();
    
    // 獲取狀態描述
    string GetStateDescription();
};
