#property strict

#include "../EAPipelineBase.mqh"
#include "../EAFileLog.mqh"

//+------------------------------------------------------------------+
//| EALogPipeline 類 - 日誌裝飾者模式實現的 EAPipelineBase           |
//+------------------------------------------------------------------+
class EALogPipeline : public EAPipelineBase
{
private:
    EAFileLog* m_logger;  // 日誌記錄器

public:
    // 構造函數
    EALogPipeline(Pipeline* pipeline, string type = "EALogPipeline", EAFileLog* logger = NULL)
        : EAPipelineBase(pipeline, type),
          m_logger(logger ? logger : EAFileLog::GetInstance())
    {
    }
    
    EALogPipeline(EAPipelineBase* pipeline, string type = "EALogPipeline", EAFileLog* logger = NULL)
        : EAPipelineBase(pipeline, type),
          m_logger(logger ? logger : EAFileLog::GetInstance())
    {
        EAPipelineBase::SetPipeline(pipeline.GetPipeline());
    }
    
    // 析構函數
    virtual ~EALogPipeline()
    {
        // 不需要刪除 m_logger，因為它是單例
    }

    // 執行流水線
    virtual void Execute() override
    {
        m_logger.Log(DEBUG, "開始執行 EALogPipeline: " + GetName());
        EAPipelineBase::Execute();
        
        // 獲取執行結果並記錄
        PipelineResult* result = GetResult();
        if(result != NULL)
        {
            if(result.IsSuccess())
            {
                m_logger.Log(DEBUG, "EALogPipeline 執行成功: " + GetName() + ", 訊息: " + result.GetMessage());
            }
            else
            {
                m_logger.Log(WARNING, "EALogPipeline 執行失敗: " + GetName() + ", 訊息: " + result.GetMessage());
            }
        }
        
        m_logger.Log(DEBUG, "完成執行 EALogPipeline: " + GetName());
    }

    // 獲取流水線名稱
    virtual string GetName() override
    {
        return EAPipelineBase::GetName();
    }

    // 獲取流水線類型
    virtual string GetType() override
    {
        return EAPipelineBase::GetType();
    }

    // 獲取流水線執行結果
    virtual PipelineResult* GetResult() override
    {
        return EAPipelineBase::GetResult();
    }

    // 重置流水線狀態
    virtual void Restore() override
    {
        m_logger.Log(DEBUG, "重置 EALogPipeline: " + GetName());
        EAPipelineBase::Restore();
    }

    // 檢查流水線是否已執行
    virtual bool IsExecuted() override
    {
        return EAPipelineBase::IsExecuted();
    }

    // 獲取被裝飾的流水線
    virtual Pipeline* GetPipeline() override
    {
        return EAPipelineBase::GetPipeline();
    }

protected:
    // 設置被裝飾的流水線
    virtual void SetPipeline(Pipeline* pipeline) override
    {
        m_logger.Log(INFO, "設置 EALogPipeline 的流水線: " + GetName());
        EAPipelineBase::SetPipeline(pipeline);
    }
};
