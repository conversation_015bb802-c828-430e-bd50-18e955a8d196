//+------------------------------------------------------------------+
//|                                     TestDisplayOptionsQuick.mq4 |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "RunAllTests.mqh"

//+------------------------------------------------------------------+
//| 快速測試通過測試顯示選項                                         |
//| 這個腳本快速驗證新的顯示選項功能                                 |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 腳本程序開始函數                                                 |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("╔══════════════════════════════════════════════════════════════╗");
    Print("║           快速測試通過測試顯示選項                           ║");
    Print("║                    EA_Wizard                                 ║");
    Print("╚══════════════════════════════════════════════════════════════╝");
    Print("");
    
    // 快速測試不同的顯示選項
    QuickTestLimitedDisplay();
    Print("");
    
    QuickTestUnlimitedDisplay();
    Print("");
    
    QuickTestAdvancedOptions();
    
    Print("");
    Print("╔══════════════════════════════════════════════════════════════╗");
    Print("║                    快速測試完成                              ║");
    Print("║     新的顯示選項功能正常工作                                 ║");
    Print("╚══════════════════════════════════════════════════════════════╝");
}

//+------------------------------------------------------------------+
//| 快速測試限制顯示                                                 |
//+------------------------------------------------------------------+
void QuickTestLimitedDisplay()
{
    Print("🧪 快速測試 1: 限制顯示選項");
    Print("────────────────────────────────────────────────────────────");
    
    Print("正在測試限制顯示前3個通過的測試...");
    RunPipelineAdvanceTestsWithLimitedDisplay(3, "QuickTest_Limited");
    
    Print("✅ 限制顯示測試完成");
    Print("📁 文檔位置: MQL4\\Files\\QuickTest_Limited\\");
}

//+------------------------------------------------------------------+
//| 快速測試無限制顯示                                               |
//+------------------------------------------------------------------+
void QuickTestUnlimitedDisplay()
{
    Print("🧪 快速測試 2: 無限制顯示選項");
    Print("────────────────────────────────────────────────────────────");
    
    Print("正在測試無限制顯示所有通過的測試...");
    RunPipelineAdvanceTestsWithUnlimitedDisplay("QuickTest_Unlimited");
    
    Print("✅ 無限制顯示測試完成");
    Print("📁 文檔位置: MQL4\\Files\\QuickTest_Unlimited\\");
}

//+------------------------------------------------------------------+
//| 快速測試高級選項                                                 |
//+------------------------------------------------------------------+
void QuickTestAdvancedOptions()
{
    Print("🧪 快速測試 3: 高級自定義選項");
    Print("────────────────────────────────────────────────────────────");
    
    Print("正在測試高級自定義選項（顯示前5個）...");
    RunPipelineAdvanceTestsWithAdvancedDocs(
        true,                           // 生成完整報告
        true,                           // 生成摘要
        "QuickTest_Advanced",           // 輸出目錄
        5                               // 顯示前5個通過的測試
    );
    
    Print("✅ 高級選項測試完成");
    Print("📁 文檔位置: MQL4\\Files\\QuickTest_Advanced\\");
}

//+------------------------------------------------------------------+
//| 驗證功能特性                                                     |
//+------------------------------------------------------------------+
void VerifyFeatures()
{
    Print("🔍 驗證新功能特性:");
    Print("────────────────────────────────────────────────────────────");
    
    Print("✅ 已實現的功能:");
    Print("   • 可配置的通過測試顯示數量");
    Print("   • 限制顯示選項 (正數、0)");
    Print("   • 無限制顯示選項 (-1)");
    Print("   • 智能提示和省略信息");
    Print("   • 總計信息顯示");
    Print("   • 高級自定義文檔輸出函數");
    Print("");
    
    Print("📋 顯示選項說明:");
    Print("   • 正數 (如 3, 5, 10): 顯示指定數量的通過測試");
    Print("   • -1: 無限制顯示所有通過的測試");
    Print("   • 0: 不顯示通過的測試（只顯示失敗的測試）");
    Print("");
    
    Print("💡 使用建議:");
    Print("   • 開發階段: 使用無限制顯示查看所有詳情");
    Print("   • 日常測試: 使用默認設置或適中限制");
    Print("   • 生產環境: 使用較小限制保持簡潔");
    Print("   • 調試失敗: 使用限制0只關注失敗測試");
}

//+------------------------------------------------------------------+
//| 顯示使用示例                                                     |
//+------------------------------------------------------------------+
void ShowUsageExamples()
{
    Print("📖 使用示例:");
    Print("────────────────────────────────────────────────────────────");
    
    Print("1. 基本使用:");
    Print("   TestPipelineAdvanceWithDocs();  // 默認顯示前10個");
    Print("");
    
    Print("2. 限制顯示:");
    Print("   RunPipelineAdvanceTestsWithLimitedDisplay(5);  // 顯示前5個");
    Print("   RunPipelineAdvanceTestsWithLimitedDisplay(0);  // 不顯示通過的");
    Print("");
    
    Print("3. 無限制顯示:");
    Print("   RunPipelineAdvanceTestsWithUnlimitedDisplay();");
    Print("");
    
    Print("4. 高級自定義:");
    Print("   RunPipelineAdvanceTestsWithAdvancedDocs(true, true, \"MyReports\", 15);");
    Print("");
    
    Print("5. 程式化設置:");
    Print("   PipelineAdvanceTestRunnerWithDocs* runner = new PipelineAdvanceTestRunnerWithDocs();");
    Print("   runner.SetPassedTestsDisplayOptions(20);");
    Print("   runner.SetUnlimitedPassedTestsDisplay();");
    Print("   runner.RunAllTestsWithDocs();");
    Print("   delete runner;");
}

//+------------------------------------------------------------------+
//| 完整功能演示                                                     |
//+------------------------------------------------------------------+
void FullFeatureDemo()
{
    Print("🚀 完整功能演示:");
    Print("────────────────────────────────────────────────────────────");
    
    // 演示所有主要功能
    Print("正在演示所有主要功能...");
    
    // 1. 默認設置
    Print("1. 默認設置 (顯示前10個):");
    TestPipelineAdvanceWithDocs();
    
    // 2. 限制顯示
    Print("2. 限制顯示 (顯示前3個):");
    RunPipelineAdvanceTestsWithLimitedDisplay(3, "Demo_Limited_3");
    
    // 3. 無限制顯示
    Print("3. 無限制顯示:");
    RunPipelineAdvanceTestsWithUnlimitedDisplay("Demo_Unlimited");
    
    // 4. 高級自定義
    Print("4. 高級自定義 (顯示前7個):");
    RunPipelineAdvanceTestsWithAdvancedDocs(true, true, "Demo_Advanced", 7);
    
    // 5. 不顯示通過的測試
    Print("5. 不顯示通過的測試:");
    RunPipelineAdvanceTestsWithLimitedDisplay(0, "Demo_FailuresOnly");
    
    Print("✅ 完整功能演示完成");
    Print("📁 所有演示文檔已生成到相應目錄");
}

//+------------------------------------------------------------------+
//| 性能測試                                                         |
//+------------------------------------------------------------------+
void PerformanceTest()
{
    Print("⚡ 性能測試:");
    Print("────────────────────────────────────────────────────────────");
    
    datetime startTime, endTime;
    
    // 測試限制顯示的性能
    Print("測試限制顯示性能...");
    startTime = TimeCurrent();
    RunPipelineAdvanceTestsWithLimitedDisplay(5, "Perf_Limited");
    endTime = TimeCurrent();
    Print("限制顯示耗時: " + IntegerToString((int)(endTime - startTime)) + " 秒");
    
    // 測試無限制顯示的性能
    Print("測試無限制顯示性能...");
    startTime = TimeCurrent();
    RunPipelineAdvanceTestsWithUnlimitedDisplay("Perf_Unlimited");
    endTime = TimeCurrent();
    Print("無限制顯示耗時: " + IntegerToString((int)(endTime - startTime)) + " 秒");
    
    Print("✅ 性能測試完成");
    Print("💡 提示: 限制顯示通常會稍快一些，特別是在測試數量很多時");
}
