//+------------------------------------------------------------------+
//|                                      TestRunnerWithDocuments.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "TestFramework.mqh"
#include "TestRunnerAdvanced.mqh"
#include "TestDocumentGenerator.mqh"
#include "unit/TestEACompoundPipeline.mqh"
#include "unit/TestEAPipelineManager.mqh"
#include "integration/TestEAPipelineIntegration.mqh"

//+------------------------------------------------------------------+
//| 運行所有EAPipeline測試並生成文檔                                 |
//+------------------------------------------------------------------+
void RunAllEAPipelineTestsWithDocs()
{
    Print("=== 開始執行 EAPipeline 模組測試套件（含文檔生成） ===");
    
    TestRunnerAdvanced* runner = new TestRunnerAdvanced(true);
    TestDocumentGenerator* docGen = new TestDocumentGenerator();
    
    // 運行單元測試
    Print("--- 開始單元測試 ---");
    RunEAPipelineUnitTests(runner);
    
    // 運行整合測試
    Print("--- 開始整合測試 ---");
    RunEAPipelineIntegrationTests(runner);
    
    // 顯示詳細摘要
    runner.ShowDetailedSummary();
    
    // 生成文檔
    Print("--- 開始生成測試文檔 ---");
    if(docGen.GenerateDocuments(runner))
    {
        Print("測試文檔生成成功");
    }
    else
    {
        Print("測試文檔生成失敗");
    }
    
    Print("=== EAPipeline 模組測試套件執行完成（含文檔生成） ===");
    
    delete runner;
    delete docGen;
}

//+------------------------------------------------------------------+
//| 運行EAPipeline單元測試並生成文檔                                 |
//+------------------------------------------------------------------+
void RunEAPipelineUnitTestsWithDocs()
{
    Print("=== 開始執行 EAPipeline 單元測試（含文檔生成） ===");
    
    TestRunnerAdvanced* runner = new TestRunnerAdvanced(true);
    TestDocumentGenerator* docGen = new TestDocumentGenerator("TestReports\\EAPipeline\\Unit");
    
    // 運行單元測試
    RunEAPipelineUnitTests(runner);
    
    // 顯示詳細摘要
    runner.ShowDetailedSummary();
    
    // 生成文檔
    if(docGen.GenerateDocuments(runner))
    {
        Print("單元測試文檔生成成功");
    }
    else
    {
        Print("單元測試文檔生成失敗");
    }
    
    Print("=== EAPipeline 單元測試執行完成（含文檔生成） ===");
    
    delete runner;
    delete docGen;
}

//+------------------------------------------------------------------+
//| 運行EAPipeline整合測試並生成文檔                                 |
//+------------------------------------------------------------------+
void RunEAPipelineIntegrationTestsWithDocs()
{
    Print("=== 開始執行 EAPipeline 整合測試（含文檔生成） ===");
    
    TestRunnerAdvanced* runner = new TestRunnerAdvanced(true);
    TestDocumentGenerator* docGen = new TestDocumentGenerator("TestReports\\EAPipeline\\Integration");
    
    // 運行整合測試
    RunEAPipelineIntegrationTests(runner);
    
    // 顯示詳細摘要
    runner.ShowDetailedSummary();
    
    // 生成文檔
    if(docGen.GenerateDocuments(runner))
    {
        Print("整合測試文檔生成成功");
    }
    else
    {
        Print("整合測試文檔生成失敗");
    }
    
    Print("=== EAPipeline 整合測試執行完成（含文檔生成） ===");
    
    delete runner;
    delete docGen;
}

//+------------------------------------------------------------------+
//| 運行特定的EAPipeline測試並生成文檔                               |
//+------------------------------------------------------------------+
void RunSpecificEAPipelineTestWithDocs(string testName)
{
    Print(StringFormat("=== 開始執行特定測試: %s（含文檔生成） ===", testName));
    
    TestRunnerAdvanced* runner = new TestRunnerAdvanced(true);
    TestDocumentGenerator* docGen = new TestDocumentGenerator(
        StringFormat("TestReports\\EAPipeline\\Specific\\%s", testName));
    
    if(testName == "TestEACompoundPipeline")
    {
        TestEACompoundPipeline* test = new TestEACompoundPipeline(runner);
        runner.RunTestCase(test);
        delete test;
    }
    else if(testName == "TestEAPipelineManager")
    {
        TestEAPipelineManager* test = new TestEAPipelineManager(runner);
        runner.RunTestCase(test);
        delete test;
    }
    else if(testName == "TestEAPipelineIntegration")
    {
        TestEAPipelineIntegration* test = new TestEAPipelineIntegration(runner);
        runner.RunTestCase(test);
        delete test;
    }
    else
    {
        Print(StringFormat("未知的測試名稱: %s", testName));
        Print("可用的測試: TestEACompoundPipeline, TestEAPipelineManager, TestEAPipelineIntegration");
        delete runner;
        delete docGen;
        return;
    }
    
    // 顯示詳細摘要
    runner.ShowDetailedSummary();
    
    // 生成文檔
    if(docGen.GenerateDocuments(runner))
    {
        Print(StringFormat("特定測試 %s 文檔生成成功", testName));
    }
    else
    {
        Print(StringFormat("特定測試 %s 文檔生成失敗", testName));
    }
    
    Print(StringFormat("=== 特定測試 %s 執行完成（含文檔生成） ===", testName));
    
    delete runner;
    delete docGen;
}

//+------------------------------------------------------------------+
//| 運行EAPipeline測試並自定義文檔選項                               |
//+------------------------------------------------------------------+
void RunEAPipelineTestsWithCustomDocs(bool generateFullReport = true, 
                                      bool generateSummary = true,
                                      string outputDirectory = "TestReports\\EAPipeline\\Custom")
{
    Print("=== 開始執行 EAPipeline 測試（自定義文檔選項） ===");
    
    TestRunnerAdvanced* runner = new TestRunnerAdvanced(true);
    TestDocumentGenerator* docGen = new TestDocumentGenerator(outputDirectory, 
                                                             generateFullReport, 
                                                             generateSummary);
    
    // 顯示文檔生成器配置
    docGen.GenerateConfigReport();
    
    // 運行所有測試
    RunEAPipelineUnitTests(runner);
    RunEAPipelineIntegrationTests(runner);
    
    // 顯示詳細摘要
    runner.ShowDetailedSummary();
    
    // 生成文檔
    if(docGen.GenerateDocuments(runner))
    {
        Print("自定義文檔生成成功");
    }
    else
    {
        Print("自定義文檔生成失敗");
    }
    
    Print("=== EAPipeline 測試執行完成（自定義文檔選項） ===");
    
    delete runner;
    delete docGen;
}

//+------------------------------------------------------------------+
//| CI測試並生成文檔                                                 |
//+------------------------------------------------------------------+
void CIEAPipelineTestsWithDocs()
{
    Print("=== 開始 EAPipeline CI 測試（含文檔生成） ===");
    
    TestRunnerAdvanced* runner = new TestRunnerAdvanced(true);
    TestDocumentGenerator* docGen = new TestDocumentGenerator("TestReports\\EAPipeline\\CI");
    
    // 運行所有測試
    RunEAPipelineUnitTests(runner);
    RunEAPipelineIntegrationTests(runner);
    
    // 檢查結果
    bool allPassed = runner.AllTestsPassed();
    
    if(allPassed)
    {
        Print("CI測試通過 - 所有測試都成功");
    }
    else
    {
        Print("CI測試失敗 - 存在失敗的測試");
        runner.ShowFailedTestsDetail();
    }
    
    // 輸出統計信息
    Print(runner.GetTestStatistics());
    
    // 生成CI文檔
    if(docGen.GenerateDocuments(runner))
    {
        Print("CI測試文檔生成成功");
    }
    else
    {
        Print("CI測試文檔生成失敗");
    }
    
    Print("=== EAPipeline CI 測試完成（含文檔生成） ===");
    
    delete runner;
    delete docGen;
}

//+------------------------------------------------------------------+
//| 測試文檔輸出功能示例                                             |
//+------------------------------------------------------------------+
void DocumentOutputExample()
{
    Print("=== EAPipeline 文檔輸出功能示例 ===");
    
    // 示例1: 基本文檔輸出
    Print("--- 示例1: 基本文檔輸出 ---");
    RunAllEAPipelineTestsWithDocs();
    
    // 示例2: 分類測試文檔輸出
    Print("--- 示例2: 分類測試文檔輸出 ---");
    RunEAPipelineUnitTestsWithDocs();
    RunEAPipelineIntegrationTestsWithDocs();
    
    // 示例3: 特定測試類別文檔輸出
    Print("--- 示例3: 特定測試類別文檔輸出 ---");
    RunSpecificEAPipelineTestWithDocs("TestEACompoundPipeline");
    
    // 示例4: 自定義文檔選項
    Print("--- 示例4: 自定義文檔選項 ---");
    RunEAPipelineTestsWithCustomDocs(true, true, "TestReports\\EAPipeline\\Example");
    
    // 示例5: CI文檔輸出
    Print("--- 示例5: CI文檔輸出 ---");
    CIEAPipelineTestsWithDocs();
    
    Print("=== 文檔輸出功能示例完成 ===");
}
