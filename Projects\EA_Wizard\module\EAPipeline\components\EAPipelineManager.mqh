//+------------------------------------------------------------------+
//|                                          EAPipelineManager.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../../PipelineAdvance/Pipeline.mqh"
#include "../../RegistryAdvance/ItemRegistry.mqh"
#include "../model/EAPipelineItem.mqh"

const string EA_PIPELINE_MANAGER_NAME = "EAPipelineManager";
const string EA_PIPELINE_MANAGER_TYPE = "EAPipelineManager";
const int EA_PIPELINE_MANAGER_MAX_PIPELINES = 100;

//+------------------------------------------------------------------+
//| EAPipelineManager 類 - 單例模式實現的流水線管理器                 |
//+------------------------------------------------------------------+
class EAPipelineManager
{
private:
    static EAPipelineManager* m_instance;  // 單例實例
    ItemRegistry<IPipeline*> m_pipelines;  // 流水線集合，使用名稱作為鍵

    // 私有構造函數
    EAPipelineManager()
        : m_pipelines(EA_PIPELINE_MANAGER_NAME, EA_PIPELINE_MANAGER_TYPE, EA_PIPELINE_MANAGER_MAX_PIPELINES)
    {
    }

    // 私有析構函數
    ~EAPipelineManager()
    {
        m_pipelines.Clear();
    }

public:
    // 獲取單例實例
    static EAPipelineManager* GetInstance()
    {
        if(m_instance == NULL)
        {
            m_instance = new EAPipelineManager();
        }
        return m_instance;
    }

    // 添加流水線
    PipelineResult* AddPipeline(IPipeline* pipeline)
    {
        // 檢查流水線是否為空
        if(pipeline == NULL)
        {
            return new PipelineResult(false, "流水線為空", "EAPipelineManager");
        }

        string pipelineName = pipeline.GetName();

        // 使用 Register 方法註冊流水線
        RegistryResult<string>* result = m_pipelines.Register(pipelineName, "Pipeline: " + pipelineName, pipeline);

        // 檢查註冊結果
        if(!result.IsSuccess())
        {
            string errorMessage = result.GetMessage();
            delete result;

            // 根據錯誤消息返回適當的結果
            if(errorMessage == "鍵已存在")
            {
                return new PipelineResult(false, "流水線已存在", "EAPipelineManager");
            }
            else if(errorMessage == "已達到最大項目數量")
            {
                return new PipelineResult(false, "流水線數量已達上限", "EAPipelineManager");
            }
            else
            {
                return new PipelineResult(false, errorMessage, "EAPipelineManager");
            }
        }

        delete result;
        return new PipelineResult(true, "操作成功", "EAPipelineManager");
    }

    // 移除流水線
    PipelineResult* RemovePipeline(IPipeline* pipeline)
    {
        // 檢查流水線是否為空
        if(pipeline == NULL)
        {
            return new PipelineResult(false, "流水線為空", "EAPipelineManager");
        }

        string pipelineName = pipeline.GetName();

        // 使用 Unregister 方法移除流水線
        bool result = m_pipelines.Unregister(pipelineName);

        if(!result)
        {
            return new PipelineResult(false, "流水線不存在", "EAPipelineManager");
        }

        return new PipelineResult(true, "操作成功", "EAPipelineManager");
    }

    // 根據名稱移除流水線
    PipelineResult* RemovePipelineByName(const string name)
    {
        // 使用 Unregister 方法移除流水線
        bool result = m_pipelines.Unregister(name);

        if(!result)
        {
            return new PipelineResult(false, "流水線不存在", "EAPipelineManager");
        }

        return new PipelineResult(true, "操作成功", "EAPipelineManager");
    }

    // 清空流水線管理器
    void Clear()
    {
        m_pipelines.Clear();
    }

    // 獲取流水線數量
    int GetPipelineCount()
    {
        return m_pipelines.GetCount();
    }

    // 獲取最大流水線數量
    int GetMaxPipelines()
    {
        return m_pipelines.GetMaxItems();
    }

    // 根據名稱執行流水線
    PipelineResult* ExecutePipeline(const string name)
    {
        // 獲取流水線
        RegistryItem<IPipeline*>* item = m_pipelines.GetItem(name);

        // 檢查流水線是否存在
        if(item == NULL || item.GetValue() == NULL)
        {
            return new PipelineResult(false, "流水線不存在", "EAPipelineManager");
        }

        // 獲取流水線
        IPipeline* pipeline = item.GetValue();

        // 執行流水線
        pipeline.Execute();

        // 獲取執行結果
        PipelineResult* result = pipeline.GetResult();

        // 檢查結果是否成功
        if(result != NULL && result.IsSuccess())
        {
            return new PipelineResult(true, "流水線執行成功", "EAPipelineManager");
        }
        else
        {
            string message = (result != NULL) ? result.GetMessage() : "流水線執行失敗";
            return new PipelineResult(false, message, "EAPipelineManager");
        }
    }

    // 獲取所有流水線名稱
    int GetAllPipelineNames(string &names[])
    {
        // 使用 GetAllKeys 方法獲取所有鍵
        string keys[];
        int count = m_pipelines.GetAllKeys(keys);

        // 將鍵複製到輸出數組
        ArrayResize(names, count);

        for(int i = 0; i < count; i++)
        {
            names[i] = keys[i];
        }

        return count;
    }

    // 根據名稱獲取流水線
    EAPipelineItem* GetPipelineByName(const string name)
    {
        return new EAPipelineItem(m_pipelines.GetItem(name));
    }

    // 檢查是否包含指定名稱的流水線
    bool Contains(const string name)
    {
        return m_pipelines.HasRegistered(name);
    }
};

// 初始化靜態成員變數
EAPipelineManager* EAPipelineManager::m_instance = NULL;
