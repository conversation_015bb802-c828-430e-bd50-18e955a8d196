//+------------------------------------------------------------------+
//|                                        TestDisplayOptionsDemo.mq4 |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "RunAllTests.mqh"

//+------------------------------------------------------------------+
//| 通過測試顯示選項演示腳本                                         |
//| 這個腳本演示如何配置通過測試的顯示數量                           |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 腳本程序開始函數                                                 |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("╔══════════════════════════════════════════════════════════════╗");
    Print("║           通過測試顯示選項演示                               ║");
    Print("║                    EA_Wizard                                 ║");
    Print("╚══════════════════════════════════════════════════════════════╝");
    Print("");
    
    // 演示不同的顯示選項
    DemoDefaultDisplay();
    Print("");
    
    DemoLimitedDisplay();
    Print("");
    
    DemoUnlimitedDisplay();
    Print("");
    
    DemoAdvancedOptions();
    Print("");
    
    ShowUsageGuide();
    
    Print("");
    Print("╔══════════════════════════════════════════════════════════════╗");
    Print("║                    演示完成                                  ║");
    Print("║     請檢查不同目錄中生成的測試文檔                           ║");
    Print("╚══════════════════════════════════════════════════════════════╝");
}

//+------------------------------------------------------------------+
//| 演示默認顯示選項                                                 |
//+------------------------------------------------------------------+
void DemoDefaultDisplay()
{
    Print("🎯 演示 1: 默認顯示選項 (限制顯示前10個)");
    Print("────────────────────────────────────────────────────────────");
    
    Print("正在運行測試並生成默認顯示文檔...");
    
    // 使用默認設置（顯示前10個通過的測試）
    TestPipelineAdvanceWithDocs();
    
    Print("✅ 默認顯示文檔已生成");
    Print("📁 位置: MQL4\\Files\\TestReports\\");
    Print("📋 顯示: 最多10個通過的測試");
}

//+------------------------------------------------------------------+
//| 演示限制顯示選項                                                 |
//+------------------------------------------------------------------+
void DemoLimitedDisplay()
{
    Print("🎯 演示 2: 限制顯示選項");
    Print("────────────────────────────────────────────────────────────");
    
    Print("正在測試不同的限制顯示選項...");
    
    // 只顯示前3個通過的測試
    Print("  • 限制顯示前3個:");
    RunPipelineAdvanceTestsWithLimitedDisplay(3, "Demo_Limited_3");
    
    Print("");
    
    // 只顯示前1個通過的測試
    Print("  • 限制顯示前1個:");
    RunPipelineAdvanceTestsWithLimitedDisplay(1, "Demo_Limited_1");
    
    Print("");
    
    // 不顯示通過的測試（只顯示失敗的）
    Print("  • 不顯示通過的測試:");
    RunPipelineAdvanceTestsWithLimitedDisplay(0, "Demo_Limited_0");
    
    Print("✅ 限制顯示文檔已生成");
    Print("📁 位置: MQL4\\Files\\Demo_Limited_*\\");
}

//+------------------------------------------------------------------+
//| 演示無限制顯示選項                                               |
//+------------------------------------------------------------------+
void DemoUnlimitedDisplay()
{
    Print("🎯 演示 3: 無限制顯示選項");
    Print("────────────────────────────────────────────────────────────");
    
    Print("正在運行測試並生成無限制顯示文檔...");
    
    // 顯示所有通過的測試
    RunPipelineAdvanceTestsWithUnlimitedDisplay("Demo_Unlimited");
    
    Print("✅ 無限制顯示文檔已生成");
    Print("📁 位置: MQL4\\Files\\Demo_Unlimited\\");
    Print("📋 顯示: 所有通過的測試（無限制）");
}

//+------------------------------------------------------------------+
//| 演示高級選項                                                     |
//+------------------------------------------------------------------+
void DemoAdvancedOptions()
{
    Print("🎯 演示 4: 高級自定義選項");
    Print("────────────────────────────────────────────────────────────");
    
    Print("正在測試高級自定義選項...");
    
    // 自定義顯示數量和目錄
    Print("  • 自定義顯示前5個:");
    RunPipelineAdvanceTestsWithAdvancedDocs(true, true, "Demo_Advanced_5", 5);
    
    Print("");
    
    // 自定義顯示數量（無限制）
    Print("  • 自定義無限制顯示:");
    RunPipelineAdvanceTestsWithAdvancedDocs(true, true, "Demo_Advanced_Unlimited", -1);
    
    Print("");
    
    // 只生成摘要，限制顯示
    Print("  • 只生成摘要，限制顯示前2個:");
    RunPipelineAdvanceTestsWithAdvancedDocs(false, true, "Demo_Advanced_SummaryOnly", 2);
    
    Print("✅ 高級自定義文檔已生成");
    Print("📁 位置: MQL4\\Files\\Demo_Advanced_*\\");
}

//+------------------------------------------------------------------+
//| 顯示使用指南                                                     |
//+------------------------------------------------------------------+
void ShowUsageGuide()
{
    Print("📖 使用指南:");
    Print("────────────────────────────────────────────────────────────");
    
    Print("1. 默認使用 (顯示前10個):");
    Print("   TestPipelineAdvanceWithDocs();");
    Print("");
    
    Print("2. 限制顯示數量:");
    Print("   RunPipelineAdvanceTestsWithLimitedDisplay(5);  // 顯示前5個");
    Print("   RunPipelineAdvanceTestsWithLimitedDisplay(0);  // 不顯示通過的測試");
    Print("");
    
    Print("3. 無限制顯示:");
    Print("   RunPipelineAdvanceTestsWithUnlimitedDisplay();");
    Print("");
    
    Print("4. 高級自定義:");
    Print("   RunPipelineAdvanceTestsWithAdvancedDocs(");
    Print("       true,                    // 生成完整報告");
    Print("       true,                    // 生成摘要");
    Print("       \"MyReports\",            // 輸出目錄");
    Print("       15                       // 最多顯示15個通過的測試");
    Print("   );");
    Print("");
    
    Print("5. 程式化設置:");
    Print("   PipelineAdvanceTestRunnerWithDocs* runner = new PipelineAdvanceTestRunnerWithDocs();");
    Print("   runner.SetPassedTestsDisplayOptions(20);      // 顯示前20個");
    Print("   runner.SetUnlimitedPassedTestsDisplay();      // 無限制顯示");
    Print("   runner.RunAllTestsWithDocs();");
    Print("   delete runner;");
    Print("");
    
    Print("📋 顯示選項說明:");
    Print("   • 正數 (如 5, 10, 20): 限制顯示指定數量的通過測試");
    Print("   • -1: 無限制顯示所有通過的測試");
    Print("   • 0: 不顯示通過的測試（只顯示失敗的測試）");
    Print("");
    
    Print("📁 文檔內容差異:");
    Print("   • 限制顯示: 顯示指定數量 + 省略信息 + 提示");
    Print("   • 無限制顯示: 顯示所有測試 + 總計信息");
    Print("   • 失敗測試: 始終完整顯示（不受限制影響）");
}

//+------------------------------------------------------------------+
//| 比較不同顯示選項的效果                                           |
//+------------------------------------------------------------------+
void CompareDisplayOptions()
{
    Print("🔍 比較不同顯示選項的效果:");
    Print("────────────────────────────────────────────────────────────");
    
    Print("正在生成比較文檔...");
    
    // 生成不同顯示選項的文檔進行比較
    RunPipelineAdvanceTestsWithLimitedDisplay(1, "Compare_Display_1");
    RunPipelineAdvanceTestsWithLimitedDisplay(5, "Compare_Display_5");
    RunPipelineAdvanceTestsWithLimitedDisplay(10, "Compare_Display_10");
    RunPipelineAdvanceTestsWithUnlimitedDisplay("Compare_Display_Unlimited");
    
    Print("✅ 比較文檔已生成");
    Print("📁 位置: MQL4\\Files\\Compare_Display_*\\");
    Print("");
    Print("💡 建議:");
    Print("   • 開發階段: 使用無限制顯示，查看所有測試詳情");
    Print("   • 生產環境: 使用限制顯示，保持文檔簡潔");
    Print("   • 調試時: 使用限制顯示0，只關注失敗的測試");
    Print("   • 報告時: 使用適中的限制（如5-10個），平衡詳細度和可讀性");
}

//+------------------------------------------------------------------+
//| 性能考慮說明                                                     |
//+------------------------------------------------------------------+
void ShowPerformanceConsiderations()
{
    Print("⚡ 性能考慮:");
    Print("────────────────────────────────────────────────────────────");
    
    Print("📊 文檔大小影響:");
    Print("   • 限制顯示: 文檔較小，生成速度快");
    Print("   • 無限制顯示: 文檔較大，生成時間較長");
    Print("");
    
    Print("💾 記憶體使用:");
    Print("   • 測試結果收集: 不受顯示限制影響");
    Print("   • 文檔生成: 只在寫入時受影響");
    Print("");
    
    Print("🕒 建議的使用場景:");
    Print("   • 快速檢查: 限制顯示 1-3 個");
    Print("   • 日常開發: 限制顯示 5-10 個");
    Print("   • 詳細分析: 無限制顯示");
    Print("   • 自動化測試: 限制顯示 0 個（只看失敗）");
}
