//+------------------------------------------------------------------+
//|                                        TestDocumentGenerator.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "TestFramework.mqh"
#include "TestRunnerAdvanced.mqh"

//+------------------------------------------------------------------+
//| 測試文檔生成器 - 負責生成測試報告和文檔                           |
//+------------------------------------------------------------------+
class TestDocumentGenerator
{
private:
    string m_outputDirectory;    // 輸出目錄
    string m_reportFileName;     // 報告文件名
    bool m_includeTimestamp;     // 是否包含時間戳
    bool m_includeDetails;       // 是否包含詳細信息

public:
    // 構造函數
    TestDocumentGenerator(string outputDir = "TestReports",
                         string fileName = "PipelineAdvance_TestReport",
                         bool includeTimestamp = true,
                         bool includeDetails = true)
    : m_outputDirectory(outputDir), m_reportFileName(fileName),
      m_includeTimestamp(includeTimestamp), m_includeDetails(includeDetails)
    {
        // 確保輸出目錄存在
        CreateOutputDirectory();
    }

    // 生成完整的測試報告
    bool GenerateTestReport(TestRunner* runner, string moduleName = "PipelineAdvance")
    {
        if(runner == NULL) return false;

        string fileName = BuildFileName();
        int fileHandle = FileOpen(fileName, FILE_WRITE|FILE_TXT);

        if(fileHandle == INVALID_HANDLE)
        {
            Print("錯誤: 無法創建測試報告文件 - " + fileName);
            return false;
        }

        // 寫入報告內容
        WriteReportHeader(fileHandle, moduleName);
        WriteExecutionSummary(fileHandle, runner);
        WriteTestStatistics(fileHandle, runner);

        if(m_includeDetails)
        {
            WriteDetailedResults(fileHandle, runner);
        }

        WriteReportFooter(fileHandle);

        FileClose(fileHandle);
        Print("✅ 測試報告已生成: " + fileName);
        return true;
    }

    // 生成簡化的測試摘要
    bool GenerateTestSummary(TestRunner* runner, string moduleName = "PipelineAdvance")
    {
        if(runner == NULL) return false;

        string fileName = BuildSummaryFileName();
        int fileHandle = FileOpen(fileName, FILE_WRITE|FILE_TXT);

        if(fileHandle == INVALID_HANDLE)
        {
            Print("錯誤: 無法創建測試摘要文件 - " + fileName);
            return false;
        }

        // 寫入摘要內容
        WriteSummaryHeader(fileHandle, moduleName);
        WriteExecutionSummary(fileHandle, runner);
        WriteSummaryFooter(fileHandle);

        FileClose(fileHandle);
        Print("✅ 測試摘要已生成: " + fileName);
        return true;
    }

    // 設置輸出目錄
    void SetOutputDirectory(string directory) { m_outputDirectory = directory; }

    // 設置報告文件名
    void SetReportFileName(string fileName) { m_reportFileName = fileName; }

    // 設置是否包含時間戳
    void SetIncludeTimestamp(bool include) { m_includeTimestamp = include; }

    // 設置是否包含詳細信息
    void SetIncludeDetails(bool include) { m_includeDetails = include; }

    // 專門為 TestRunnerAdvanced 設計的報告生成方法
    bool GenerateAdvancedTestReport(TestRunnerAdvanced* runner, string moduleName = "PipelineAdvance")
    {
        if(runner == NULL) return false;

        string fileName = BuildFileName();
        int fileHandle = FileOpen(fileName, FILE_WRITE|FILE_TXT);

        if(fileHandle == INVALID_HANDLE)
        {
            Print("錯誤: 無法創建測試報告文件 - " + fileName);
            return false;
        }

        // 寫入報告內容
        WriteReportHeader(fileHandle, moduleName);
        WriteExecutionSummary(fileHandle, runner);
        WriteTestStatistics(fileHandle, runner);
        WriteAdvancedResults(fileHandle, runner);
        WriteReportFooter(fileHandle);

        FileClose(fileHandle);
        Print("✅ 增強版測試報告已生成: " + fileName);
        return true;
    }

private:
    // 創建輸出目錄
    void CreateOutputDirectory()
    {
        // MQL4 會自動在 Files 目錄下創建子目錄
        // 這裡我們只需要確保目錄名稱正確
        if(m_outputDirectory == "")
        {
            m_outputDirectory = "TestReports";
        }
    }

    // 寫入增強版測試結果
    void WriteAdvancedResults(int fileHandle, TestRunnerAdvanced* runner)
    {
        // 寫入執行時間統計
        WriteExecutionTimeStats(fileHandle, runner);

        // 寫入失敗測試詳情
        WriteFailedTestDetails(fileHandle, runner);

        // 寫入通過測試列表
        WritePassedTestsList(fileHandle, runner);

        // 寫入按類別分組的結果
        WriteResultsByClass(fileHandle, runner);
    }

    // 寫入執行時間統計
    void WriteExecutionTimeStats(int fileHandle, TestRunnerAdvanced* runner)
    {
        int totalTime, avgTime, maxTime, minTime;
        runner.GetExecutionStats(totalTime, avgTime, maxTime, minTime);

        FileWrite(fileHandle, "⏱️ 執行時間統計");
        FileWrite(fileHandle, "----------------------------------------------------------------");
        FileWrite(fileHandle, "總執行時間: " + IntegerToString(totalTime) + " ms");
        FileWrite(fileHandle, "平均執行時間: " + IntegerToString(avgTime) + " ms");
        FileWrite(fileHandle, "最長執行時間: " + IntegerToString(maxTime) + " ms");
        FileWrite(fileHandle, "最短執行時間: " + IntegerToString(minTime) + " ms");
        FileWrite(fileHandle, "");
    }

    // 寫入失敗測試詳情
    void WriteFailedTestDetails(int fileHandle, TestRunnerAdvanced* runner)
    {
        TestResultDetail* failedResults[];
        int failedCount;
        runner.GetFailedResults(failedResults, failedCount);

        FileWrite(fileHandle, "❌ 失敗測試詳情");
        FileWrite(fileHandle, "----------------------------------------------------------------");

        if(failedCount == 0)
        {
            FileWrite(fileHandle, "沒有失敗的測試 - 恭喜！");
        }
        else
        {
            for(int i = 0; i < failedCount; i++)
            {
                if(failedResults[i] != NULL)
                {
                    FileWrite(fileHandle, failedResults[i].ToString());
                }
            }
        }

        FileWrite(fileHandle, "");
    }

    // 寫入通過測試列表
    void WritePassedTestsList(int fileHandle, TestRunnerAdvanced* runner)
    {
        TestResultDetail* passedResults[];
        int passedCount;
        runner.GetPassedResults(passedResults, passedCount);

        FileWrite(fileHandle, "✅ 通過測試列表");
        FileWrite(fileHandle, "----------------------------------------------------------------");

        if(passedCount == 0)
        {
            FileWrite(fileHandle, "沒有通過的測試");
        }
        else
        {
            for(int i = 0; i < passedCount; i++)
            {
                if(passedResults[i] != NULL)
                {
                    FileWrite(fileHandle, passedResults[i].ToString());
                }
            }
        }

        FileWrite(fileHandle, "");
    }

    // 寫入按類別分組的結果
    void WriteResultsByClass(int fileHandle, TestRunnerAdvanced* runner)
    {
        FileWrite(fileHandle, "📂 按測試類別分組的結果");
        FileWrite(fileHandle, "----------------------------------------------------------------");

        // 獲取所有測試類別
        string testClasses[];
        GetUniqueTestClasses(runner, testClasses);

        for(int i = 0; i < ArraySize(testClasses); i++)
        {
            WriteClassResults(fileHandle, runner, testClasses[i]);
        }
    }

    // 獲取唯一的測試類別
    void GetUniqueTestClasses(TestRunnerAdvanced* runner, string &classes[])
    {
        ArrayResize(classes, 0);

        for(int i = 0; i < runner.GetResultCount(); i++)
        {
            TestResultDetail* result = runner.GetResult(i);
            if(result != NULL)
            {
                string className = result.GetTestClass();
                bool found = false;

                for(int j = 0; j < ArraySize(classes); j++)
                {
                    if(classes[j] == className)
                    {
                        found = true;
                        break;
                    }
                }

                if(!found)
                {
                    ArrayResize(classes, ArraySize(classes) + 1);
                    classes[ArraySize(classes) - 1] = className;
                }
            }
        }
    }

    // 寫入特定類別的結果
    void WriteClassResults(int fileHandle, TestRunnerAdvanced* runner, string className)
    {
        TestResultDetail* classResults[];
        int classCount;
        runner.GetResultsByClass(className, classResults, classCount);

        FileWrite(fileHandle, "");
        FileWrite(fileHandle, "📋 " + className + " 測試結果:");

        int passed = 0, failed = 0;
        for(int i = 0; i < classCount; i++)
        {
            if(classResults[i] != NULL)
            {
                if(classResults[i].IsPassed()) passed++;
                else failed++;

                FileWrite(fileHandle, "  " + classResults[i].ToString());
            }
        }

        FileWrite(fileHandle, StringFormat("  總計: %d, 通過: %d, 失敗: %d", classCount, passed, failed));
    }

    // 構建完整的文件名
    string BuildFileName()
    {
        string fileName = m_outputDirectory + "\\" + m_reportFileName;

        if(m_includeTimestamp)
        {
            string timestamp = TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES);
            StringReplace(timestamp, ":", "");
            StringReplace(timestamp, ".", "");
            StringReplace(timestamp, " ", "_");
            fileName += "_" + timestamp;
        }

        fileName += ".txt";
        return fileName;
    }

    // 構建摘要文件名
    string BuildSummaryFileName()
    {
        string fileName = m_outputDirectory + "\\" + m_reportFileName + "_Summary";

        if(m_includeTimestamp)
        {
            string timestamp = TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES);
            StringReplace(timestamp, ":", "");
            StringReplace(timestamp, ".", "");
            StringReplace(timestamp, " ", "_");
            fileName += "_" + timestamp;
        }

        fileName += ".txt";
        return fileName;
    }

    // 寫入報告標題
    void WriteReportHeader(int fileHandle, string moduleName)
    {
        FileWrite(fileHandle, "================================================================");
        FileWrite(fileHandle, "                    EA_WIZARD 測試報告");
        FileWrite(fileHandle, "================================================================");
        FileWrite(fileHandle, "");
        FileWrite(fileHandle, "模組名稱: " + moduleName);
        FileWrite(fileHandle, "報告生成時間: " + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS));
        FileWrite(fileHandle, "測試框架版本: EA_Wizard TestFramework v1.0");
        FileWrite(fileHandle, "");
        FileWrite(fileHandle, "================================================================");
        FileWrite(fileHandle, "");
    }

    // 寫入執行摘要
    void WriteExecutionSummary(int fileHandle, TestRunner* runner)
    {
        FileWrite(fileHandle, "📊 測試執行摘要");
        FileWrite(fileHandle, "----------------------------------------------------------------");
        FileWrite(fileHandle, "總測試數量: " + IntegerToString(runner.GetTotalTests()));
        FileWrite(fileHandle, "通過測試: " + IntegerToString(runner.GetPassedTests()));
        FileWrite(fileHandle, "失敗測試: " + IntegerToString(runner.GetFailedTests()));

        double successRate = runner.GetTotalTests() > 0 ?
            (double)runner.GetPassedTests() / runner.GetTotalTests() * 100.0 : 0.0;
        FileWrite(fileHandle, "成功率: " + DoubleToString(successRate, 2) + "%");

        FileWrite(fileHandle, "");
        FileWrite(fileHandle, "測試結果: " + (runner.AllTestsPassed() ? "✅ 全部通過" : "❌ 有失敗"));
        FileWrite(fileHandle, "");
    }

    // 寫入執行摘要（TestRunnerAdvanced 版本）
    void WriteExecutionSummary(int fileHandle, TestRunnerAdvanced* runner)
    {
        FileWrite(fileHandle, "📊 測試執行摘要");
        FileWrite(fileHandle, "----------------------------------------------------------------");
        FileWrite(fileHandle, "總測試數量: " + IntegerToString(runner.GetTotalTests()));
        FileWrite(fileHandle, "通過測試: " + IntegerToString(runner.GetPassedTests()));
        FileWrite(fileHandle, "失敗測試: " + IntegerToString(runner.GetFailedTests()));
        FileWrite(fileHandle, "詳細結果數量: " + IntegerToString(runner.GetResultCount()));

        double successRate = runner.GetTotalTests() > 0 ?
            (double)runner.GetPassedTests() / runner.GetTotalTests() * 100.0 : 0.0;
        FileWrite(fileHandle, "成功率: " + DoubleToString(successRate, 2) + "%");

        FileWrite(fileHandle, "");
        FileWrite(fileHandle, "測試結果: " + (runner.AllTestsPassed() ? "✅ 全部通過" : "❌ 有失敗"));
        FileWrite(fileHandle, "");
    }

    // 寫入測試統計
    void WriteTestStatistics(int fileHandle, TestRunner* runner)
    {
        FileWrite(fileHandle, "📈 詳細統計信息");
        FileWrite(fileHandle, "----------------------------------------------------------------");

        if(runner.GetTotalTests() > 0)
        {
            double passRate = (double)runner.GetPassedTests() / runner.GetTotalTests() * 100.0;
            double failRate = (double)runner.GetFailedTests() / runner.GetTotalTests() * 100.0;

            FileWrite(fileHandle, "通過率: " + DoubleToString(passRate, 2) + "%");
            FileWrite(fileHandle, "失敗率: " + DoubleToString(failRate, 2) + "%");
        }
        else
        {
            FileWrite(fileHandle, "沒有執行任何測試");
        }

        FileWrite(fileHandle, "");
    }

    // 寫入測試統計（TestRunnerAdvanced 版本）
    void WriteTestStatistics(int fileHandle, TestRunnerAdvanced* runner)
    {
        FileWrite(fileHandle, "📈 詳細統計信息");
        FileWrite(fileHandle, "----------------------------------------------------------------");

        if(runner.GetTotalTests() > 0)
        {
            double passRate = (double)runner.GetPassedTests() / runner.GetTotalTests() * 100.0;
            double failRate = (double)runner.GetFailedTests() / runner.GetTotalTests() * 100.0;

            FileWrite(fileHandle, "通過率: " + DoubleToString(passRate, 2) + "%");
            FileWrite(fileHandle, "失敗率: " + DoubleToString(failRate, 2) + "%");
            FileWrite(fileHandle, "詳細記錄覆蓋率: 100% (增強版運行器)");
        }
        else
        {
            FileWrite(fileHandle, "沒有執行任何測試");
        }

        FileWrite(fileHandle, "");
    }

    // 寫入詳細結果
    void WriteDetailedResults(int fileHandle, TestRunner* runner)
    {
        FileWrite(fileHandle, "📋 詳細測試結果");
        FileWrite(fileHandle, "----------------------------------------------------------------");

        // 在 MQL4 中，我們需要通過其他方式來檢查類型
        // 這裡我們假設如果傳入的是 TestRunnerAdvanced，調用者會直接傳入正確的類型
        FileWrite(fileHandle, "注意: 詳細結果功能需要使用 TestRunnerAdvanced");
        FileWrite(fileHandle, "當前顯示基本測試結果信息");
        FileWrite(fileHandle, "");
        FileWrite(fileHandle, "如需詳細結果，請使用 GenerateAdvancedTestReport 方法");

        FileWrite(fileHandle, "");
    }





    // 寫入報告尾部
    void WriteReportFooter(int fileHandle)
    {
        FileWrite(fileHandle, "================================================================");
        FileWrite(fileHandle, "報告結束");
        FileWrite(fileHandle, "生成工具: EA_Wizard TestDocumentGenerator");
        FileWrite(fileHandle, "================================================================");
    }

    // 寫入摘要標題
    void WriteSummaryHeader(int fileHandle, string moduleName)
    {
        FileWrite(fileHandle, "================================");
        FileWrite(fileHandle, "    " + moduleName + " 測試摘要");
        FileWrite(fileHandle, "================================");
        FileWrite(fileHandle, "");
        FileWrite(fileHandle, "生成時間: " + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS));
        FileWrite(fileHandle, "");
    }

    // 寫入摘要尾部
    void WriteSummaryFooter(int fileHandle)
    {
        FileWrite(fileHandle, "");
        FileWrite(fileHandle, "================================");
        FileWrite(fileHandle, "摘要結束");
        FileWrite(fileHandle, "================================");
    }
};
