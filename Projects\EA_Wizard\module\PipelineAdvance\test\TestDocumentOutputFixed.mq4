//+------------------------------------------------------------------+
//|                                      TestDocumentOutputFixed.mq4 |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "RunAllTests.mqh"

//+------------------------------------------------------------------+
//| 修復後的文檔輸出功能測試腳本                                     |
//| 這個腳本用於驗證指針訪問錯誤是否已修復                           |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 腳本程序開始函數                                                 |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("╔══════════════════════════════════════════════════════════════╗");
    Print("║         修復後的 PipelineAdvance 文檔輸出功能測試            ║");
    Print("║                      EA_Wizard                               ║");
    Print("╚══════════════════════════════════════════════════════════════╝");
    Print("");
    
    // 測試基本功能（不生成文檔）
    TestBasicFunctionality();
    
    Print("");
    Print("═══════════════════════════════════════════════════════════════");
    Print("");
    
    // 測試文檔輸出功能
    TestDocumentOutput();
    
    Print("");
    Print("╔══════════════════════════════════════════════════════════════╗");
    Print("║                    測試完成                                  ║");
    Print("║     如果沒有錯誤信息，說明指針問題已修復                     ║");
    Print("╚══════════════════════════════════════════════════════════════╝");
}

//+------------------------------------------------------------------+
//| 測試基本功能                                                     |
//+------------------------------------------------------------------+
void TestBasicFunctionality()
{
    Print("🧪 測試 1: 基本測試功能（不生成文檔）");
    Print("────────────────────────────────────────────────────────────");
    
    try
    {
        // 運行基本測試
        RunAllPipelineAdvanceTests();
        Print("✅ 基本測試功能正常");
    }
    catch(string error)
    {
        Print("❌ 基本測試功能出錯: " + error);
    }
}

//+------------------------------------------------------------------+
//| 測試文檔輸出功能                                                 |
//+------------------------------------------------------------------+
void TestDocumentOutput()
{
    Print("📄 測試 2: 文檔輸出功能");
    Print("────────────────────────────────────────────────────────────");
    
    try
    {
        // 測試文檔輸出功能
        Print("正在測試文檔輸出功能...");
        TestPipelineAdvanceWithDocs();
        Print("✅ 文檔輸出功能正常");
    }
    catch(string error)
    {
        Print("❌ 文檔輸出功能出錯: " + error);
    }
}

//+------------------------------------------------------------------+
//| 測試增強版運行器                                                 |
//+------------------------------------------------------------------+
void TestAdvancedRunner()
{
    Print("🔧 測試 3: 增強版測試運行器");
    Print("────────────────────────────────────────────────────────────");
    
    try
    {
        // 直接測試增強版運行器
        TestRunnerAdvanced* runner = new TestRunnerAdvanced(true);
        
        // 創建一個簡單的測試結果
        TestResult* result = new TestResult("測試項目", true, "測試成功");
        
        // 記錄結果（這裡之前會出現指針錯誤）
        runner.RecordResult(result);
        
        Print("✅ 增強版運行器功能正常");
        Print("   總測試數: " + IntegerToString(runner.GetTotalTests()));
        Print("   通過測試: " + IntegerToString(runner.GetPassedTests()));
        Print("   詳細結果數: " + IntegerToString(runner.GetResultCount()));
        
        delete runner;
    }
    catch(string error)
    {
        Print("❌ 增強版運行器出錯: " + error);
    }
}

//+------------------------------------------------------------------+
//| 測試文檔生成器                                                   |
//+------------------------------------------------------------------+
void TestDocumentGenerator()
{
    Print("📋 測試 4: 文檔生成器");
    Print("────────────────────────────────────────────────────────────");
    
    try
    {
        // 創建測試運行器和文檔生成器
        TestRunnerAdvanced* runner = new TestRunnerAdvanced(true);
        TestDocumentGenerator* generator = new TestDocumentGenerator("TestOutput", "FixedTest", true, true);
        
        // 添加一些測試結果
        TestResult* result1 = new TestResult("測試1", true, "成功");
        TestResult* result2 = new TestResult("測試2", false, "失敗原因");
        
        runner.RecordResult(result1);
        runner.RecordResult(result2);
        
        // 生成文檔
        bool success = generator.GenerateAdvancedTestReport(runner, "PipelineAdvance");
        
        if(success)
        {
            Print("✅ 文檔生成器功能正常");
            Print("   文檔已保存到 MQL4\\Files\\TestOutput\\ 目錄");
        }
        else
        {
            Print("❌ 文檔生成失敗");
        }
        
        delete runner;
        delete generator;
    }
    catch(string error)
    {
        Print("❌ 文檔生成器出錯: " + error);
    }
}

//+------------------------------------------------------------------+
//| 完整測試流程                                                     |
//+------------------------------------------------------------------+
void RunCompleteTest()
{
    Print("🚀 運行完整測試流程");
    Print("────────────────────────────────────────────────────────────");
    
    TestBasicFunctionality();
    Print("");
    
    TestAdvancedRunner();
    Print("");
    
    TestDocumentGenerator();
    Print("");
    
    TestDocumentOutput();
    Print("");
    
    Print("✅ 完整測試流程完成");
}

//+------------------------------------------------------------------+
//| 錯誤處理示例                                                     |
//+------------------------------------------------------------------+
void try(void)
{
    // MQL4 沒有真正的 try-catch，這裡只是示意
    // 實際上我們依賴 MQL4 的錯誤處理機制
}

void catch(string error)
{
    // 錯誤處理邏輯
    Print("捕獲到錯誤: " + error);
    Print("錯誤代碼: " + IntegerToString(GetLastError()));
}
