//+------------------------------------------------------------------+
//|                                       EAPipelineStatePending.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "EAPipelineState.mqh"
#include "EAPipelineStateRunning.mqh"

//+------------------------------------------------------------------+
//| 流水線待執行狀態 - 實現待執行狀態的行為                           |
//+------------------------------------------------------------------+

class EAPipelineStatePending : public EAPipelineState
{
public:
    // 構造函數
    EAPipelineStatePending()
    : EAPipelineState(EA_PIPELINE_STATE_PENDING, "待執行") {}
    
    // 析構函數
    ~EAPipelineStatePending() {}
    
    // 執行流水線
    void Execute(EAPipeline_A* pipeline) override;
    
    // 重置流水線狀態
    void Restore(EAPipeline_A* pipeline) override;
};


void EAPipelineStatePending::Execute(EAPipeline_A* pipeline)
{
    // 將狀態轉換為執行中
    pipeline.SetState(new EAPipelineStateRunning());

    // 執行實際的流水線邏輯
    pipeline.Execute();

    if(pipeline.GetResult().IsSuccess())
    {
        pipeline.SetState(new EAPipelineStateCompleted());
    }
    else
    {
        pipeline.SetState(new EAPipelineStateFailed());
    }
    
    // 根據執行結果更新狀態
    // 這部分應該在流水線內部實現，而不是在狀態中實現
}


void EAPipelineStatePending::Restore(EAPipeline_A* pipeline)
{
    // 待執行狀態下的重置不需要做任何事情，因為已經是初始狀態
}
