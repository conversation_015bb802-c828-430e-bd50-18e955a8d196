//+------------------------------------------------------------------+
//| Module: Configuration/JsonExtensions.mqh                         |
//| This file is part of the EA_Wizard project.                      |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

// 防止重複包含
#ifndef _JSON_EXTENSIONS_MQH_
#define _JSON_EXTENSIONS_MQH_

#include "../mql4-lib-master/Format/Json.mqh"
#include "../mql4-lib-master/Lang/String.mqh"

//+------------------------------------------------------------------+
//| 擴展Json類的功能                                                  |
//+------------------------------------------------------------------+
class JsonExtensions
{
public:
    /**
     * 將JsonValue轉換為JSON字符串
     * @param value JsonValue對象
     * @return JSON字符串
     */
    static string dumps(const JsonValue &value)
    {
        if(CheckPointer(&value) == POINTER_INVALID || &value == NULL)
        {
            return "null";
        }

        // 處理null值
        if(&value == null)
        {
            return "null";
        }

        // 處理布爾值
        const JsonBoolean *boolValue = dynamic_cast<const JsonBoolean*>(&value);
        if(boolValue != NULL)
        {
            return boolValue.value ? "true" : "false";
        }

        // 處理數值
        const JsonNumber *numValue = dynamic_cast<const JsonNumber*>(&value);
        if(numValue != NULL)
        {
            return DoubleToString(numValue.value, 8);
        }

        // 處理字符串
        const JsonString *strValue = dynamic_cast<const JsonString*>(&value);
        if(strValue != NULL)
        {
            return StringFormat("\"%s\"", escapeJsonString(strValue.value));
        }

        // 處理數組
        const JsonArray *arrValue = dynamic_cast<const JsonArray*>(&value);
        if(arrValue != NULL)
        {
            string result = "[";

            for(int i = 0; i < arrValue.length(); i++)
            {
                if(i > 0) result += ",";
                result += dumps(arrValue[i]);
            }

            result += "]";
            return result;
        }

        // 處理對象
        const JsonObject *objValue = dynamic_cast<const JsonObject*>(&value);
        if(objValue != NULL)
        {
            string result = "{";

            // 獲取所有鍵
            JsonArray *keys = objValue.keys();

            for(int i = 0; i < keys.length(); i++)
            {
                if(i > 0) result += ",";

                JsonString *keyObj = dynamic_cast<JsonString*>(keys[i]);
                if(keyObj == NULL) continue;

                string key = keyObj.value;
                JsonValue *val = objValue[key];

                result += StringFormat("\"%s\":%s", escapeJsonString(key), dumps(val));
            }

            result += "}";
            return result;
        }

        return "null";
    }

private:
    /**
     * 轉義JSON字符串中的特殊字符
     * @param str 原始字符串
     * @return 轉義後的字符串
     */
    static string escapeJsonString(const string &str)
    {
        string result = "";
        int len = StringLen(str);

        for(int i = 0; i < len; i++)
        {
            ushort ch = StringGetCharacter(str, i);

            switch(ch)
            {
                case '\"': result += "\\\""; break;
                case '\\': result += "\\\\"; break;
                case '/':  result += "\\/";  break;
                case 8:    result += "\\b";  break; // Backspace
                case 12:   result += "\\f";  break; // Form feed
                case '\n': result += "\\n";  break;
                case '\r': result += "\\r";  break;
                case '\t': result += "\\t";  break;
                default:
                    if(ch < 32)
                    {
                        result += StringFormat("\\u%04x", ch);
                    }
                    else
                    {
                        result += ShortToString(ch);
                    }
                    break;
            }
        }

        return result;
    }
};

#endif // _JSON_EXTENSIONS_MQH_
