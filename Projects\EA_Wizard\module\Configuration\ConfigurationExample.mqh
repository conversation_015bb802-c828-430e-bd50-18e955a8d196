//+------------------------------------------------------------------+
//| Module: Configuration/ConfigurationExample.mqh                   |
//| This file is part of the EA_Wizard project.                      |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "JsonConfigurationManager.mqh"

//+------------------------------------------------------------------+
//| 配置示例類                                                        |
//+------------------------------------------------------------------+
class ConfigurationExample
{
public:
    /**
     * 基本配置示例
     */
    static void BasicExample()
    {
        Print("===== 基本配置示例 =====");
        
        // 獲取配置管理器實例
        JsonConfigurationManager* config = JsonConfigurationManager::GetInstance("example_config.json");
        
        // 加載配置
        if(!config.load())
        {
            Print("配置加載失敗: ", GetLastError());
            return;
        }
        
        Print("配置已加載");
        
        // 訪問配置值
        string name = config.getString("name", "Default EA");
        double version = config.getNumber("version", 1.0);
        bool enabled = config.getBoolean("enabled", true);
        string symbol = config.getString("symbol", Symbol());
        double takeProfit = config.getNumber("takeProfit", 50.0);
        double stopLoss = config.getNumber("stopLoss", 30.0);
        
        // 打印配置值
        Print("名稱: ", name);
        Print("版本: ", version);
        Print("啟用: ", enabled);
        Print("交易品種: ", symbol);
        Print("止盈點數: ", takeProfit);
        Print("止損點數: ", stopLoss);
        
        // 訪問嵌套配置
        JsonObject* riskSettings = config.getObject("riskManagement");
        if(riskSettings != NULL)
        {
            bool riskEnabled = dynamic_cast<JsonBoolean*>(riskSettings["enabled"]).value;
            double maxRiskPercent = dynamic_cast<JsonNumber*>(riskSettings["maxRiskPercent"]).value;
            
            Print("風險管理啟用: ", riskEnabled);
            Print("最大風險百分比: ", maxRiskPercent, "%");
        }
        
        // 訪問數組配置
        JsonArray* indicators = config.getArray("indicators");
        if(indicators != NULL)
        {
            Print("指標數量: ", indicators.length());
            
            for(int i = 0; i < indicators.length(); i++)
            {
                JsonObject* indicator = dynamic_cast<JsonObject*>(indicators[i]);
                if(indicator != NULL)
                {
                    string indName = dynamic_cast<JsonString*>(indicator["name"]).value;
                    bool indEnabled = dynamic_cast<JsonBoolean*>(indicator["enabled"]).value;
                    
                    Print("指標 ", i, ": ", indName, " (", (indEnabled ? "啟用" : "禁用"), ")");
                }
            }
        }
    }
    
    /**
     * 更新配置示例
     */
    static void UpdateExample()
    {
        Print("===== 更新配置示例 =====");
        
        // 獲取配置管理器實例
        JsonConfigurationManager* config = JsonConfigurationManager::GetInstance("example_config.json");
        
        // 加載配置
        if(!config.load())
        {
            Print("配置加載失敗: ", GetLastError());
            return;
        }
        
        // 更新配置值
        config.setString("name", "Updated EA");
        config.setNumber("version", 1.1);
        config.setBoolean("enabled", false);
        
        // 更新嵌套配置
        JsonObject* riskSettings = config.getObject("riskManagement");
        if(riskSettings != NULL)
        {
            dynamic_cast<JsonBoolean*>(riskSettings["enabled"]).value = true;
            dynamic_cast<JsonNumber*>(riskSettings["maxRiskPercent"]).value = 3.0;
        }
        
        // 保存配置
        if(!config.save())
        {
            Print("配置保存失敗: ", GetLastError());
            return;
        }
        
        Print("配置已更新並保存");
        
        // 重新加載配置
        if(!config.load())
        {
            Print("配置重新加載失敗: ", GetLastError());
            return;
        }
        
        // 驗證更新後的值
        string name = config.getString("name");
        double version = config.getNumber("version");
        bool enabled = config.getBoolean("enabled");
        
        Print("更新後的名稱: ", name);
        Print("更新後的版本: ", version);
        Print("更新後的啟用狀態: ", enabled);
        
        // 驗證嵌套配置更新
        riskSettings = config.getObject("riskManagement");
        if(riskSettings != NULL)
        {
            bool riskEnabled = dynamic_cast<JsonBoolean*>(riskSettings["enabled"]).value;
            double maxRiskPercent = dynamic_cast<JsonNumber*>(riskSettings["maxRiskPercent"]).value;
            
            Print("更新後的風險管理啟用: ", riskEnabled);
            Print("更新後的最大風險百分比: ", maxRiskPercent, "%");
        }
    }
    
    /**
     * 重置配置示例
     */
    static void ResetExample()
    {
        Print("===== 重置配置示例 =====");
        
        // 獲取配置管理器實例
        JsonConfigurationManager* config = JsonConfigurationManager::GetInstance("example_config.json");
        
        // 重置配置
        if(!config.reset())
        {
            Print("配置重置失敗: ", GetLastError());
            return;
        }
        
        Print("配置已重置為默認值");
        
        // 驗證重置後的值
        string name = config.getString("name");
        double version = config.getNumber("version");
        bool enabled = config.getBoolean("enabled");
        
        Print("重置後的名稱: ", name);
        Print("重置後的版本: ", version);
        Print("重置後的啟用狀態: ", enabled);
    }
};
