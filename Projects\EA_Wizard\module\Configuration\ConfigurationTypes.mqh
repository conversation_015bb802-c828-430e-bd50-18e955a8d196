//+------------------------------------------------------------------+
//| Module: Configuration/ConfigurationTypes.mqh                     |
//| This file is part of the EA_Wizard project.                      |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

// 防止重複包含
#ifndef _CONFIGURATION_TYPES_MQH_
#define _CONFIGURATION_TYPES_MQH_

//+------------------------------------------------------------------+
//| 配置值類型枚舉                                                    |
//+------------------------------------------------------------------+
enum ENUM_CONFIG_VALUE_TYPE
{
    CONFIG_TYPE_NULL,     // 空值
    CONFIG_TYPE_BOOLEAN,  // 布爾值
    CONFIG_TYPE_NUMBER,   // 數值
    CONFIG_TYPE_STRING,   // 字符串
    CONFIG_TYPE_ARRAY,    // 數組
    CONFIG_TYPE_OBJECT    // 對象
};

//+------------------------------------------------------------------+
//| 配置錯誤代碼                                                      |
//+------------------------------------------------------------------+
//+------------------------------------------------------------------+
//| 配置錯誤處理                                                      |
//+------------------------------------------------------------------+
// 錯誤代碼
#define CONFIG_ERROR_NONE              0  // 無錯誤
#define CONFIG_ERROR_FILE_NOT_FOUND    1  // 文件不存在
#define CONFIG_ERROR_FILE_READ         2  // 文件讀取錯誤
#define CONFIG_ERROR_FILE_WRITE        3  // 文件寫入錯誤
#define CONFIG_ERROR_PARSE             4  // 解析錯誤
#define CONFIG_ERROR_INVALID_TYPE      5  // 類型錯誤
#define CONFIG_ERROR_KEY_NOT_FOUND     6  // 鍵不存在
#define CONFIG_ERROR_VALIDATION        7  // 驗證錯誤
#define CONFIG_ERROR_SCHEMA            8  // 架構錯誤

// 全局錯誤變量
int g_configLastError = CONFIG_ERROR_NONE;

// 設置錯誤代碼
void ConfigSetLastError(int error)
{
    g_configLastError = error;
}

// 獲取錯誤代碼
int ConfigGetLastError()
{
    return g_configLastError;
}

//+------------------------------------------------------------------+
//| 配置文件路徑常量                                                  |
//+------------------------------------------------------------------+
#define CONFIG_DEFAULT_PATH            "MQL4\\Files\\"  // 默認配置文件路徑
#define CONFIG_DEFAULT_EXTENSION       ".json"          // 默認配置文件擴展名

//+------------------------------------------------------------------+
//| 配置驗證常量                                                      |
//+------------------------------------------------------------------+
#define CONFIG_VALIDATION_REQUIRED     true   // 必填項
#define CONFIG_VALIDATION_OPTIONAL     false  // 可選項

//+------------------------------------------------------------------+
//| 配置架構項結構                                                    |
//+------------------------------------------------------------------+
struct SConfigSchemaItem
{
    string                 key;           // 配置項鍵名
    ENUM_CONFIG_VALUE_TYPE type;          // 配置項類型
    bool                   required;      // 是否必填
    string                 defaultValue;  // 默認值（字符串形式）
};

#endif // _CONFIGURATION_TYPES_MQH_
