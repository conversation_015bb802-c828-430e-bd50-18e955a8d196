//+------------------------------------------------------------------+
//|                                               SafeTestRunner.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "TestFramework.mqh"

//+------------------------------------------------------------------+
//| 安全測試運行器 - 提供更安全的測試執行環境                         |
//+------------------------------------------------------------------+
class SafeTestRunner
{
private:
    int m_totalTests;                // 總測試數
    int m_passedTests;               // 通過的測試數
    int m_failedTests;               // 失敗的測試數
    string m_currentTestClass;       // 當前測試類別名稱
    datetime m_testStartTime;        // 測試開始時間
    datetime m_testEndTime;          // 測試結束時間

public:
    // 構造函數
    SafeTestRunner() : m_totalTests(0), m_passedTests(0), m_failedTests(0),
                       m_currentTestClass(""), m_testStartTime(0), m_testEndTime(0) {}

    // 析構函數
    virtual ~SafeTestRunner() {}

    // 安全記錄測試結果
    virtual void RecordResult(TestResult* result)
    {
        if(result == NULL)
        {
            Print("[錯誤] 測試結果為空指針");
            return;
        }

        // 安全地獲取測試信息
        string testName = "";
        string message = "";
        bool passed = false;

        // 檢查指針有效性並獲取信息
        if(CheckPointer(result) == POINTER_DYNAMIC)
        {
            testName = result.GetTestName();
            message = result.GetMessage();
            passed = result.IsPassed();
        }
        else
        {
            Print("[錯誤] 測試結果指針無效");
            return;
        }

        // 更新統計
        m_totalTests++;
        if(passed)
        {
            m_passedTests++;
            Print(StringFormat("[通過] %s", testName));
        }
        else
        {
            m_failedTests++;
            Print(StringFormat("[失敗] %s: %s", testName, message));
        }

        // 安全刪除結果
        delete result;
    }

    // 安全運行測試類別
    virtual void RunTestCase(TestCase* testCase)
    {
        if(testCase == NULL)
        {
            Print("[錯誤] 測試類別為空指針");
            return;
        }

        // 檢查指針有效性並執行測試
        if(CheckPointer(testCase) == POINTER_DYNAMIC)
        {
            m_currentTestClass = testCase.GetClassName();
            m_testStartTime = TimeCurrent();

            Print(StringFormat("=== 開始執行測試類別: %s ===", m_currentTestClass));

            // 安全執行測試
            testCase.RunTests();

            m_testEndTime = TimeCurrent();
            Print(StringFormat("=== 完成測試類別: %s (耗時: %d秒) ===",
                              m_currentTestClass, (int)(m_testEndTime - m_testStartTime)));
        }
        else
        {
            Print(StringFormat("[錯誤] 測試類別指針無效"));
        }
    }

    // 顯示安全摘要
    virtual void ShowSummary()
    {
        Print("=== 安全測試摘要 ===");
        Print(StringFormat("總測試數: %d", GetTotalTests()));
        Print(StringFormat("通過: %d", GetPassedTests()));
        Print(StringFormat("失敗: %d", GetFailedTests()));

        if(GetTotalTests() > 0)
        {
            double successRate = (double)GetPassedTests() / GetTotalTests() * 100.0;
            Print(StringFormat("成功率: %.2f%%", successRate));
        }
        else
        {
            Print("成功率: 0.00%");
        }

        if(m_testEndTime > m_testStartTime)
        {
            Print(StringFormat("總耗時: %d秒", (int)(m_testEndTime - m_testStartTime)));
        }
    }

    // 獲取測試統計
    int GetTotalTests() const { return m_totalTests; }
    int GetPassedTests() const { return m_passedTests; }
    int GetFailedTests() const { return m_failedTests; }
    bool AllTestsPassed() const { return m_failedTests == 0 && m_totalTests > 0; }

    // 獲取當前測試類別
    string GetCurrentTestClass() const { return m_currentTestClass; }

    // 獲取測試統計信息
    string GetTestStatistics() const
    {
        string stats = StringFormat("總測試數: %d, 通過: %d, 失敗: %d",
                                   GetTotalTests(), GetPassedTests(), GetFailedTests());

        if(GetTotalTests() > 0)
        {
            double successRate = (double)GetPassedTests() / GetTotalTests() * 100.0;
            stats += StringFormat(", 成功率: %.2f%%", successRate);
        }

        if(m_testEndTime > m_testStartTime)
        {
            stats += StringFormat(", 總耗時: %d秒", (int)(m_testEndTime - m_testStartTime));
        }

        return stats;
    }
};

//+------------------------------------------------------------------+
//| 安全測試執行函數                                                 |
//+------------------------------------------------------------------+
void RunSafeEAPipelineTests()
{
    Print("=== 開始安全 EAPipeline 測試 ===");

    SafeTestRunner* runner = new SafeTestRunner();

    if(runner == NULL)
    {
        Print("[錯誤] 無法創建安全測試運行器");
        return;
    }

    // 檢查運行器有效性並執行測試
    if(CheckPointer(runner) == POINTER_DYNAMIC)
    {
        // 運行基本測試
        Print("--- 開始基本功能測試 ---");
        RunBasicSafeTests(runner);

        // 顯示摘要
        runner.ShowSummary();

        Print("=== 安全 EAPipeline 測試完成 ===");
    }
    else
    {
        Print("[錯誤] 測試運行器指針無效");
    }

    delete runner;
}

//+------------------------------------------------------------------+
//| 運行基本安全測試                                                 |
//+------------------------------------------------------------------+
void RunBasicSafeTests(SafeTestRunner* runner)
{
    if(runner == NULL) return;

    // 測試基本斷言功能
    Print("測試基本斷言功能...");

    TestResult* result1 = Assert::AssertTrue("基本測試_真值", true);
    runner.RecordResult(result1);

    TestResult* result2 = Assert::AssertFalse("基本測試_假值", false);
    runner.RecordResult(result2);

    TestResult* result3 = Assert::AssertEquals("基本測試_字符串相等", "test", "test");
    runner.RecordResult(result3);

    TestResult* result4 = Assert::AssertEquals("基本測試_整數相等", 123, 123);
    runner.RecordResult(result4);

    // 測試 EACompoundPipeline 基本功能
    Print("測試 EACompoundPipeline 基本功能...");

    EACompoundPipeline* pipeline = new EACompoundPipeline("SafeTestPipeline");

    if(pipeline != NULL && CheckPointer(pipeline) == POINTER_DYNAMIC)
    {
        TestResult* result5 = Assert::AssertNotNull("EACompoundPipeline_創建成功", pipeline);
        runner.RecordResult(result5);

        TestResult* result6 = Assert::AssertEquals("EACompoundPipeline_名稱正確", "SafeTestPipeline", pipeline.GetName());
        runner.RecordResult(result6);

        TestResult* result7 = Assert::AssertEquals("EACompoundPipeline_初始數量為0", 0, pipeline.GetCount());
        runner.RecordResult(result7);

        delete pipeline;
    }
    else
    {
        TestResult* result5 = Assert::AssertTrue("EACompoundPipeline_創建失敗", false);
        runner.RecordResult(result5);

        if(pipeline != NULL) delete pipeline; // 清理可能的無效指針
    }

    // 測試 EAPipelineManager 基本功能
    Print("測試 EAPipelineManager 基本功能...");

    EAPipelineManager* manager = EAPipelineManager::GetInstance();

    if(manager != NULL && CheckPointer(manager) == POINTER_DYNAMIC)
    {
        TestResult* result8 = Assert::AssertNotNull("EAPipelineManager_獲取成功", manager);
        runner.RecordResult(result8);

        int initialCount = manager.GetPipelineCount();
        TestResult* result9 = Assert::AssertTrue("EAPipelineManager_初始數量非負", initialCount >= 0);
        runner.RecordResult(result9);
    }
    else
    {
        TestResult* result8 = Assert::AssertTrue("EAPipelineManager_獲取失敗", false);
        runner.RecordResult(result8);
    }

    Print("基本安全測試完成");
}

//+------------------------------------------------------------------+
//| 快速安全檢查                                                     |
//+------------------------------------------------------------------+
bool QuickSafeEAPipelineCheck()
{
    Print("=== 開始快速安全檢查 ===");

    SafeTestRunner* runner = new SafeTestRunner();

    if(runner == NULL)
    {
        Print("[錯誤] 無法創建測試運行器");
        return false;
    }

    // 運行最基本的測試
    TestResult* result = Assert::AssertTrue("快速檢查_基本功能", true);
    runner.RecordResult(result);

    bool allPassed = runner.AllTestsPassed();

    Print(StringFormat("=== 快速安全檢查完成 - %s ===", allPassed ? "通過" : "失敗"));

    delete runner;
    return allPassed;
}
