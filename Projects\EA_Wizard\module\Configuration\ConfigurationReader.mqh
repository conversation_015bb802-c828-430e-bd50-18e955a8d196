//+------------------------------------------------------------------+
//| Module: Configuration/ConfigurationReader.mqh                    |
//| This file is part of the EA_Wizard project.                      |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

// 防止重複包含
#ifndef _CONFIGURATION_READER_MQH_
#define _CONFIGURATION_READER_MQH_

#include "ConfigurationTypes.mqh"
#include "../mql4-lib-master/Utils/File.mqh"
#include "../mql4-lib-master/Lang/String.mqh"


//+------------------------------------------------------------------+
//| 配置讀取器類                                                      |
//+------------------------------------------------------------------+
class ConfigurationReader
{
public:
    /**
     * 讀取JSON文件內容
     * @param filename 文件名
     * @param common 是否使用公共目錄
     * @return 文件內容字符串，失敗時返回空字符串
     */
    static string readJsonFile(string filename, bool common=false)
    {
        // 檢查文件是否存在
        if(!File::exist(filename, common))
        {
            Print("配置文件不存在: ", filename);
            ConfigSetLastError(CONFIG_ERROR_FILE_NOT_FOUND);
            return "";
        }

        // 打開文件
        TextFile file(filename, FILE_READ, CP_UTF8);
        if(!file.valid())
        {
            Print("無法打開配置文件: ", filename, ", 錯誤: ", GetLastError());
            ConfigSetLastError(CONFIG_ERROR_FILE_READ);
            return "";
        }

        // 讀取文件內容
        string content = "";
        while(!file.end())
        {
            content += file.readLine();
        }

        return content;
    }

    /**
     * 寫入JSON文件內容
     * @param filename 文件名
     * @param content 文件內容
     * @param common 是否使用公共目錄
     * @return 是否成功
     */
    static bool writeJsonFile(string filename, string content, bool common=false)
    {
        // 打開文件
        TextFile file(filename, FILE_WRITE, CP_UTF8);
        if(!file.valid())
        {
            Print("無法創建配置文件: ", filename, ", 錯誤: ", GetLastError());
            ConfigSetLastError(CONFIG_ERROR_FILE_WRITE);
            return false;
        }

        // 寫入文件內容
        uint bytesWritten = file.write(content);
        if(bytesWritten == 0)
        {
            Print("寫入配置文件失敗: ", filename, ", 錯誤: ", GetLastError());
            ConfigSetLastError(CONFIG_ERROR_FILE_WRITE);
            return false;
        }

        return true;
    }

    /**
     * 檢查文件是否存在
     * @param filename 文件名
     * @param common 是否使用公共目錄
     * @return 文件是否存在
     */
    static bool fileExists(string filename, bool common=false)
    {
        return File::exist(filename, common);
    }

    /**
     * 獲取完整文件路徑
     * @param filename 文件名
     * @param common 是否使用公共目錄
     * @return 完整文件路徑
     */
    static string getFullPath(string filename, bool common=false)
    {
        // 如果文件名已經包含路徑，則直接返回
        if(StringFind(filename, "\\") >= 0 || StringFind(filename, "/") >= 0)
        {
            return filename;
        }

        // 添加默認路徑
        string path = CONFIG_DEFAULT_PATH + filename;

        // 添加默認擴展名（如果沒有）
        if(StringFind(path, ".") < 0)
        {
            path += CONFIG_DEFAULT_EXTENSION;
        }

        return path;
    }
};

#endif // _CONFIGURATION_READER_MQH_
