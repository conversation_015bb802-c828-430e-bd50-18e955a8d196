//+------------------------------------------------------------------+
//|                                               RunAllTests.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "TestRunner.mqh"
#include "TestRunnerWithDocuments.mqh"

//+------------------------------------------------------------------+
//| RegistryAdvance模組測試主入口                                    |
//| 這個檔案提供了運行RegistryAdvance模組所有測試的主要入口點         |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 主測試函數 - 運行所有測試                                         |
//+------------------------------------------------------------------+
void RunRegistryAdvanceTests()
{
    Print("開始執行RegistryAdvance模組測試...");
    Print("測試時間: " + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS));
    Print("");

    // 運行所有測試
    RunAllRegistryAdvanceTests();

    Print("");
    Print("RegistryAdvance模組測試執行完成。");
}

//+------------------------------------------------------------------+
//| 測試菜單函數 - 提供交互式測試選項                                 |
//+------------------------------------------------------------------+
void ShowRegistryAdvanceTestMenu()
{
    Print("╔══════════════════════════════════════════════════════════════╗");
    Print("║              RegistryAdvance 測試菜單                        ║");
    Print("╚══════════════════════════════════════════════════════════════╝");
    Print("");
    Print("可用的測試選項:");
    Print("1. RunAllRegistryAdvanceTests()           - 運行所有測試");
    Print("2. RunRegistryAdvanceUnitTests()          - 只運行單元測試");
    Print("3. RunRegistryAdvanceIntegrationTests()   - 只運行整合測試");
    Print("4. QuickRegistryAdvanceCheck()            - 快速檢查（靜默模式）");
    Print("");
    Print("特定測試類別:");
    Print("5. RunSpecificRegistryAdvanceTest(\"TestRegistryItem\")");
    Print("6. RunSpecificRegistryAdvanceTest(\"TestRegistryResult\")");
    Print("7. RunSpecificRegistryAdvanceTest(\"TestItemRegistry\")");
    Print("8. RunSpecificRegistryAdvanceTest(\"TestRegistryIntegration\")");
    Print("");
    Print("使用方法: 在EA的OnInit()或OnTick()中調用相應的函數");
    Print("例如: RunAllRegistryAdvanceTests();");
}

//+------------------------------------------------------------------+
//| 驗證測試環境函數                                                 |
//+------------------------------------------------------------------+
bool ValidateRegistryTestEnvironment()
{
    Print("🔍 驗證測試環境...");

    bool environmentValid = true;

    // 檢查測試框架
    TestRunner* testRunner = new TestRunner();
    if(testRunner == NULL)
    {
        Print("❌ 測試框架初始化失敗");
        environmentValid = false;
    }
    else
    {
        delete testRunner;
        Print("✅ 測試框架正常");
    }

    // 檢查Mock類別
    MockRegistry<string, int>* mockRegistry = MockRegistryFactory::CreateStringKeyRegistry<int>("環境測試");
    if(mockRegistry == NULL)
    {
        Print("❌ Mock類別初始化失敗");
        environmentValid = false;
    }
    else
    {
        MockRegistryFactory::CleanupRegistry(mockRegistry);
        Print("✅ Mock類別正常");
    }

    // 檢查被測試的類別
    ItemRegistry<string>* registry = new ItemRegistry<string>("環境測試", "EnvTest");
    if(registry == NULL)
    {
        Print("❌ ItemRegistry類別初始化失敗");
        environmentValid = false;
    }
    else
    {
        delete registry;
        Print("✅ ItemRegistry類別正常");
    }

    // 檢查RegistryItem類別
    RegistryItem<string>* item = new RegistryItem<string>("test_id", "測試項目", "測試描述", "測試值", "string");
    if(item == NULL)
    {
        Print("❌ RegistryItem類別初始化失敗");
        environmentValid = false;
    }
    else
    {
        delete item;
        Print("✅ RegistryItem類別正常");
    }

    // 檢查RegistryResult類別
    RegistryResult<string>* result = new RegistryResult<string>(true, "測試消息", "test_key", "測試來源");
    if(result == NULL)
    {
        Print("❌ RegistryResult類別初始化失敗");
        environmentValid = false;
    }
    else
    {
        delete result;
        Print("✅ RegistryResult類別正常");
    }

    if(environmentValid)
    {
        Print("🎉 測試環境驗證通過！");
    }
    else
    {
        Print("❌ 測試環境驗證失敗，請檢查相關檔案和依賴");
    }

    return environmentValid;
}

//+------------------------------------------------------------------+
//| 性能測試函數                                                     |
//+------------------------------------------------------------------+
void RunRegistryPerformanceTests()
{
    Print("🚀 開始性能測試...");

    uint startTime = GetTickCount();

    // 運行快速檢查
    bool result = QuickRegistryAdvanceCheck();

    uint endTime = GetTickCount();
    uint duration = endTime - startTime;

    Print("⏱️ 性能測試結果:");
    Print("   執行時間: " + IntegerToString(duration) + " 毫秒");
    Print("   測試結果: " + (result ? "通過" : "失敗"));

    if(duration < 2000)
    {
        Print("✅ 性能優秀 (< 2秒)");
    }
    else if(duration < 5000)
    {
        Print("⚠️ 性能一般 (2-5秒)");
    }
    else
    {
        Print("❌ 性能較差 (> 5秒)");
    }

    // 單獨的註冊器性能測試
    Print("");
    Print("📊 詳細性能測試:");

    ItemRegistry<int>* perfRegistry = new ItemRegistry<int>("性能測試", "PerfTest", 1000);

    // 測試註冊性能
    startTime = GetTickCount();
    for(int i = 0; i < 100; i++)
    {
        string key = StringFormat("perf_item_%d", i);
        RegistryResult<string>* regResult = perfRegistry.Register(key, "性能測試項目", i);
        delete regResult;
    }
    uint registerDuration = GetTickCount() - startTime;

    // 測試查詢性能
    startTime = GetTickCount();
    for(int i = 0; i < 100; i++)
    {
        string key = StringFormat("perf_item_%d", i);
        RegistryItem<int>* item = perfRegistry.Find(key);
        // 不需要delete item，因為它是內部管理的
    }
    uint queryDuration = GetTickCount() - startTime;

    Print("   註冊100個項目耗時: " + IntegerToString(registerDuration) + " ms");
    Print("   查詢100個項目耗時: " + IntegerToString(queryDuration) + " ms");

    delete perfRegistry;
}

//+------------------------------------------------------------------+
//| 回歸測試函數 - 用於確保修改後功能仍然正常                         |
//+------------------------------------------------------------------+
void RunRegistryRegressionTests()
{
    Print("🔄 開始回歸測試...");
    Print("這將運行所有測試以確保沒有功能退化...");

    bool previousResult = QuickRegistryAdvanceCheck();

    Print("📊 回歸測試結果:");
    if(previousResult)
    {
        Print("✅ 回歸測試通過 - 所有功能正常");
        Print("   可以安全地部署新版本");
    }
    else
    {
        Print("❌ 回歸測試失敗 - 發現功能退化");
        Print("   請檢查最近的修改並修復問題");
    }
}

//+------------------------------------------------------------------+
//| 測試報告生成函數                                                 |
//+------------------------------------------------------------------+
void GenerateRegistryTestReport()
{
    Print("📋 生成測試報告...");

    RegistryAdvanceTestRunner* runner = new RegistryAdvanceTestRunner();
    runner.RunAllTests();

    Print("");
    Print("📊 詳細測試報告:");
    Print("   總測試數: " + IntegerToString(runner.GetTotalTests()));
    Print("   通過測試: " + IntegerToString(runner.GetPassedTests()));
    Print("   失敗測試: " + IntegerToString(runner.GetFailedTests()));
    Print("   成功率: " + DoubleToString(
        runner.GetTotalTests() > 0 ?
        (double)runner.GetPassedTests() / runner.GetTotalTests() * 100.0 : 0.0, 2) + "%");

    Print("");
    Print("📅 報告生成時間: " + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS));
    Print("🏷️ 模組版本: RegistryAdvance v1.0");
    Print("🔧 測試框架: EA_Wizard TestFramework v1.0");

    Print("");
    Print("🎯 測試覆蓋範圍:");
    Print("   • RegistryItem: 數據模型和完整性");
    Print("   • RegistryResult: 結果處理和錯誤報告");
    Print("   • ItemRegistry: 核心註冊功能");
    Print("   • Integration: 複雜場景和性能");

    delete runner;
}

//+------------------------------------------------------------------+
//| 開發者測試函數 - 用於開發過程中的快速測試                         |
//+------------------------------------------------------------------+
void DevQuickRegistryTest()
{
    Print("🛠️ 開發者快速測試...");

    // 只運行最關鍵的測試
    RunSpecificRegistryAdvanceTest("TestRegistryItem");
    RunSpecificRegistryAdvanceTest("TestItemRegistry");

    Print("✅ 開發者快速測試完成");
}

//+------------------------------------------------------------------+
//| 文檔輸出功能函數                                                 |
//+------------------------------------------------------------------+

// 運行所有測試並生成完整文檔
void RunAllRegistryAdvanceTestsWithDocs()
{
    Print("📄 開始執行 RegistryAdvance 測試並生成文檔...");

    RegistryAdvanceTestRunnerWithDocs* runner = new RegistryAdvanceTestRunnerWithDocs();
    runner.RunAllTestsWithDocs();
    delete runner;

    Print("✅ 測試執行完成，文檔已生成");
}

// 運行單元測試並生成文檔
void RunRegistryAdvanceUnitTestsWithDocs()
{
    Print("📄 開始執行 RegistryAdvance 單元測試並生成文檔...");

    RegistryAdvanceTestRunnerWithDocs* runner = new RegistryAdvanceTestRunnerWithDocs(true, false);
    runner.RunUnitTestsOnlyWithDocs();
    delete runner;

    Print("✅ 單元測試執行完成，文檔已生成");
}

// 運行整合測試並生成文檔
void RunRegistryAdvanceIntegrationTestsWithDocs()
{
    Print("📄 開始執行 RegistryAdvance 整合測試並生成文檔...");

    RegistryAdvanceTestRunnerWithDocs* runner = new RegistryAdvanceTestRunnerWithDocs(false, true);
    runner.RunIntegrationTestsOnlyWithDocs();
    delete runner;

    Print("✅ 整合測試執行完成，文檔已生成");
}

// 運行特定測試並生成文檔
void RunSpecificRegistryAdvanceTestWithDocs(string testClassName)
{
    Print("📄 開始執行 " + testClassName + " 測試並生成文檔...");

    RegistryAdvanceTestRunnerWithDocs* runner = new RegistryAdvanceTestRunnerWithDocs();
    runner.RunSpecificTestWithDocs(testClassName);
    delete runner;

    Print("✅ " + testClassName + " 測試執行完成，文檔已生成");
}

// 自定義文檔輸出選項
void RunRegistryAdvanceTestsWithCustomDocs(bool generateFullReport = true,
                                           bool generateSummary = true,
                                           string outputDirectory = "RegistryAdvance_TestReports")
{
    Print("📄 開始執行自定義文檔輸出的 RegistryAdvance 測試...");

    RegistryAdvanceTestRunnerWithDocs* runner = new RegistryAdvanceTestRunnerWithDocs();
    runner.SetDocumentOptions(generateFullReport, generateSummary, outputDirectory);
    runner.RunAllTestsWithDocs();
    delete runner;

    Print("✅ 自定義測試文檔已生成到: " + outputDirectory);
}

// 自定義文檔輸出選項（包含通過測試顯示設置）
void RunRegistryAdvanceTestsWithAdvancedDocs(bool generateFullReport = true,
                                             bool generateSummary = true,
                                             string outputDirectory = "RegistryAdvance_TestReports",
                                             int maxPassedTestsDisplay = 10)
{
    Print("📄 開始執行高級自定義文檔輸出的 RegistryAdvance 測試...");

    RegistryAdvanceTestRunnerWithDocs* runner = new RegistryAdvanceTestRunnerWithDocs();
    runner.SetDocumentOptions(generateFullReport, generateSummary, outputDirectory);
    runner.SetPassedTestsDisplayOptions(maxPassedTestsDisplay);
    runner.RunAllTestsWithDocs();
    delete runner;

    string displayInfo = (maxPassedTestsDisplay == -1) ? "無限制" : IntegerToString(maxPassedTestsDisplay);
    Print("✅ 高級自定義測試文檔已生成到: " + outputDirectory);
    Print("   通過測試顯示設置: " + displayInfo);
}

// 運行測試並生成限制顯示的文檔
void RunRegistryAdvanceTestsWithLimitedDisplay(int maxDisplay = 5,
                                               string outputDirectory = "RegistryAdvance_LimitedDisplay")
{
    Print("📄 開始執行限制顯示的 RegistryAdvance 測試...");

    RegistryAdvanceTestRunnerWithDocs* runner = new RegistryAdvanceTestRunnerWithDocs();
    runner.SetDocumentOptions(true, true, outputDirectory);
    runner.SetPassedTestsDisplayOptions(maxDisplay);
    runner.RunAllTestsWithDocs();
    delete runner;

    Print("✅ 限制顯示測試文檔已生成到: " + outputDirectory);
    Print("   最多顯示 " + IntegerToString(maxDisplay) + " 個通過的測試");
}

// 運行測試並生成無限制顯示的文檔
void RunRegistryAdvanceTestsWithUnlimitedDisplay(string outputDirectory = "RegistryAdvance_UnlimitedDisplay")
{
    Print("📄 開始執行無限制顯示的 RegistryAdvance 測試...");

    RegistryAdvanceTestRunnerWithDocs* runner = new RegistryAdvanceTestRunnerWithDocs();
    runner.SetDocumentOptions(true, true, outputDirectory);
    runner.SetUnlimitedPassedTestsDisplay();
    runner.RunAllTestsWithDocs();
    delete runner;

    Print("✅ 無限制顯示測試文檔已生成到: " + outputDirectory);
    Print("   所有通過的測試都會完整顯示");
}

//+------------------------------------------------------------------+
//| 主要導出函數 - 供外部調用                                         |
//+------------------------------------------------------------------+

// 標準測試入口
void TestRegistryAdvance()
{
    RunRegistryAdvanceTests();
}

// 標準測試入口（含文檔輸出）
void TestRegistryAdvanceWithDocs()
{
    RunAllRegistryAdvanceTestsWithDocs();
}

// 完整測試套件
void FullRegistryAdvanceTestSuite()
{
    ValidateRegistryTestEnvironment();
    RunRegistryAdvanceTests();
    RunRegistryPerformanceTests();
    GenerateRegistryTestReport();
}

// 完整測試套件（含文檔輸出）
void FullRegistryAdvanceTestSuiteWithDocs()
{
    Print("🚀 開始執行完整的 RegistryAdvance 測試套件（含文檔輸出）...");

    ValidateRegistryTestEnvironment();
    RunAllRegistryAdvanceTestsWithDocs();
    RunRegistryPerformanceTests();

    Print("✅ 完整測試套件執行完成，所有文檔已生成");
}

// 持續整合測試
void CIRegistryAdvanceTests()
{
    Print("🔄 持續整合測試開始...");

    if(!ValidateRegistryTestEnvironment())
    {
        Print("❌ 環境驗證失敗，終止測試");
        return;
    }

    bool result = QuickRegistryAdvanceCheck();

    if(result)
    {
        Print("✅ CI測試通過 - 構建可以繼續");
    }
    else
    {
        Print("❌ CI測試失敗 - 構建應該停止");
    }
}

// 壓力測試
void RegistryStressTest()
{
    Print("💪 開始壓力測試...");

    ItemRegistry<string>* stressRegistry = new ItemRegistry<string>("壓力測試", "StressTest", 10000);

    uint startTime = GetTickCount();
    int successCount = 0;

    // 大量註冊操作
    for(int i = 0; i < 1000; i++)
    {
        string key = StringFormat("stress_item_%d", i);
        string value = StringFormat("壓力測試值_%d", i);
        RegistryResult<string>* result = stressRegistry.Register(key, "壓力測試項目", value);

        if(result.IsSuccess())
            successCount++;

        delete result;
    }

    uint duration = GetTickCount() - startTime;

    Print("💪 壓力測試結果:");
    Print("   註冊1000個項目耗時: " + IntegerToString(duration) + " ms");
    Print("   成功註冊數量: " + IntegerToString(successCount));
    Print("   最終註冊器大小: " + IntegerToString(stressRegistry.Size()));

    delete stressRegistry;
}
