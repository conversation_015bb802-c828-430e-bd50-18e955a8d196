//+------------------------------------------------------------------+
//|                                      TestRunnerWithDocuments.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "TestFramework.mqh"
#include "TestRunnerAdvanced.mqh"
#include "TestDocumentGeneratorFixed.mqh"
#include "unit/TestRegistryItem.mqh"
#include "unit/TestRegistryResult.mqh"
#include "unit/TestItemRegistry.mqh"
#include "integration/TestRegistryIntegration.mqh"

//+------------------------------------------------------------------+
//| 支援文檔輸出的 RegistryAdvance 測試運行器                        |
//+------------------------------------------------------------------+
class RegistryAdvanceTestRunnerWithDocs
{
private:
    TestRunnerAdvanced* m_runner;                // 增強版測試運行器
    TestDocumentGeneratorFixed* m_docGenerator;  // 文檔生成器
    bool m_runUnitTests;                    // 是否運行單元測試
    bool m_runIntegrationTests;             // 是否運行整合測試
    bool m_verbose;                         // 是否詳細輸出
    bool m_generateDocs;                    // 是否生成文檔
    bool m_generateSummary;                 // 是否生成摘要
    string m_outputDirectory;               // 輸出目錄

public:
    // 構造函數
    RegistryAdvanceTestRunnerWithDocs(bool runUnitTests = true,
                                     bool runIntegrationTests = true,
                                     bool verbose = true,
                                     bool generateDocs = true,
                                     bool generateSummary = true,
                                     string outputDir = "TestReports")
    : m_runUnitTests(runUnitTests), m_runIntegrationTests(runIntegrationTests),
      m_verbose(verbose), m_generateDocs(generateDocs), m_generateSummary(generateSummary),
      m_outputDirectory(outputDir)
    {
        m_runner = new TestRunnerAdvanced(true);
        m_docGenerator = new TestDocumentGeneratorFixed(outputDir, "RegistryAdvance_TestReport", true, true);
    }

    // 析構函數
    ~RegistryAdvanceTestRunnerWithDocs()
    {
        if(m_runner != NULL)
        {
            delete m_runner;
            m_runner = NULL;
        }

        if(m_docGenerator != NULL)
        {
            delete m_docGenerator;
            m_docGenerator = NULL;
        }
    }

    // 運行所有測試並生成文檔
    void RunAllTestsWithDocs()
    {
        PrintHeader();

        if(m_runUnitTests)
        {
            RunUnitTests();
        }

        if(m_runIntegrationTests)
        {
            RunIntegrationTests();
        }

        PrintSummary();
        GenerateDocuments();
    }

    // 只運行單元測試並生成文檔
    void RunUnitTestsOnlyWithDocs()
    {
        PrintHeader();
        RunUnitTests();
        PrintSummary();
        GenerateDocuments();
    }

    // 只運行整合測試並生成文檔
    void RunIntegrationTestsOnlyWithDocs()
    {
        PrintHeader();
        RunIntegrationTests();
        PrintSummary();
        GenerateDocuments();
    }

    // 運行特定測試並生成文檔
    void RunSpecificTestWithDocs(string testClassName)
    {
        PrintHeader();

        if(m_verbose)
        {
            Print("🎯 運行特定測試: " + testClassName);
            Print("");
        }

        RunSpecificTest(testClassName);
        PrintSummary();
        GenerateDocuments();
    }

private:
    // 打印標題
    void PrintHeader()
    {
        if(m_verbose)
        {
            Print("================================================================");
            Print("              RegistryAdvance 測試套件（含文檔輸出）");
            Print("                        EA_Wizard");
            Print("================================================================");
            Print("");
        }
    }

    // 運行單元測試
    void RunUnitTests()
    {
        if(m_verbose)
        {
            Print("🧪 開始運行單元測試...");
            Print("=====================================");
        }

        // 運行RegistryItem測試
        TestRegistryItem* itemTest = new TestRegistryItem(m_runner);
        m_runner.RunTestCase(itemTest);
        delete itemTest;

        // 運行RegistryResult測試
        TestRegistryResult* resultTest = new TestRegistryResult(m_runner);
        m_runner.RunTestCase(resultTest);
        delete resultTest;

        // 運行ItemRegistry測試
        TestItemRegistry* registryTest = new TestItemRegistry(m_runner);
        m_runner.RunTestCase(registryTest);
        delete registryTest;

        if(m_verbose)
        {
            Print("=====================================");
            Print("✅ 單元測試完成");
            Print("");
        }
    }

    // 運行整合測試
    void RunIntegrationTests()
    {
        if(m_verbose)
        {
            Print("🔗 開始運行整合測試...");
            Print("=====================================");
        }

        // 運行整合測試
        TestRegistryIntegration* integrationTest = new TestRegistryIntegration(m_runner);
        m_runner.RunTestCase(integrationTest);
        delete integrationTest;

        if(m_verbose)
        {
            Print("=====================================");
            Print("✅ 整合測試完成");
            Print("");
        }
    }

    // 運行特定測試
    void RunSpecificTest(string testClassName)
    {
        if(testClassName == "TestRegistryItem")
        {
            TestRegistryItem* test = new TestRegistryItem(m_runner);
            m_runner.RunTestCase(test);
            delete test;
        }
        else if(testClassName == "TestRegistryResult")
        {
            TestRegistryResult* test = new TestRegistryResult(m_runner);
            m_runner.RunTestCase(test);
            delete test;
        }
        else if(testClassName == "TestItemRegistry")
        {
            TestItemRegistry* test = new TestItemRegistry(m_runner);
            m_runner.RunTestCase(test);
            delete test;
        }
        else if(testClassName == "TestRegistryIntegration")
        {
            TestRegistryIntegration* test = new TestRegistryIntegration(m_runner);
            m_runner.RunTestCase(test);
            delete test;
        }
        else
        {
            Print("❌ 未知的測試類別: " + testClassName);
        }
    }

    // 打印摘要
    void PrintSummary()
    {
        if(m_verbose)
        {
            m_runner.PrintSummary();
        }
    }

    // 生成文檔
    void GenerateDocuments()
    {
        if(!m_generateDocs && !m_generateSummary)
        {
            if(m_verbose)
            {
                Print("📄 跳過文檔生成（已禁用）");
            }
            return;
        }

        if(m_verbose)
        {
            Print("📄 開始生成測試文檔...");
        }

        bool success = true;

        if(m_generateDocs)
        {
            success &= m_docGenerator.GenerateAdvancedTestReport(m_runner, "RegistryAdvance");
        }

        if(m_generateSummary)
        {
            success &= m_docGenerator.GenerateTestSummary(m_runner, "RegistryAdvance");
        }

        if(m_verbose)
        {
            if(success)
            {
                Print("📁 所有文檔已保存到: " + m_outputDirectory + " 目錄");
                Print("💡 提示: 文檔保存在 MQL4\\Files\\" + m_outputDirectory + " 目錄中");
            }
            else
            {
                Print("⚠️ 部分文檔生成失敗，請檢查文件權限和磁盤空間");
            }
        }
    }

public:
    // 設置文檔生成選項
    void SetDocumentOptions(bool generateDocs, bool generateSummary, string outputDir = "")
    {
        m_generateDocs = generateDocs;
        m_generateSummary = generateSummary;

        if(outputDir != "")
        {
            m_outputDirectory = outputDir;
            m_docGenerator.SetOutputDirectory(outputDir);
        }
    }

    // 設置通過測試顯示選項
    void SetPassedTestsDisplayOptions(int maxDisplay)
    {
        if(m_docGenerator != NULL)
        {
            m_docGenerator.SetMaxPassedTestsDisplay(maxDisplay);
        }
    }

    // 設置無限制顯示通過測試
    void SetUnlimitedPassedTestsDisplay()
    {
        if(m_docGenerator != NULL)
        {
            m_docGenerator.SetUnlimitedPassedTestsDisplay();
        }
    }

    // 獲取測試運行器（用於外部訪問）
    TestRunnerAdvanced* GetRunner() { return m_runner; }

    // 獲取文檔生成器（用於外部訪問）
    TestDocumentGeneratorFixed* GetDocumentGenerator() { return m_docGenerator; }
};
