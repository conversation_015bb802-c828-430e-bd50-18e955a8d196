//+------------------------------------------------------------------+
//|                                            ConfigurationTest.mq4 |
//|                                                       EA_Wizard  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "ConfigurationExample.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                     |
//+------------------------------------------------------------------+
void OnStart()
{
    // 顯示測試開始信息
    Print("===== 配置模組測試開始 =====");
    
    // 運行基本配置示例
    ConfigurationExample::BasicExample();
    
    // 運行更新配置示例
    ConfigurationExample::UpdateExample();
    
    // 運行重置配置示例
    ConfigurationExample::ResetExample();
    
    // 顯示測試結束信息
    Print("===== 配置模組測試結束 =====");
}
