//+------------------------------------------------------------------+
//|                                          TestItemRegistry.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../ItemRegistry.mqh"

//+------------------------------------------------------------------+
//| ItemRegistry測試類別                                             |
//+------------------------------------------------------------------+
class TestItemRegistry : public TestCase
{
private:
    TestRunner* m_runner;    // 測試運行器引用

public:
    // 構造函數
    TestItemRegistry(TestRunner* runner)
    : TestCase("TestItemRegistry"), m_runner(runner) {}

    // 運行所有測試
    void RunTests() override
    {
        TestConstructor();
        TestBasicProperties();
        TestRegisterItem();
        TestUnregisterItem();
        TestFindItem();
        TestMaxItemsLimit();
        TestDuplicateKeys();
        TestClearRegistry();
        TestGetLastRegisteredKey();
        TestDifferentValueTypes();
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        SetUp();

        // 測試默認構造
        ItemRegistry<string>* registry1 = new ItemRegistry<string>();
        m_runner.RecordResult(Assert::AssertNotNull("構造函數_默認參數", registry1));
        m_runner.RecordResult(Assert::AssertEquals("構造函數_默認名稱", "ItemRegistry", registry1.GetName()));
        m_runner.RecordResult(Assert::AssertEquals("構造函數_默認類型", "ItemRegistry", registry1.GetType()));
        m_runner.RecordResult(Assert::AssertEquals("構造函數_默認最大項目數", 100, registry1.GetMaxItems()));

        // 測試自定義參數構造
        ItemRegistry<int>* registry2 = new ItemRegistry<int>("自定義註冊器", "CustomRegistry", 50);
        m_runner.RecordResult(Assert::AssertNotNull("構造函數_自定義參數", registry2));
        m_runner.RecordResult(Assert::AssertEquals("構造函數_自定義名稱", "自定義註冊器", registry2.GetName()));
        m_runner.RecordResult(Assert::AssertEquals("構造函數_自定義類型", "CustomRegistry", registry2.GetType()));
        m_runner.RecordResult(Assert::AssertEquals("構造函數_自定義最大項目數", 50, registry2.GetMaxItems()));

        // 清理
        delete registry1;
        delete registry2;

        TearDown();
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        SetUp();

        ItemRegistry<string>* registry = new ItemRegistry<string>("屬性測試", "PropertyTest", 10);

        m_runner.RecordResult(Assert::AssertEquals("基本屬性_名稱", "屬性測試", registry.GetName()));
        m_runner.RecordResult(Assert::AssertEquals("基本屬性_類型", "PropertyTest", registry.GetType()));
        m_runner.RecordResult(Assert::AssertEquals("基本屬性_最大項目數", 10, registry.GetMaxItems()));
        m_runner.RecordResult(Assert::AssertEquals("基本屬性_初始大小", 0, registry.Size()));
        m_runner.RecordResult(Assert::AssertTrue("基本屬性_初始為空", registry.IsEmpty()));

        delete registry;

        TearDown();
    }

    // 測試註冊項目
    void TestRegisterItem()
    {
        SetUp();

        ItemRegistry<string>* registry = new ItemRegistry<string>("註冊測試", "RegisterTest", 5);

        // 測試註冊第一個項目
        RegistryResult<string>* result1 = registry.Register("item1", "第一個項目", "值1");
        m_runner.RecordResult(Assert::AssertNotNull("註冊項目_結果1不為空", result1));
        m_runner.RecordResult(Assert::AssertTrue("註冊項目_結果1成功", result1.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("註冊項目_結果1鍵", "item1", result1.GetKey()));
        m_runner.RecordResult(Assert::AssertEquals("註冊項目_大小1", 1, registry.Size()));
        m_runner.RecordResult(Assert::AssertFalse("註冊項目_不再為空", registry.IsEmpty()));

        // 測試註冊第二個項目
        RegistryResult<string>* result2 = registry.Register("item2", "第二個項目", "值2");
        m_runner.RecordResult(Assert::AssertTrue("註冊項目_結果2成功", result2.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("註冊項目_大小2", 2, registry.Size()));

        // 測試查找已註冊的項目
        RegistryItem<string>* foundItem = registry.Find("item1");
        m_runner.RecordResult(Assert::AssertNotNull("註冊項目_找到項目1", (void*)foundItem));
        if(foundItem != NULL)
        {
            m_runner.RecordResult(Assert::AssertEquals("註冊項目_項目1名稱", "第一個項目", foundItem.GetName()));
            m_runner.RecordResult(Assert::AssertEquals("註冊項目_項目1值", "值1", foundItem.GetValue()));
        }

        // 清理
        delete result1;
        delete result2;
        delete registry;

        TearDown();
    }

    // 測試取消註冊項目
    void TestUnregisterItem()
    {
        SetUp();

        ItemRegistry<int>* registry = new ItemRegistry<int>("取消註冊測試", "UnregisterTest");

        // 先註冊一些項目
        registry.Register("item1", "項目1", 100);
        registry.Register("item2", "項目2", 200);
        registry.Register("item3", "項目3", 300);

        m_runner.RecordResult(Assert::AssertEquals("取消註冊前_大小", 3, registry.Size()));

        // 測試取消註冊存在的項目
        bool unregResult1 = registry.Unregister("item2");
        m_runner.RecordResult(Assert::AssertTrue("取消註冊_成功", unregResult1));
        m_runner.RecordResult(Assert::AssertEquals("取消註冊後_大小", 2, registry.Size()));

        // 確認項目已被移除
        RegistryItem<int>* notFound = registry.Find("item2");
        m_runner.RecordResult(Assert::AssertNull("取消註冊_項目已移除", (void*)notFound));

        // 測試取消註冊不存在的項目
        bool unregResult2 = registry.Unregister("nonexistent");
        m_runner.RecordResult(Assert::AssertFalse("取消註冊_不存在項目失敗", unregResult2));
        m_runner.RecordResult(Assert::AssertEquals("取消註冊不存在後_大小不變", 2, registry.Size()));

        // 確認其他項目仍然存在
        RegistryItem<int>* stillExists = registry.Find("item1");
        m_runner.RecordResult(Assert::AssertNotNull("取消註冊_其他項目仍存在", (void*)stillExists));

        delete registry;

        TearDown();
    }

    // 測試查找項目
    void TestFindItem()
    {
        SetUp();

        ItemRegistry<double>* registry = new ItemRegistry<double>("查找測試", "FindTest");

        // 註冊一些項目
        registry.Register("pi", "圓周率", 3.14159);
        registry.Register("e", "自然常數", 2.71828);
        registry.Register("golden", "黃金比例", 1.618);

        // 測試查找存在的項目
        RegistryItem<double>* piItem = registry.Find("pi");
        m_runner.RecordResult(Assert::AssertNotNull("查找項目_找到pi", (void*)piItem));
        if(piItem != NULL)
        {
            m_runner.RecordResult(Assert::AssertEquals("查找項目_pi名稱", "圓周率", piItem.GetName()));
            m_runner.RecordResult(Assert::AssertTrue("查找項目_pi值",
                MathAbs(piItem.GetValue() - 3.14159) < 0.00001));
        }

        // 測試查找不存在的項目
        RegistryItem<double>* notFound = registry.Find("nonexistent");
        m_runner.RecordResult(Assert::AssertNull("查找項目_不存在項目", (void*)notFound));

        // 測試查找空鍵
        RegistryItem<double>* emptyKey = registry.Find("");
        m_runner.RecordResult(Assert::AssertNull("查找項目_空鍵", (void*)emptyKey));

        delete registry;

        TearDown();
    }

    // 測試最大項目數限制
    void TestMaxItemsLimit()
    {
        SetUp();

        ItemRegistry<string>* registry = new ItemRegistry<string>("限制測試", "LimitTest", 3);

        // 註冊到限制數量
        RegistryResult<string>* result1 = registry.Register("item1", "項目1", "值1");
        RegistryResult<string>* result2 = registry.Register("item2", "項目2", "值2");
        RegistryResult<string>* result3 = registry.Register("item3", "項目3", "值3");

        m_runner.RecordResult(Assert::AssertTrue("限制測試_項目1成功", result1.IsSuccess()));
        m_runner.RecordResult(Assert::AssertTrue("限制測試_項目2成功", result2.IsSuccess()));
        m_runner.RecordResult(Assert::AssertTrue("限制測試_項目3成功", result3.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("限制測試_達到限制", 3, registry.Size()));

        // 嘗試超過限制
        RegistryResult<string>* result4 = registry.Register("item4", "項目4", "值4");
        m_runner.RecordResult(Assert::AssertFalse("限制測試_超過限制失敗", result4.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("限制測試_超過限制後大小不變", 3, registry.Size()));
        m_runner.RecordResult(Assert::AssertContains("限制測試_錯誤消息", result4.GetMessage(), "最大項目數量"));

        // 清理
        delete result1;
        delete result2;
        delete result3;
        delete result4;
        delete registry;

        TearDown();
    }

    // 測試重複鍵
    void TestDuplicateKeys()
    {
        SetUp();

        ItemRegistry<int>* registry = new ItemRegistry<int>("重複鍵測試", "DuplicateTest");

        // 註冊第一個項目
        RegistryResult<string>* result1 = registry.Register("duplicate", "第一個項目", 100);
        m_runner.RecordResult(Assert::AssertTrue("重複鍵_第一次註冊成功", result1.IsSuccess()));

        // 嘗試註冊相同鍵的項目
        RegistryResult<string>* result2 = registry.Register("duplicate", "第二個項目", 200);
        m_runner.RecordResult(Assert::AssertFalse("重複鍵_第二次註冊失敗", result2.IsSuccess()));
        m_runner.RecordResult(Assert::AssertContains("重複鍵_錯誤消息", result2.GetMessage(), "鍵已存在"));
        m_runner.RecordResult(Assert::AssertEquals("重複鍵_大小不變", 1, registry.Size()));

        // 確認原項目未被覆蓋
        RegistryItem<int>* originalItem = registry.Find("duplicate");
        m_runner.RecordResult(Assert::AssertNotNull("重複鍵_原項目仍存在", (void*)originalItem));
        if(originalItem != NULL)
        {
            m_runner.RecordResult(Assert::AssertEquals("重複鍵_原項目值未變", 100, originalItem.GetValue()));
            m_runner.RecordResult(Assert::AssertEquals("重複鍵_原項目名稱未變", "第一個項目", originalItem.GetName()));
        }

        // 清理
        delete result1;
        delete result2;
        delete registry;

        TearDown();
    }

    // 測試清空註冊器
    void TestClearRegistry()
    {
        SetUp();

        ItemRegistry<string>* registry = new ItemRegistry<string>("清空測試", "ClearTest");

        // 註冊一些項目
        registry.Register("item1", "項目1", "值1");
        registry.Register("item2", "項目2", "值2");
        registry.Register("item3", "項目3", "值3");

        m_runner.RecordResult(Assert::AssertEquals("清空前_大小", 3, registry.Size()));
        m_runner.RecordResult(Assert::AssertFalse("清空前_不為空", registry.IsEmpty()));

        // 清空註冊器
        registry.Clear();

        m_runner.RecordResult(Assert::AssertEquals("清空後_大小", 0, registry.Size()));
        m_runner.RecordResult(Assert::AssertTrue("清空後_為空", registry.IsEmpty()));

        // 確認項目已被移除
        RegistryItem<string>* notFound = registry.Find("item1");
        m_runner.RecordResult(Assert::AssertNull("清空後_項目已移除", (void*)notFound));

        delete registry;

        TearDown();
    }

    // 測試獲取最後註冊的鍵
    void TestGetLastRegisteredKey()
    {
        SetUp();

        ItemRegistry<bool>* registry = new ItemRegistry<bool>("最後鍵測試", "LastKeyTest");

        // 註冊項目並檢查最後註冊的鍵
        registry.Register("first", "第一個", true);
        m_runner.RecordResult(Assert::AssertEquals("最後鍵_第一個", "first", registry.GetLastRegisteredKey()));

        registry.Register("second", "第二個", false);
        m_runner.RecordResult(Assert::AssertEquals("最後鍵_第二個", "second", registry.GetLastRegisteredKey()));

        registry.Register("third", "第三個", true);
        m_runner.RecordResult(Assert::AssertEquals("最後鍵_第三個", "third", registry.GetLastRegisteredKey()));

        // 測試註冊失敗時最後鍵不變
        string lastKeyBeforeFail = registry.GetLastRegisteredKey();
        RegistryResult<string>* failResult = registry.Register("third", "重複", false); // 重複鍵
        m_runner.RecordResult(Assert::AssertFalse("最後鍵_註冊失敗", failResult.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("最後鍵_失敗後不變", lastKeyBeforeFail, registry.GetLastRegisteredKey()));

        // 清理
        delete failResult;
        delete registry;

        TearDown();
    }

    // 測試不同值類型
    void TestDifferentValueTypes()
    {
        SetUp();

        // 測試字符串值類型
        ItemRegistry<string>* stringRegistry = new ItemRegistry<string>("字符串註冊器", "StringRegistry");
        RegistryResult<string>* stringResult = stringRegistry.Register("str_key", "字符串項目", "字符串值");
        m_runner.RecordResult(Assert::AssertTrue("不同類型_字符串註冊成功", stringResult.IsSuccess()));

        RegistryItem<string>* stringItem = stringRegistry.Find("str_key");
        m_runner.RecordResult(Assert::AssertEquals("不同類型_字符串值", "字符串值", stringItem.GetValue()));

        // 測試整數值類型
        ItemRegistry<int>* intRegistry = new ItemRegistry<int>("整數註冊器", "IntRegistry");
        RegistryResult<string>* intResult = intRegistry.Register("int_key", "整數項目", 42);
        m_runner.RecordResult(Assert::AssertTrue("不同類型_整數註冊成功", intResult.IsSuccess()));

        RegistryItem<int>* intItem = intRegistry.Find("int_key");
        m_runner.RecordResult(Assert::AssertEquals("不同類型_整數值", 42, intItem.GetValue()));

        // 測試雙精度值類型
        ItemRegistry<double>* doubleRegistry = new ItemRegistry<double>("雙精度註冊器", "DoubleRegistry");
        RegistryResult<string>* doubleResult = doubleRegistry.Register("double_key", "雙精度項目", 3.14159);
        m_runner.RecordResult(Assert::AssertTrue("不同類型_雙精度註冊成功", doubleResult.IsSuccess()));

        RegistryItem<double>* doubleItem = doubleRegistry.Find("double_key");
        m_runner.RecordResult(Assert::AssertTrue("不同類型_雙精度值",
            MathAbs(doubleItem.GetValue() - 3.14159) < 0.00001));

        // 清理
        delete stringResult;
        delete stringRegistry;
        delete intResult;
        delete intRegistry;
        delete doubleResult;
        delete doubleRegistry;

        TearDown();
    }
};
