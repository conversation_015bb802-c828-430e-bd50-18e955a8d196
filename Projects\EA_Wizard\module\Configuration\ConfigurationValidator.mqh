//+------------------------------------------------------------------+
//| Module: Configuration/ConfigurationValidator.mqh                 |
//| This file is part of the EA_Wizard project.                      |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

// 防止重複包含
#ifndef _CONFIGURATION_VALIDATOR_MQH_
#define _CONFIGURATION_VALIDATOR_MQH_

#include "ConfigurationTypes.mqh"
#include "ConfigurationSchema.mqh"
#include "../mql4-lib-master/Format/Json.mqh"
#include "JsonHelper.mqh"

//+------------------------------------------------------------------+
//| 配置驗證器類                                                      |
//+------------------------------------------------------------------+
class ConfigurationValidator
{
private:
    static string m_lastError;  // 最後一個錯誤消息

public:
    /**
     * 驗證配置對象是否符合架構
     * @param config 配置對象
     * @param schema 架構對象
     * @return 是否驗證通過
     */
    static bool validate(JsonObject* config, JsonObject* schema)
    {
        if(config == NULL || schema == NULL)
        {
            m_lastError = "配置或架構對象為空";
            return false;
        }

        // 獲取架構中的所有鍵
        JsonArray* schemaKeys = schema.keys();
        if(schemaKeys == NULL)
        {
            m_lastError = "無法獲取架構鍵";
            return false;
        }

        // 遍歷所有架構鍵
        for(int i = 0; i < schemaKeys.length(); i++)
        {
            JsonString* keyObj = dynamic_cast<JsonString*>(schemaKeys[i]);
            if(keyObj == NULL) continue;

            string key = keyObj.value;
            JsonObject* schemaItem = schema.getObject(key);
            if(schemaItem == NULL) continue;

            // 檢查必填項
            bool required = JsonHelper::getBoolean(schemaItem, "required", false);
            if(required && !config.contains(key))
            {
                m_lastError = StringFormat("缺少必填項: %s", key);
                return false;
            }

            // 如果配置中不存在該鍵，則跳過（非必填項）
            if(!config.contains(key)) continue;

            // 檢查類型
            string expectedType = JsonHelper::getString(schemaItem, "type", "");
            JsonValue* value = config[key];

            if(!validateType(value, expectedType))
            {
                m_lastError = StringFormat("類型錯誤: %s 應為 %s 類型", key, expectedType);
                return false;
            }

            // 檢查嵌套對象
            if(expectedType == "object" && schemaItem.contains("properties"))
            {
                JsonObject* nestedConfig = dynamic_cast<JsonObject*>(value);
                JsonObject* nestedSchema = schemaItem.getObject("properties");

                if(nestedConfig != NULL && nestedSchema != NULL)
                {
                    if(!validate(nestedConfig, nestedSchema))
                    {
                        // 錯誤消息已在遞歸調用中設置
                        return false;
                    }
                }
            }

            // 檢查數組項
            if(expectedType == "array" && schemaItem.contains("items"))
            {
                JsonArray* arrayConfig = dynamic_cast<JsonArray*>(value);
                JsonObject* itemSchema = schemaItem.getObject("items");

                if(arrayConfig != NULL && itemSchema != NULL)
                {
                    for(int j = 0; j < arrayConfig.length(); j++)
                    {
                        JsonValue* item = arrayConfig[j];
                        string itemType = JsonHelper::getString(itemSchema, "type", "");

                        if(!validateType(item, itemType))
                        {
                            m_lastError = StringFormat("數組項類型錯誤: %s[%d] 應為 %s 類型", key, j, itemType);
                            return false;
                        }

                        // 如果數組項是對象，則遞歸驗證
                        if(itemType == "object" && itemSchema.contains("properties"))
                        {
                            JsonObject* itemConfig = dynamic_cast<JsonObject*>(item);
                            JsonObject* itemProperties = itemSchema.getObject("properties");

                            if(itemConfig != NULL && itemProperties != NULL)
                            {
                                if(!validate(itemConfig, itemProperties))
                                {
                                    // 錯誤消息已在遞歸調用中設置
                                    return false;
                                }
                            }
                        }
                    }
                }
            }
        }

        return true;
    }

    /**
     * 應用默認值到配置對象
     * @param config 配置對象
     * @param schema 架構對象
     */
    static void applyDefaults(JsonObject* config, JsonObject* schema)
    {
        if(config == NULL || schema == NULL) return;

        // 獲取架構中的所有鍵
        JsonArray* schemaKeys = schema.keys();
        if(schemaKeys == NULL) return;

        // 遍歷所有架構鍵
        for(int i = 0; i < schemaKeys.length(); i++)
        {
            JsonString* keyObj = dynamic_cast<JsonString*>(schemaKeys[i]);
            if(keyObj == NULL) continue;

            string key = keyObj.value;
            JsonObject* schemaItem = schema.getObject(key);
            if(schemaItem == NULL) continue;

            // 如果配置中不存在該鍵，則應用默認值
            if(!config.contains(key) && schemaItem.contains("default"))
            {
                string type = JsonHelper::getString(schemaItem, "type", "");

                if(type == "string")
                {
                    string defaultValue = JsonHelper::getString(schemaItem, "default", "");
                    config.setString(key, defaultValue);
                }
                else if(type == "number")
                {
                    double defaultValue = JsonHelper::getNumber(schemaItem, "default", 0.0);
                    config.setNumber(key, defaultValue);
                }
                else if(type == "boolean")
                {
                    bool defaultValue = JsonHelper::getBoolean(schemaItem, "default", false);
                    config.setBoolean(key, defaultValue);
                }
                else if(type == "object" && schemaItem.contains("properties"))
                {
                    JsonObject* defaultObj = new JsonObject();
                    config.setObject(key, defaultObj);

                    // 遞歸應用默認值
                    JsonObject* nestedSchema = schemaItem.getObject("properties");
                    if(nestedSchema != NULL)
                    {
                        applyDefaults(defaultObj, nestedSchema);
                    }
                }
                else if(type == "array" && schemaItem.contains("default"))
                {
                    JsonArray* defaultArray = schemaItem.getArray("default");
                    if(defaultArray != NULL)
                    {
                        JsonArray* newArray = new JsonArray();
                        ArrayResize(newArray.value, defaultArray.length());

                        for(int j = 0; j < defaultArray.length(); j++)
                        {
                            newArray.value[j] = defaultArray[j];
                        }

                        config.setArray(key, newArray);
                    }
                }
            }

            // 遞歸處理嵌套對象
            if(config.contains(key))
            {
                string type = JsonHelper::getString(schemaItem, "type", "");

                if(type == "object" && schemaItem.contains("properties"))
                {
                    JsonObject* nestedConfig = config.getObject(key);
                    JsonObject* nestedSchema = schemaItem.getObject("properties");

                    if(nestedConfig != NULL && nestedSchema != NULL)
                    {
                        applyDefaults(nestedConfig, nestedSchema);
                    }
                }
                else if(type == "array" && schemaItem.contains("items"))
                {
                    JsonArray* arrayConfig = config.getArray(key);
                    JsonObject* itemSchema = schemaItem.getObject("items");

                    if(arrayConfig != NULL && itemSchema != NULL && JsonHelper::getString(itemSchema, "type", "") == "object")
                    {
                        JsonObject* itemProperties = itemSchema.getObject("properties");

                        if(itemProperties != NULL)
                        {
                            for(int j = 0; j < arrayConfig.length(); j++)
                            {
                                JsonObject* itemConfig = dynamic_cast<JsonObject*>(arrayConfig[j]);

                                if(itemConfig != NULL)
                                {
                                    applyDefaults(itemConfig, itemProperties);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 獲取最後一個錯誤消息
     * @return 錯誤消息
     */
    static string getLastError()
    {
        return m_lastError;
    }

private:
    /**
     * 驗證值的類型是否符合預期
     * @param value 值
     * @param expectedType 預期類型
     * @return 是否符合預期類型
     */
    static bool validateType(JsonValue* value, string expectedType)
    {
        if(value == NULL) return false;

        if(expectedType == "string")
        {
            return CheckPointer(dynamic_cast<JsonString*>(value)) != POINTER_INVALID;
        }
        else if(expectedType == "number")
        {
            return CheckPointer(dynamic_cast<JsonNumber*>(value)) != POINTER_INVALID;
        }
        else if(expectedType == "boolean")
        {
            return CheckPointer(dynamic_cast<JsonBoolean*>(value)) != POINTER_INVALID;
        }
        else if(expectedType == "object")
        {
            return CheckPointer(dynamic_cast<JsonObject*>(value)) != POINTER_INVALID;
        }
        else if(expectedType == "array")
        {
            return CheckPointer(dynamic_cast<JsonArray*>(value)) != POINTER_INVALID;
        }
        else if(expectedType == "null")
        {
            return value == null;
        }

        return false;
    }
};

// 初始化靜態成員
string ConfigurationValidator::m_lastError = "";

#endif // _CONFIGURATION_VALIDATOR_MQH_
