//+------------------------------------------------------------------+
//|                                     EAPipelineStateCompleted.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict


#include "EAPipelineState.mqh"
#include "EAPipelineStatePending.mqh"

//+------------------------------------------------------------------+
//| 流水線已完成狀態 - 實現已完成狀態的行為                           |
//+------------------------------------------------------------------+

class EAPipelineStateCompleted : public EAPipelineState
{
public:
    // 構造函數
    EAPipelineStateCompleted()
    : EAPipelineState(EA_PIPELINE_STATE_COMPLETED, "已完成") {}
    
    // 析構函數
    ~EAPipelineStateCompleted() {}
    
    // 執行流水線
    void Execute(EAPipeline_A* pipeline) override;
    
    // 重置流水線狀態
    void Restore(EAPipeline_A* pipeline) override;
};


void EAPipelineStateCompleted::Execute(EAPipeline_A* pipeline)
{
    // 已完成狀態下不應該再次執行
    Print("警告: 流水線 '", pipeline.GetName(), "' 已經完成執行，不能重複執行");
}


void EAPipelineStateCompleted::Restore(EAPipeline_A* pipeline)
{
    // 將狀態轉換為待執行
    pipeline.SetState(new EAPipelineStatePending());
    pipeline.Restore();
}
