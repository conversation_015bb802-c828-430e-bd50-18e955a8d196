//+------------------------------------------------------------------+
//|                                             EAPipelineSafe.mq4 |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

// 包含安全測試框架
#include "../../Projects/EA_Wizard/module/EAPipeline/test/SafeTestRunner.mqh"
#include "../../Projects/EA_Wizard/module/EAPipeline/components/EACompoundPipeline.mqh"
#include "../../Projects/EA_Wizard/module/EAPipeline/components/EAPipelineManager.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== EAPipeline 安全測試開始 ===");
    
    // 快速安全檢查
    bool quickResult = QuickSafeEAPipelineCheck();
    Print(StringFormat("快速檢查結果: %s", quickResult ? "通過" : "失敗"));
    
    // 完整安全測試
    RunSafeEAPipelineTests();
    
    Print("=== EAPipeline 安全測試完成 ===");
}
