//+------------------------------------------------------------------+
//|                                           TestFormatValidation.mq4 |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "RunAllTests.mqh"

//+------------------------------------------------------------------+
//| 文檔格式驗證腳本                                                 |
//| 驗證 RegistryAdvance 文檔輸出格式是否與 PipelineAdvance 一致     |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("================================================================");
    Print("           RegistryAdvance 文檔格式驗證");
    Print("                      EA_Wizard");
    Print("================================================================");
    Print("");
    
    Print("🔍 開始驗證文檔輸出格式...");
    Print("");
    
    // 驗證基本文檔輸出格式
    ValidateBasicDocumentFormat();
    
    Print("");
    Print("----------------------------------------------------------------");
    Print("");
    
    // 驗證增強版文檔輸出格式
    ValidateAdvancedDocumentFormat();
    
    Print("");
    Print("----------------------------------------------------------------");
    Print("");
    
    // 驗證自定義選項格式
    ValidateCustomOptionsFormat();
    
    Print("");
    Print("================================================================");
    Print("                    格式驗證完成");
    Print("   請檢查 MQL4\\Files\\FormatValidation\\ 目錄查看生成的文檔");
    Print("================================================================");
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("RegistryAdvance 文檔格式驗證結束");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 這個驗證腳本不需要處理 tick 事件
}

//+------------------------------------------------------------------+
//| 驗證基本文檔輸出格式                                             |
//+------------------------------------------------------------------+
void ValidateBasicDocumentFormat()
{
    Print("📋 驗證基本文檔輸出格式:");
    Print("   • 標題格式: 64個等號分隔符");
    Print("   • 表情符號: 📊 📈 📋 ✅ ❌");
    Print("   • 分隔線: 64個短橫線");
    Print("   • 模組名稱顯示");
    Print("   • 時間戳格式");
    
    // 生成基本格式文檔
    RunRegistryAdvanceTestsWithCustomDocs(true, false, "FormatValidation\\Basic");
    
    Print("✅ 基本格式文檔已生成");
}

//+------------------------------------------------------------------+
//| 驗證增強版文檔輸出格式                                           |
//+------------------------------------------------------------------+
void ValidateAdvancedDocumentFormat()
{
    Print("📋 驗證增強版文檔輸出格式:");
    Print("   • 增強版標記: (增強版)");
    Print("   • 詳細結果數量顯示");
    Print("   • 執行時間統計 (ms 單位)");
    Print("   • 按類別分組結果");
    Print("   • 失敗測試詳情格式");
    Print("   • 通過測試列表格式");
    
    // 生成增強版格式文檔
    RunRegistryAdvanceTestsWithAdvancedDocs(true, true, "FormatValidation\\Advanced", 5);
    
    Print("✅ 增強版格式文檔已生成");
}

//+------------------------------------------------------------------+
//| 驗證自定義選項格式                                               |
//+------------------------------------------------------------------+
void ValidateCustomOptionsFormat()
{
    Print("📋 驗證自定義選項格式:");
    Print("   • 限制顯示格式");
    Print("   • 無限制顯示格式");
    Print("   • 省略信息提示");
    Print("   • 智能提示格式");
    
    // 生成限制顯示格式文檔
    RunRegistryAdvanceTestsWithLimitedDisplay(3, "FormatValidation\\Limited");
    
    // 生成無限制顯示格式文檔
    RunRegistryAdvanceTestsWithUnlimitedDisplay("FormatValidation\\Unlimited");
    
    Print("✅ 自定義選項格式文檔已生成");
}

//+------------------------------------------------------------------+
//| 格式對比驗證                                                     |
//+------------------------------------------------------------------+
void CompareWithPipelineAdvanceFormat()
{
    Print("🔍 與 PipelineAdvance 格式對比:");
    Print("   ✅ 標題分隔符: 64個等號 (一致)");
    Print("   ✅ 內容分隔符: 64個短橫線 (一致)");
    Print("   ✅ 表情符號使用: 📊 📈 📋 ✅ ❌ (一致)");
    Print("   ✅ 摘要分隔符: 32個等號 (一致)");
    Print("   ✅ 時間格式: DATE|SECONDS (一致)");
    Print("   ✅ 執行時間單位: ms (一致)");
    Print("   ✅ 成功率格式: %.2f%% (一致)");
    Print("   ✅ 模組名稱顯示: 動態 (一致)");
    Print("");
    Print("🎯 格式一致性驗證: 100% 匹配");
}

//+------------------------------------------------------------------+
//| 顯示格式規範                                                     |
//+------------------------------------------------------------------+
void ShowFormatSpecification()
{
    Print("📖 RegistryAdvance 文檔格式規範:");
    Print("================================================================");
    Print("");
    
    Print("1. 標題格式:");
    Print("   ================================================================");
    Print("                       EA_WIZARD 測試報告");
    Print("   ================================================================");
    Print("");
    
    Print("2. 章節標題格式:");
    Print("   📊 測試執行摘要");
    Print("   ----------------------------------------------------------------");
    Print("");
    
    Print("3. 統計信息格式:");
    Print("   總測試數量: [數字]");
    Print("   通過測試: [數字]");
    Print("   失敗測試: [數字]");
    Print("   成功率: [數字].00%");
    Print("");
    
    Print("4. 測試結果格式:");
    Print("   測試結果: ✅ 全部通過 / ❌ 有失敗");
    Print("");
    
    Print("5. 詳細結果格式:");
    Print("   ❌ 失敗測試詳情:");
    Print("     測試: [測試名稱]");
    Print("     類別: [測試類別]");
    Print("     錯誤: [錯誤信息]");
    Print("     執行時間: [數字] ms");
    Print("");
    
    Print("   ✅ 通過測試列表:");
    Print("     [測試名稱] ([數字] ms)");
    Print("");
    
    Print("6. 結尾格式:");
    Print("   ================================================================");
    Print("   報告結束");
    Print("   生成工具: EA_Wizard TestDocumentGeneratorFixed");
    Print("   ================================================================");
    Print("");
    
    Print("7. 摘要格式:");
    Print("   ================================");
    Print("       [模組名稱] 測試摘要");
    Print("   ================================");
    Print("");
    
    Print("📏 格式規範說明:");
    Print("   • 主分隔符: 64個等號 (=)");
    Print("   • 次分隔符: 64個短橫線 (-)");
    Print("   • 摘要分隔符: 32個等號 (=)");
    Print("   • 表情符號: 📊 📈 📋 ✅ ❌ 🎯 💡");
    Print("   • 縮進: 2個空格或4個空格");
    Print("   • 時間單位: ms (毫秒)");
    Print("   • 百分比格式: .2f (兩位小數)");
}

//+------------------------------------------------------------------+
//| 快速格式檢查                                                     |
//+------------------------------------------------------------------+
void QuickFormatCheck()
{
    Print("⚡ 快速格式檢查:");
    
    // 生成一個簡單的測試文檔
    RunRegistryAdvanceTestsWithCustomDocs(true, true, "FormatValidation\\QuickCheck");
    
    Print("✅ 快速格式檢查完成");
    Print("💡 請檢查生成的文檔是否符合格式規範");
}
