//+------------------------------------------------------------------+
//|                                        TestDocumentGenerator.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "TestRunnerAdvanced.mqh"

//+------------------------------------------------------------------+
//| 測試文檔生成器 - 生成測試結果文檔                                 |
//+------------------------------------------------------------------+
class TestDocumentGenerator
{
private:
    string m_outputDirectory;        // 輸出目錄
    bool m_generateFullReport;       // 是否生成完整報告
    bool m_generateSummary;          // 是否生成摘要
    int m_maxDisplayItems;           // 最大顯示項目數（-1為無限制）
    
public:
    // 構造函數
    TestDocumentGenerator(string outputDir = "TestReports\\EAPipeline", 
                         bool fullReport = true, 
                         bool summary = true,
                         int maxDisplay = 10)
        : m_outputDirectory(outputDir), 
          m_generateFullReport(fullReport), 
          m_generateSummary(summary),
          m_maxDisplayItems(maxDisplay)
    {
    }
    
    // 生成測試文檔
    bool GenerateDocuments(TestRunnerAdvanced* runner)
    {
        if(runner == NULL || !runner.IsCollectingDetails())
        {
            Print("錯誤: 測試運行器為空或未收集詳細信息");
            return false;
        }
        
        bool success = true;
        
        // 生成完整報告
        if(m_generateFullReport)
        {
            if(!GenerateFullReport(runner))
            {
                Print("生成完整報告失敗");
                success = false;
            }
        }
        
        // 生成摘要
        if(m_generateSummary)
        {
            if(!GenerateSummary(runner))
            {
                Print("生成摘要失敗");
                success = false;
            }
        }
        
        // 生成失敗測試報告
        if(runner.GetFailedTests() > 0)
        {
            if(!GenerateFailedTestsReport(runner))
            {
                Print("生成失敗測試報告失敗");
                success = false;
            }
        }
        
        return success;
    }
    
    // 生成完整報告
    bool GenerateFullReport(TestRunnerAdvanced* runner)
    {
        string timestamp = TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES);
        StringReplace(timestamp, ":", "");
        StringReplace(timestamp, ".", "");
        StringReplace(timestamp, " ", "_");
        
        string filename = StringFormat("%s\\EAPipeline_TestReport_%s.txt", m_outputDirectory, timestamp);
        
        int fileHandle = FileOpen(filename, FILE_WRITE | FILE_TXT);
        if(fileHandle == INVALID_HANDLE)
        {
            Print("無法創建完整報告文件: " + filename);
            return false;
        }
        
        // 寫入報告頭部
        FileWrite(fileHandle, "=== EAPipeline 模組測試完整報告 ===");
        FileWrite(fileHandle, "生成時間: " + TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES));
        FileWrite(fileHandle, "");
        
        // 寫入測試統計
        FileWrite(fileHandle, "=== 測試統計 ===");
        FileWrite(fileHandle, runner.GetTestStatistics());
        FileWrite(fileHandle, "");
        
        // 寫入詳細測試結果
        FileWrite(fileHandle, "=== 詳細測試結果 ===");
        
        if(runner.GetDetailResultCount() > 0)
        {
            // 這裡需要實現獲取詳細結果的邏輯
            // 由於TestRunnerAdvanced沒有公開詳細結果，我們使用基本信息
            FileWrite(fileHandle, StringFormat("總測試數: %d", runner.GetTotalTests()));
            FileWrite(fileHandle, StringFormat("通過測試: %d", runner.GetPassedTests()));
            FileWrite(fileHandle, StringFormat("失敗測試: %d", runner.GetFailedTests()));
            
            if(runner.GetFailedTests() > 0)
            {
                FileWrite(fileHandle, "");
                FileWrite(fileHandle, "=== 失敗測試列表 ===");
                // 這裡可以添加失敗測試的詳細信息
            }
        }
        else
        {
            FileWrite(fileHandle, "沒有收集到詳細測試結果");
        }
        
        FileWrite(fileHandle, "");
        FileWrite(fileHandle, "=== 報告結束 ===");
        
        FileClose(fileHandle);
        Print("完整報告已生成: " + filename);
        return true;
    }
    
    // 生成摘要
    bool GenerateSummary(TestRunnerAdvanced* runner)
    {
        string timestamp = TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES);
        StringReplace(timestamp, ":", "");
        StringReplace(timestamp, ".", "");
        StringReplace(timestamp, " ", "_");
        
        string filename = StringFormat("%s\\EAPipeline_TestSummary_%s.txt", m_outputDirectory, timestamp);
        
        int fileHandle = FileOpen(filename, FILE_WRITE | FILE_TXT);
        if(fileHandle == INVALID_HANDLE)
        {
            Print("無法創建摘要文件: " + filename);
            return false;
        }
        
        // 寫入摘要頭部
        FileWrite(fileHandle, "=== EAPipeline 模組測試摘要 ===");
        FileWrite(fileHandle, "生成時間: " + TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES));
        FileWrite(fileHandle, "");
        
        // 寫入基本統計
        FileWrite(fileHandle, "=== 基本統計 ===");
        FileWrite(fileHandle, runner.GetTestStatistics());
        FileWrite(fileHandle, "");
        
        // 寫入測試結果
        string result = runner.AllTestsPassed() ? "全部通過" : "存在失敗";
        FileWrite(fileHandle, "測試結果: " + result);
        
        if(runner.GetFailedTests() > 0)
        {
            FileWrite(fileHandle, StringFormat("失敗測試數量: %d", runner.GetFailedTests()));
            FileWrite(fileHandle, "建議: 查看完整報告了解失敗詳情");
        }
        
        FileWrite(fileHandle, "");
        FileWrite(fileHandle, "=== 摘要結束 ===");
        
        FileClose(fileHandle);
        Print("測試摘要已生成: " + filename);
        return true;
    }
    
    // 生成失敗測試報告
    bool GenerateFailedTestsReport(TestRunnerAdvanced* runner)
    {
        if(runner.GetFailedTests() == 0)
            return true; // 沒有失敗測試，不需要生成報告
        
        string timestamp = TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES);
        StringReplace(timestamp, ":", "");
        StringReplace(timestamp, ".", "");
        StringReplace(timestamp, " ", "_");
        
        string filename = StringFormat("%s\\EAPipeline_FailedTests_%s.txt", m_outputDirectory, timestamp);
        
        int fileHandle = FileOpen(filename, FILE_WRITE | FILE_TXT);
        if(fileHandle == INVALID_HANDLE)
        {
            Print("無法創建失敗測試報告文件: " + filename);
            return false;
        }
        
        // 寫入報告頭部
        FileWrite(fileHandle, "=== EAPipeline 模組失敗測試報告 ===");
        FileWrite(fileHandle, "生成時間: " + TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES));
        FileWrite(fileHandle, "");
        
        // 寫入失敗統計
        FileWrite(fileHandle, "=== 失敗統計 ===");
        FileWrite(fileHandle, StringFormat("失敗測試數量: %d", runner.GetFailedTests()));
        FileWrite(fileHandle, StringFormat("總測試數量: %d", runner.GetTotalTests()));
        FileWrite(fileHandle, StringFormat("失敗率: %.2f%%", 
                                         (double)runner.GetFailedTests() / runner.GetTotalTests() * 100.0));
        FileWrite(fileHandle, "");
        
        // 寫入失敗測試詳情
        FileWrite(fileHandle, "=== 失敗測試詳情 ===");
        FileWrite(fileHandle, "注意: 詳細的失敗信息請查看控制台輸出或完整報告");
        FileWrite(fileHandle, "");
        
        // 寫入建議
        FileWrite(fileHandle, "=== 修復建議 ===");
        FileWrite(fileHandle, "1. 檢查失敗測試的具體錯誤消息");
        FileWrite(fileHandle, "2. 驗證測試數據和預期結果");
        FileWrite(fileHandle, "3. 確認被測試組件的實現是否正確");
        FileWrite(fileHandle, "4. 檢查測試環境和依賴項");
        FileWrite(fileHandle, "");
        
        FileWrite(fileHandle, "=== 報告結束 ===");
        
        FileClose(fileHandle);
        Print("失敗測試報告已生成: " + filename);
        return true;
    }
    
    // 設置輸出目錄
    void SetOutputDirectory(string directory) { m_outputDirectory = directory; }
    
    // 設置是否生成完整報告
    void SetGenerateFullReport(bool generate) { m_generateFullReport = generate; }
    
    // 設置是否生成摘要
    void SetGenerateSummary(bool generate) { m_generateSummary = generate; }
    
    // 設置最大顯示項目數
    void SetMaxDisplayItems(int maxItems) { m_maxDisplayItems = maxItems; }
    
    // 獲取設置信息
    string GetOutputDirectory() const { return m_outputDirectory; }
    bool IsGeneratingFullReport() const { return m_generateFullReport; }
    bool IsGeneratingSummary() const { return m_generateSummary; }
    int GetMaxDisplayItems() const { return m_maxDisplayItems; }
    
    // 生成配置報告
    void GenerateConfigReport()
    {
        Print("=== 測試文檔生成器配置 ===");
        Print("輸出目錄: " + m_outputDirectory);
        Print("生成完整報告: " + (m_generateFullReport ? "是" : "否"));
        Print("生成摘要: " + (m_generateSummary ? "是" : "否"));
        Print("最大顯示項目數: " + (m_maxDisplayItems == -1 ? "無限制" : IntegerToString(m_maxDisplayItems)));
        Print("=== 配置信息結束 ===");
    }
};
