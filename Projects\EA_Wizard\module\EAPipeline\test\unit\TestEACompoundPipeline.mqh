//+------------------------------------------------------------------+
//|                                        TestEACompoundPipeline.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../components/EACompoundPipeline.mqh"
#include "../mock/MockEAPipeline.mqh"

//+------------------------------------------------------------------+
//| EACompoundPipeline測試類別                                       |
//+------------------------------------------------------------------+
class TestEACompoundPipeline : public TestCase
{
private:
    TestRunner* m_runner;    // 測試運行器引用

public:
    // 構造函數
    TestEACompoundPipeline(TestRunner* runner)
    : TestCase("TestEACompoundPipeline"), m_runner(runner) {}

    // 運行所有測試
    void RunTests() override
    {
        TestConstructor();
        TestBasicProperties();
        TestExecuteEmpty();
        TestAddChild();
        TestGetCountAndMaxItems();
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        SetUp();

        // 測試基本構造函數
        EACompoundPipeline* pipeline = new EACompoundPipeline("TestPipeline");

        m_runner.RecordResult(Assert::AssertNotNull("構造函數_創建對象", pipeline));
        m_runner.RecordResult(Assert::AssertEquals("構造函數_名稱", "TestPipeline", pipeline.GetName()));
        m_runner.RecordResult(Assert::AssertEquals("構造函數_類型", "EACompoundPipeline", pipeline.GetType()));
        m_runner.RecordResult(Assert::AssertFalse("構造函數_未執行", pipeline.IsExecuted()));

        delete pipeline;
        TearDown();
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        SetUp();

        EACompoundPipeline* pipeline = new EACompoundPipeline("TestPipeline", "CustomType", 50);

        m_runner.RecordResult(Assert::AssertEquals("基本屬性_名稱", "TestPipeline", pipeline.GetName()));
        m_runner.RecordResult(Assert::AssertEquals("基本屬性_類型", "CustomType", pipeline.GetType()));
        m_runner.RecordResult(Assert::AssertEquals("基本屬性_最大項目數", 50, pipeline.GetMaxItems()));
        m_runner.RecordResult(Assert::AssertEquals("基本屬性_當前數量", 0, pipeline.GetCount()));

        delete pipeline;
        TearDown();
    }

    // 測試空流水線執行
    void TestExecuteEmpty()
    {
        SetUp();

        EACompoundPipeline* pipeline = new EACompoundPipeline("EmptyPipeline");

        // 執行空流水線
        pipeline.Execute();

        m_runner.RecordResult(Assert::AssertTrue("空流水線執行_已執行", pipeline.IsExecuted()));

        PipelineResult* result = pipeline.GetResult();
        m_runner.RecordResult(Assert::AssertNotNull("空流水線執行_結果不為空", result));

        if(result != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("空流水線執行_結果成功", result.IsSuccess()));
        }

        delete pipeline;
        TearDown();
    }



    // 測試添加子流水線
    void TestAddChild()
    {
        SetUp();

        EACompoundPipeline* pipeline = new EACompoundPipeline("ParentPipeline");
        MockEAPipeline* child = MockEAPipelineFactory::CreateSuccessfulPipeline("Child");

        // 添加子流水線
        PipelineResult* result = pipeline.Add(child);

        m_runner.RecordResult(Assert::AssertNotNull("添加子流水線_結果不為空", result));

        if(result != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("添加子流水線_結果成功", result.IsSuccess()));
            delete result;
        }

        m_runner.RecordResult(Assert::AssertEquals("添加子流水線_數量增加", 1, pipeline.GetCount()));

        delete pipeline;
        TearDown();
    }

    // 測試移除子流水線
    void TestRemoveChild()
    {
        SetUp();

        EACompoundPipeline* pipeline = new EACompoundPipeline("ParentPipeline");
        MockEAPipeline* child = MockEAPipelineFactory::CreateSuccessfulPipeline("Child");

        // 先添加子流水線
        pipeline.Add(child);
        m_runner.RecordResult(Assert::AssertEquals("移除子流水線_添加後數量", 1, pipeline.GetCount()));

        // 移除子流水線
        PipelineResult* result = pipeline.Remove(child);

        m_runner.RecordResult(Assert::AssertNotNull("移除子流水線_結果不為空", result));

        if(result != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("移除子流水線_結果成功", result.IsSuccess()));
            delete result;
        }

        m_runner.RecordResult(Assert::AssertEquals("移除子流水線_數量減少", 0, pipeline.GetCount()));

        delete pipeline;
        TearDown();
    }



    // 測試獲取數量和最大項目數
    void TestGetCountAndMaxItems()
    {
        SetUp();

        EACompoundPipeline* pipeline = new EACompoundPipeline("CountPipeline");

        m_runner.RecordResult(Assert::AssertEquals("數量統計_初始數量", 0, pipeline.GetCount()));

        // 添加一個子流水線
        MockEAPipeline* child = MockEAPipelineFactory::CreateSuccessfulPipeline("Child1");
        pipeline.Add(child);

        m_runner.RecordResult(Assert::AssertEquals("數量統計_添加後數量", 1, pipeline.GetCount()));

        delete pipeline;
        TearDown();
    }
};
