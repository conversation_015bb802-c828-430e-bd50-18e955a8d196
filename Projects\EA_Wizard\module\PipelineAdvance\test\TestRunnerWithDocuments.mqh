//+------------------------------------------------------------------+
//|                                      TestRunnerWithDocuments.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "TestFramework.mqh"
#include "TestRunnerAdvanced.mqh"
#include "TestDocumentGeneratorFixed.mqh"
#include "unit/TestPipelineResult.mqh"
#include "unit/TestPipelineComposite.mqh"
#include "unit/TestPipelineStates.mqh"
#include "integration/TestPipelineIntegration.mqh"

//+------------------------------------------------------------------+
//| 支援文檔輸出的 PipelineAdvance 測試運行器                        |
//+------------------------------------------------------------------+
class PipelineAdvanceTestRunnerWithDocs
{
private:
    TestRunnerAdvanced* m_runner;                // 增強版測試運行器
    TestDocumentGeneratorFixed* m_docGenerator;  // 文檔生成器
    bool m_runUnitTests;                    // 是否運行單元測試
    bool m_runIntegrationTests;             // 是否運行整合測試
    bool m_verbose;                         // 是否詳細輸出
    bool m_generateDocs;                    // 是否生成文檔
    bool m_generateSummary;                 // 是否生成摘要
    string m_outputDirectory;               // 輸出目錄

public:
    // 構造函數
    PipelineAdvanceTestRunnerWithDocs(bool runUnitTests = true,
                                     bool runIntegrationTests = true,
                                     bool verbose = true,
                                     bool generateDocs = true,
                                     bool generateSummary = true,
                                     string outputDir = "TestReports")
    : m_runUnitTests(runUnitTests), m_runIntegrationTests(runIntegrationTests),
      m_verbose(verbose), m_generateDocs(generateDocs), m_generateSummary(generateSummary),
      m_outputDirectory(outputDir)
    {
        m_runner = new TestRunnerAdvanced(true);
        m_docGenerator = new TestDocumentGeneratorFixed(outputDir, "PipelineAdvance_TestReport", true, true);
    }

    // 析構函數
    ~PipelineAdvanceTestRunnerWithDocs()
    {
        if(m_runner != NULL)
        {
            delete m_runner;
            m_runner = NULL;
        }

        if(m_docGenerator != NULL)
        {
            delete m_docGenerator;
            m_docGenerator = NULL;
        }
    }

    // 運行所有測試並生成文檔
    void RunAllTestsWithDocs()
    {
        PrintHeader();

        if(m_runUnitTests)
        {
            RunUnitTests();
        }

        if(m_runIntegrationTests)
        {
            RunIntegrationTests();
        }

        PrintSummary();
        GenerateDocuments();
    }

    // 只運行單元測試並生成文檔
    void RunUnitTestsOnlyWithDocs()
    {
        PrintHeader();
        RunUnitTests();
        PrintSummary();
        GenerateDocuments();
    }

    // 只運行整合測試並生成文檔
    void RunIntegrationTestsOnlyWithDocs()
    {
        PrintHeader();
        RunIntegrationTests();
        PrintSummary();
        GenerateDocuments();
    }

    // 運行特定測試並生成文檔
    void RunSpecificTestWithDocs(string testClassName)
    {
        PrintHeader();

        if(testClassName == "TestPipelineResult")
        {
            TestPipelineResult* test = new TestPipelineResult(m_runner);
            m_runner.RunTestCase(test);
            delete test;
        }
        else if(testClassName == "TestPipelineComposite")
        {
            TestPipelineComposite* test = new TestPipelineComposite(m_runner);
            m_runner.RunTestCase(test);
            delete test;
        }
        else if(testClassName == "TestPipelineStates")
        {
            TestPipelineStates* test = new TestPipelineStates(m_runner);
            m_runner.RunTestCase(test);
            delete test;
        }
        else if(testClassName == "TestPipelineIntegration")
        {
            TestPipelineIntegration* test = new TestPipelineIntegration(m_runner);
            m_runner.RunTestCase(test);
            delete test;
        }
        else
        {
            Print("錯誤: 未知的測試類別 - " + testClassName);
            Print("可用的測試類別:");
            Print("  - TestPipelineResult");
            Print("  - TestPipelineComposite");
            Print("  - TestPipelineStates");
            Print("  - TestPipelineIntegration");
            return;
        }

        PrintSummary();
        GenerateDocuments();
    }

    // 生成文檔
    void GenerateDocuments()
    {
        if(!m_generateDocs && !m_generateSummary)
        {
            if(m_verbose)
            {
                Print("📄 文檔生成已禁用");
            }
            return;
        }

        if(m_verbose)
        {
            Print("");
            Print("📄 開始生成測試文檔...");
        }

        bool success = true;

        // 生成完整報告
        if(m_generateDocs)
        {
            if(m_docGenerator.GenerateAdvancedTestReport(m_runner, "PipelineAdvance"))
            {
                if(m_verbose) Print("✅ 完整測試報告已生成");
            }
            else
            {
                Print("❌ 完整測試報告生成失敗");
                success = false;
            }
        }

        // 生成摘要報告
        if(m_generateSummary)
        {
            if(m_docGenerator.GenerateTestSummary(m_runner, "PipelineAdvance"))
            {
                if(m_verbose) Print("✅ 測試摘要已生成");
            }
            else
            {
                Print("❌ 測試摘要生成失敗");
                success = false;
            }
        }

        if(m_verbose)
        {
            if(success)
            {
                Print("📁 所有文檔已保存到: " + m_outputDirectory + " 目錄");
                Print("💡 提示: 文檔保存在 MQL4\\Files\\" + m_outputDirectory + " 目錄中");
            }
            else
            {
                Print("⚠️ 部分文檔生成失敗，請檢查文件權限和磁盤空間");
            }
        }
    }

    // 設置文檔生成選項
    void SetDocumentOptions(bool generateDocs, bool generateSummary, string outputDir = "")
    {
        m_generateDocs = generateDocs;
        m_generateSummary = generateSummary;

        if(outputDir != "")
        {
            m_outputDirectory = outputDir;
            m_docGenerator.SetOutputDirectory(outputDir);
        }
    }

    // 設置通過測試顯示選項
    void SetPassedTestsDisplayOptions(int maxDisplay)
    {
        if(m_docGenerator != NULL)
        {
            m_docGenerator.SetMaxPassedTestsDisplay(maxDisplay);
        }
    }

    // 設置無限制顯示通過測試
    void SetUnlimitedPassedTestsDisplay()
    {
        if(m_docGenerator != NULL)
        {
            m_docGenerator.SetUnlimitedPassedTestsDisplay();
        }
    }

    // 獲取測試結果
    bool AllTestsPassed() const { return m_runner.AllTestsPassed(); }
    int GetTotalTests() const { return m_runner.GetTotalTests(); }
    int GetPassedTests() const { return m_runner.GetPassedTests(); }
    int GetFailedTests() const { return m_runner.GetFailedTests(); }

    // 獲取增強版運行器（用於高級功能）
    TestRunnerAdvanced* GetAdvancedRunner() const { return m_runner; }

private:
    // 運行單元測試
    void RunUnitTests()
    {
        if(m_verbose)
        {
            Print("🧪 開始運行單元測試...");
            Print("=====================================");
        }

        // 運行PipelineResult測試
        TestPipelineResult* resultTest = new TestPipelineResult(m_runner);
        m_runner.RunTestCase(resultTest);
        delete resultTest;

        // 運行PipelineComposite測試
        TestPipelineComposite* compositeTest = new TestPipelineComposite(m_runner);
        m_runner.RunTestCase(compositeTest);
        delete compositeTest;

        // 運行PipelineStates測試
        TestPipelineStates* statesTest = new TestPipelineStates(m_runner);
        m_runner.RunTestCase(statesTest);
        delete statesTest;

        if(m_verbose)
        {
            Print("=====================================");
            Print("✅ 單元測試完成");
            Print("");
        }
    }

    // 運行整合測試
    void RunIntegrationTests()
    {
        if(m_verbose)
        {
            Print("🔗 開始運行整合測試...");
            Print("=====================================");
        }

        // 運行整合測試
        TestPipelineIntegration* integrationTest = new TestPipelineIntegration(m_runner);
        m_runner.RunTestCase(integrationTest);
        delete integrationTest;

        if(m_verbose)
        {
            Print("=====================================");
            Print("✅ 整合測試完成");
            Print("");
        }
    }

    // 打印測試頭部信息
    void PrintHeader()
    {
        if(m_verbose)
        {
            Print("╔══════════════════════════════════════════════════════════════╗");
            Print("║            PipelineAdvance 模組測試套件 (含文檔輸出)         ║");
            Print("║                        EA_Wizard                             ║");
            Print("╚══════════════════════════════════════════════════════════════╝");
            Print("");
            Print("📋 測試配置:");
            Print("   • 單元測試: " + (m_runUnitTests ? "啟用" : "禁用"));
            Print("   • 整合測試: " + (m_runIntegrationTests ? "啟用" : "禁用"));
            Print("   • 詳細輸出: " + (m_verbose ? "啟用" : "禁用"));
            Print("   • 生成完整報告: " + (m_generateDocs ? "啟用" : "禁用"));
            Print("   • 生成摘要: " + (m_generateSummary ? "啟用" : "禁用"));
            Print("   • 輸出目錄: " + m_outputDirectory);
            Print("");
        }
    }

    // 打印測試摘要
    void PrintSummary()
    {
        if(m_verbose)
        {
            Print("");
            Print("╔══════════════════════════════════════════════════════════════╗");
            Print("║                        測試執行摘要                          ║");
            Print("╚══════════════════════════════════════════════════════════════╝");
        }

        m_runner.PrintSummary();

        if(m_verbose)
        {
            Print("");
            if(AllTestsPassed())
            {
                Print("🎉 恭喜！所有測試都通過了！");
                Print("   PipelineAdvance模組運行正常，可以安全使用。");
            }
            else
            {
                Print("❌ 有測試失敗！");
                Print("   請檢查上述錯誤信息並修復相關問題。");
                Print("   建議在修復後重新運行測試。");
            }
            Print("");
            Print("📊 測試覆蓋範圍:");
            Print("   • PipelineResult: 基本功能測試");
            Print("   • PipelineComposite: 組合模式測試");
            Print("   • PipelineStates: 狀態模式測試");
            Print("   • Integration: 整合場景測試");
        }
    }
};
