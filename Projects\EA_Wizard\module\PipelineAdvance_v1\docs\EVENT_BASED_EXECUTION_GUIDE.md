# 事件驅動執行指南

## 📋 概述

PipelineGroupManager 現在支援基於事件類型的執行和重置，提供更精確的流水線控制。

## 🔧 新增方法

### Execute(ENUM_TRADING_EVENT eventType)

執行指定事件類型的流水線組：

```mql4
// 只執行 TRADING_TICK 事件的流水線組
manager.Execute(TRADING_TICK);

// 只執行 TRADING_INIT 事件的流水線組
manager.Execute(TRADING_INIT);

// 只執行 TRADING_DEINIT 事件的流水線組
manager.Execute(TRADING_DEINIT);
```

### Restore(ENUM_TRADING_EVENT eventType)

重置指定事件類型的流水線組：

```mql4
// 只重置 TRADING_TICK 事件的流水線組
manager.Restore(TRADING_TICK);

// 只重置 TRADING_INIT 事件的流水線組
manager.Restore(TRADING_INIT);
```

## 🎯 使用場景

### 1. EA 生命週期管理

```mql4
void OnInit()
{
    // 執行初始化相關的流水線組
    manager.Execute(TRADING_INIT);
}

void OnTick()
{
    // 執行每個 Tick 相關的流水線組
    manager.Execute(TRADING_TICK);
}

void OnDeinit(const int reason)
{
    // 執行清理相關的流水線組
    manager.Execute(TRADING_DEINIT);
}
```

### 2. 分階段處理

```mql4
// 創建不同事件類型的流水線組
PipelineGroup* initGroup = new PipelineGroup("初始化組", "處理初始化", TRADING_INIT);
PipelineGroup* tickGroup = new PipelineGroup("Tick組", "處理Tick", TRADING_TICK);
PipelineGroup* deinitGroup = new PipelineGroup("清理組", "處理清理", TRADING_DEINIT);

// 添加到管理器
manager.AddGroup(initGroup);
manager.AddGroup(tickGroup);
manager.AddGroup(deinitGroup);

// 分別執行不同階段
manager.Execute(TRADING_INIT);    // 只執行初始化組
manager.Execute(TRADING_TICK);    // 只執行Tick組
manager.Execute(TRADING_DEINIT);  // 只執行清理組
```

### 3. 錯誤恢復

```mql4
// 如果 Tick 處理出現錯誤，只重置 Tick 相關的組
if(tickProcessingError)
{
    manager.Restore(TRADING_TICK);
    // 重新執行 Tick 處理
    manager.Execute(TRADING_TICK);
}
```

## 📊 事件類型對應

| 事件類型 | 使用場景 | 執行時機 |
|---------|---------|---------|
| `TRADING_INIT` | 初始化流水線 | OnInit() |
| `TRADING_TICK` | Tick 處理流水線 | OnTick() |
| `TRADING_DEINIT` | 清理流水線 | OnDeinit() |

## 🔄 執行邏輯

### Execute(ENUM_TRADING_EVENT) 邏輯

1. 檢查管理器是否已執行和是否啟用
2. 遍歷 3 個流水線組（m_group1, m_group2, m_group3）
3. 對於每個組：
   - 檢查組是否為 NULL
   - 檢查組是否啟用
   - **檢查組的事件類型是否匹配**
   - 如果匹配，執行該組的所有流水線

### Restore(ENUM_TRADING_EVENT) 邏輯

1. 遍歷 3 個流水線組
2. 對於每個組：
   - 檢查組是否為 NULL
   - **檢查組的事件類型是否匹配**
   - 如果匹配，重置該組
3. 檢查是否所有組都已重置，如果是則重置管理器狀態

## 🧪 完整測試示例

```mql4
void TestEventBasedExecution()
{
    // 創建管理器
    PipelineGroupManager* manager = new PipelineGroupManager("事件管理器");
    
    // 創建不同事件類型的組
    PipelineGroup* initGroup = new PipelineGroup("初始化", "初始化處理", TRADING_INIT);
    PipelineGroup* tickGroup = new PipelineGroup("Tick處理", "Tick處理", TRADING_TICK);
    PipelineGroup* deinitGroup = new PipelineGroup("清理", "清理處理", TRADING_DEINIT);
    
    // 添加到管理器
    manager.AddGroup(initGroup);
    manager.AddGroup(tickGroup);
    manager.AddGroup(deinitGroup);
    
    Print("=== 測試事件驅動執行 ===");
    
    // 測試分別執行不同事件類型
    Print("執行 TRADING_INIT:");
    manager.Execute(TRADING_INIT);
    
    Print("執行 TRADING_TICK:");
    manager.Execute(TRADING_TICK);
    
    Print("執行 TRADING_DEINIT:");
    manager.Execute(TRADING_DEINIT);
    
    // 測試分別重置不同事件類型
    Print("重置 TRADING_TICK:");
    manager.Restore(TRADING_TICK);
    
    Print("重新執行 TRADING_TICK:");
    manager.Execute(TRADING_TICK);
    
    // 清理
    delete manager;
}
```

## 🚨 注意事項

1. **事件類型匹配**：只有事件類型匹配的組才會被執行或重置
2. **管理器狀態**：Execute() 會設置管理器為已執行狀態
3. **部分重置**：Restore(eventType) 只重置指定事件類型的組
4. **完全重置**：只有當所有組都被重置時，管理器狀態才會重置
5. **組數量限制**：最多支援 3 個組，每個組可以有不同的事件類型

## 🎯 最佳實踐

1. **明確事件分類**：為不同的處理階段創建對應事件類型的組
2. **錯誤隔離**：使用事件類型重置來隔離錯誤影響範圍
3. **性能優化**：只執行當前需要的事件類型，避免不必要的處理
4. **狀態管理**：合理使用 Restore() 來管理流水線狀態
