#property strict

#include "../../Util/ErrorHandlerAdvance.mqh"

#define EA_ERROR_HANDLER_MAX_ERRORS 10
#define EA_ERROR_HANDLER_PRINTABLE true

class EAErrorHandler : public ErrorHandler {
private:
    static EAErrorHandler* instance;

    // Private constructor
    EAErrorHandler() : ErrorHandler(EA_ERROR_HANDLER_MAX_ERRORS, EA_ERROR_HANDLER_PRINTABLE) {}

public:
    // Delete copy constructor and assignment operator
    <PERSON><PERSON><PERSON>r<PERSON>and<PERSON>(const EAErrorHandler&) = delete;
    void operator=(const EAErrorHandler&) = delete;

    // Get instance method
    static EAErrorHandler* GetInstance() {
        if(instance == NULL) {
            instance = new EAErrorHandler();
        }
        return instance;
    }
};

// Initialize static instance
EAErrorHandler* EAErrorHandler::instance = NULL;
