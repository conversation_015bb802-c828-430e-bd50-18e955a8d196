//+------------------------------------------------------------------+
//|                                       EACompoundPipelineStatePending.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "EACompoundPipelineState.mqh"
#include "EACompoundPipelineStateRunning.mqh"

//+------------------------------------------------------------------+
//| 流水線待執行狀態 - 實現待執行狀態的行為                           |
//+------------------------------------------------------------------+

class EACompoundPipelineStatePending : public EACompoundPipelineState
{
public:
    // 構造函數
    EACompoundPipelineStatePending()
    : EACompoundPipelineState(EA_PIPELINE_STATE_PENDING, "待執行") {}
    
    // 析構函數
    ~EACompoundPipelineStatePending() {}
    
    // 執行流水線
    void Execute(EACompoundPipeline_A* pipeline) override;
    
    // 重置流水線狀態
    void Restore(EACompoundPipeline_A* pipeline) override;
};


void EACompoundPipelineStatePending::Execute(EACompoundPipeline_A* pipeline)
{
    // 將狀態轉換為執行中
    pipeline.SetState(new EACompoundPipelineStateRunning());

    // 執行實際的流水線邏輯
    pipeline.GetComposite().Execute();

    if(pipeline.GetResult().IsSuccess())
    {
        pipeline.SetState(new EACompoundPipelineStateCompleted());
    }
    else
    {
        pipeline.SetState(new EACompoundPipelineStateFailed());
    }
    // 根據執行結果更新狀態
    // 這部分應該在流水線內部實現，而不是在狀態中實現
}


void EACompoundPipelineStatePending::Restore(EACompoundPipeline_A* pipeline)
{
    // 待執行狀態下的重置不需要做任何事情，因為已經是初始狀態
}
