# EAPipeline 模組測試套件

## 概述

EAPipeline 模組測試套件提供了完整的測試框架，用於驗證 EAPipeline 模組的各個組件和功能。測試套件包括單元測試、整合測試和性能測試，確保模組的可靠性和穩定性。

## 目錄結構

```
test/
├── README.md                           # 本文檔
├── TestFramework.mqh                   # 測試框架
├── TestRunnerAdvanced.mqh              # 增強版測試運行器
├── RunAllTests.mqh                     # 主測試入口
├── mock/                               # Mock 類別
│   ├── MockEAPipeline.mqh             # Mock EA流水線
│   └── MockEARegistry.mqh             # Mock EA註冊器
├── unit/                               # 單元測試
│   ├── TestEACompoundPipeline.mqh     # EACompoundPipeline 測試
│   └── TestEAPipelineManager.mqh      # EAPipelineManager 測試
└── integration/                        # 整合測試
    └── TestEAPipelineIntegration.mqh   # 整合場景測試
```

## 快速開始

### 運行所有測試

```mql4
#include "module/EAPipeline/test/RunAllTests.mqh"

void OnInit()
{
    // 基本測試（僅控制台輸出）
    RunAllEAPipelineTests();

    // 增強版測試（詳細信息）
    RunAllEAPipelineTestsAdvanced();
}
```

### 運行特定類型的測試

```mql4
// 只運行單元測試
RunEAPipelineUnitTests();

// 只運行整合測試
RunEAPipelineIntegrationTests();

// 快速檢查（靜默模式）
bool allPassed = QuickEAPipelineCheck();
```

### 運行特定測試類別

```mql4
// 測試 EACompoundPipeline
RunSpecificEAPipelineTest("TestEACompoundPipeline");

// 測試 EAPipelineManager
RunSpecificEAPipelineTest("TestEAPipelineManager");

// 測試整合場景
RunSpecificEAPipelineTest("TestEAPipelineIntegration");
```

## 測試框架

### TestFramework.mqh

提供基礎的測試功能：

- **Assert 類別**：提供各種斷言方法
- **TestCase 基類**：所有測試類別的基類
- **TestRunner**：管理和執行測試
- **TestResult**：記錄測試結果

### 斷言方法

```mql4
// 布爾斷言
Assert::AssertTrue("測試名稱", condition, "錯誤消息");
Assert::AssertFalse("測試名稱", condition, "錯誤消息");

// 相等斷言
Assert::AssertEquals("測試名稱", expected, actual, "錯誤消息");

// 指針斷言
Assert::AssertNotNull("測試名稱", pointer, "錯誤消息");
Assert::AssertNull("測試名稱", pointer, "錯誤消息");

// 字符串斷言
Assert::AssertContains("測試名稱", haystack, needle, "錯誤消息");
Assert::AssertNotContains("測試名稱", haystack, needle, "錯誤消息");
```

## 測試類別

### 1. TestEACompoundPipeline

測試 `EACompoundPipeline` 類別的裝飾者模式功能：

- 構造函數和基本屬性測試
- 添加和移除子流水線測試
- 執行邏輯測試
- 容量限制測試
- 重複執行防護測試

### 2. TestEAPipelineManager

測試 `EAPipelineManager` 類別的管理功能：

- 流水線註冊和移除測試
- 流水線執行測試
- 流水線查詢測試
- 容量限制測試
- 錯誤處理測試

### 3. TestEAPipelineIntegration

測試整合場景：

- 簡單工作流程測試
- 複雜嵌套結構測試
- 錯誤處理測試
- OnInit/OnTick/OnDeinit 工作流程測試
- 大規模場景測試

## Mock 類別

### MockEAPipeline

提供用於測試的模擬 EA 流水線：

```mql4
// 創建成功的 Mock 流水線
MockEAPipeline* success = MockEAPipelineFactory::CreateSuccessfulPipeline("測試");

// 創建失敗的 Mock 流水線
MockEAPipeline* failed = MockEAPipelineFactory::CreateFailedPipeline("測試");

// 檢查調用次數
int callCount = mockPipeline.GetExecuteCallCount();
```

### MockEARegistry

提供用於測試的模擬 EA 註冊器：

```mql4
// 創建成功的 Mock 註冊器
MockEARegistry<IPipeline*>* registry = MockEARegistryFactory::CreateSuccessfulRegistry<IPipeline*>("測試");

// 檢查註冊調用次數
int registerCalls = registry.GetRegisterCallCount();
```

## 測試最佳實踐

### 1. 測試命名

- 使用描述性的測試名稱
- 包含測試的功能和預期結果
- 使用中文註解說明測試目的

### 2. 測試結構

```mql4
void TestSpecificFeature()
{
    SetUp();        // 準備測試環境

    // 執行測試邏輯
    // 使用斷言驗證結果

    TearDown();     // 清理測試環境
}
```

### 3. 資源管理

- 在測試中創建的對象必須在測試結束時清理
- 使用 `SetUp()` 和 `TearDown()` 方法管理資源
- 避免記憶體洩漏

### 4. 測試獨立性

- 每個測試應該獨立運行
- 不依賴其他測試的執行順序
- 不共享狀態

## 持續整合

### CI 測試

```mql4
void OnInit()
{
    CIEAPipelineTests();
}
```

### 回歸測試

```mql4
void OnInit()
{
    RunEAPipelineRegressionTests();
}
```

### 性能測試

```mql4
void OnInit()
{
    RunEAPipelinePerformanceTests();
}
```

## 測試覆蓋範圍

### 單元測試覆蓋

- **EACompoundPipeline**: 100% 方法覆蓋

  - 構造函數、執行邏輯、子流水線管理
  - 容量限制、重複執行防護
  - 重置功能、狀態查詢

- **EAPipelineManager**: 100% 方法覆蓋
  - 流水線註冊、移除、執行
  - 流水線查詢、清空功能
  - 錯誤處理、容量管理

### 整合測試覆蓋

- **工作流程測試**: 完整的 EA 生命週期

  - OnInit 階段流水線
  - OnTick 階段流水線
  - OnDeinit 階段流水線

- **複雜場景測試**: 嵌套和大規模場景
  - 多層嵌套流水線
  - 大量流水線管理
  - 混合成功/失敗場景

## 故障排除

### 常見問題

1. **記憶體洩漏**

   - 確保所有創建的對象都被正確刪除
   - 檢查 Mock 對象的生命週期管理

2. **測試失敗**

   - 檢查斷言條件是否正確
   - 驗證測試數據的有效性
   - 確認 Mock 對象的行為設置

3. **性能問題**
   - 減少大規模測試的數據量
   - 使用快速檢查模式
   - 優化測試邏輯

### 調試技巧

```mql4
// 啟用詳細日誌
TestRunnerAdvanced* runner = new TestRunnerAdvanced(true);

// 只顯示失敗的測試
runner.ShowFailedTestsDetail();

// 獲取特定類別的測試結果
TestResultDetail* classResults[];
int count = runner.GetResultsByClass("TestEACompoundPipeline", classResults);
```

## 擴展測試

### 添加新測試

1. 在適當的目錄創建新的測試檔案
2. 繼承 `TestCase` 基類
3. 實現 `RunTests()` 方法
4. 在 `RunAllTests.mqh` 中添加新測試

### 添加新斷言

在 `TestFramework.mqh` 的 `Assert` 類別中添加新方法。

### 添加新 Mock 類別

在 `mock/` 目錄中創建新的 Mock 實現。

## 測試文檔生成

### 基本文檔輸出

測試套件支援將測試結果輸出為文檔：

```mql4
// 運行測試並生成基本文檔
RunAllEAPipelineTestsWithDocs();

// 運行特定測試並生成文檔
RunSpecificEAPipelineTestWithDocs("TestEACompoundPipeline");
```

### 自定義文檔選項

```mql4
// 自定義文檔輸出選項
RunEAPipelineTestsWithCustomDocs(
    true,                           // 生成完整報告
    true,                           // 生成摘要
    "EAPipelineTestReports"         // 自定義輸出目錄
);
```

### 文檔輸出位置

測試文檔將輸出到 `MQL4\Files\TestReports\EAPipeline\` 目錄：

- `EAPipeline_TestReport_YYYYMMDD_HHMMSS.txt` - 完整測試報告
- `EAPipeline_TestSummary_YYYYMMDD_HHMMSS.txt` - 測試摘要
- `EAPipeline_FailedTests_YYYYMMDD_HHMMSS.txt` - 失敗測試詳情

## 使用示例

### 基本使用

```mql4
#include "module/EAPipeline/test/RunAllTests.mqh"

void OnInit()
{
    // 顯示測試套件信息
    ShowEAPipelineTestInfo();

    // 運行所有測試
    RunAllEAPipelineTests();
}
```

### 開發環境使用

```mql4
void OnInit()
{
    // 快速檢查
    if(!QuickEAPipelineCheck())
    {
        Print("快速檢查失敗，運行詳細測試...");
        RunAllEAPipelineTestsAdvanced();
    }
    else
    {
        Print("快速檢查通過");
    }
}
```

### CI/CD 環境使用

```mql4
void OnInit()
{
    // CI 測試
    CIEAPipelineTests();

    // 回歸測試
    RunEAPipelineRegressionTests();

    // 性能測試
    RunEAPipelinePerformanceTests();
}
```

## 版本歷史

- **v1.0.0**: 初始版本
  - 基本測試框架
  - EACompoundPipeline 和 EAPipelineManager 測試
  - 整合測試場景
  - Mock 類別支持
  - 文檔生成功能

## 貢獻指南

1. 遵循現有的代碼風格和命名約定
2. 為新功能添加相應的測試
3. 確保所有測試都能通過
4. 更新相關文檔
5. 測試文件不超過 500 行
6. 使用中文註解
7. 遵循模組化設計原則

## 聯繫信息

如有問題或建議，請聯繫 EA_Wizard 開發團隊。
