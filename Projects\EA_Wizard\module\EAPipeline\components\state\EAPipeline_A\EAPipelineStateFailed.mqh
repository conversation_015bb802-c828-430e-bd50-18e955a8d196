//+------------------------------------------------------------------+
//|                                       EAPipelineStateFailed.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "EAPipelineState.mqh"
#include "EAPipelineStatePending.mqh"

//+------------------------------------------------------------------+
//| 流水線執行失敗狀態 - 實現執行失敗狀態的行為                       |
//+------------------------------------------------------------------+

class EAPipelineStateFailed : public EAPipelineState
{
public:
    // 構造函數
    EAPipelineStateFailed()
    : EAPipelineState(EA_PIPELINE_STATE_FAILED, "執行失敗") {}
    
    // 析構函數
    ~EAPipelineStateFailed() {}
    
    // 執行流水線
    void Execute(EAPipeline_A* pipeline) override;
    
    // 重置流水線狀態
    void Restore(EAPipeline_A* pipeline) override;
};


void EAPipelineStateFailed::Execute(EAPipeline_A* pipeline)
{
    // 執行失敗狀態下不應該再次執行
    Print("警告: 流水線 '", pipeline.GetName(), "' 執行失敗，需要先重置才能再次執行");
}


void EAPipelineStateFailed::Restore(EAPipeline_A* pipeline)
{
    // 將狀態轉換為待執行
    pipeline.SetState(new EAPipelineStatePending());
    pipeline.Restore();
}
