//+------------------------------------------------------------------+
//|                                              PipelineAdvance.mq4 |
//|                                  Copyright 2023, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
#include "../../Projects/EA_Wizard/module/EAPipeline/test/RunAllTests.mqh"

void OnStart()
{
    // 基本測試（僅控制台輸出）
    RunAllEAPipelineTests();

    // 安全版測試（避免指針問題）
    RunAllEAPipelineTestsSafe();
}
//+------------------------------------------------------------------+
