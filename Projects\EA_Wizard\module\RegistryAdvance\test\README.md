# RegistryAdvance 模組測試文檔

## 概述

本目錄包含 RegistryAdvance 模組的完整測試套件，提供全面的單元測試和整合測試，確保註冊器功能的正確性和穩定性。

## 目錄結構

```
test/
├── README.md                           # 本文檔
├── TestFramework.mqh                   # 測試框架
├── TestRunner.mqh                      # 測試運行器
├── TestRunnerAdvanced.mqh              # 增強版測試運行器
├── TestDocumentGeneratorFixed.mqh      # 測試文檔生成器
├── TestRunnerWithDocuments.mqh         # 支援文檔輸出的測試運行器
├── RunAllTests.mqh                     # 主測試入口
├── DocumentOutputExample.mqh           # 文檔輸出功能使用示例
├── mock/                               # Mock 類別
│   └── MockRegistry.mqh               # Mock 註冊器實現
├── unit/                               # 單元測試
│   ├── TestRegistryItem.mqh           # RegistryItem 測試
│   ├── TestRegistryResult.mqh         # RegistryResult 測試
│   └── TestItemRegistry.mqh           # ItemRegistry 測試
└── integration/                        # 整合測試
    └── TestRegistryIntegration.mqh     # 整合場景測試
```

## 快速開始

### 運行所有測試

```mql4
#include "Projects/EA_Wizard/module/RegistryAdvance/test/RunAllTests.mqh"

void OnInit()
{
    // 基本測試（僅控制台輸出）
    RunAllRegistryAdvanceTests();

    // 測試 + 文檔輸出
    RunAllRegistryAdvanceTestsWithDocs();
}
```

### 運行特定類型的測試

```mql4
// 只運行單元測試
RunRegistryAdvanceUnitTests();

// 只運行整合測試
RunRegistryAdvanceIntegrationTests();

// 快速檢查（靜默模式）
bool allPassed = QuickRegistryAdvanceCheck();
```

### 運行特定測試類別

```mql4
// 測試 RegistryItem
RunSpecificRegistryAdvanceTest("TestRegistryItem");

// 測試 RegistryResult
RunSpecificRegistryAdvanceTest("TestRegistryResult");

// 測試 ItemRegistry
RunSpecificRegistryAdvanceTest("TestItemRegistry");

// 測試整合場景
RunSpecificRegistryAdvanceTest("TestRegistryIntegration");
```

## 測試框架

### TestFramework.mqh

提供基礎的測試功能：

- **Assert 類別**：提供各種斷言方法
- **TestCase 基類**：所有測試類別的基類
- **TestRunner**：管理和執行測試
- **TestResult**：記錄測試結果

### 斷言方法

```mql4
// 布爾斷言
Assert::AssertTrue("測試名稱", condition, "錯誤消息");
Assert::AssertFalse("測試名稱", condition, "錯誤消息");

// 相等斷言
Assert::AssertEquals("測試名稱", expected, actual, "錯誤消息");

// 指針斷言
Assert::AssertNotNull("測試名稱", pointer, "錯誤消息");
Assert::AssertNull("測試名稱", pointer, "錯誤消息");

// 字符串包含斷言
Assert::AssertContains("測試名稱", haystack, needle, "錯誤消息");
Assert::AssertNotContains("測試名稱", haystack, needle, "錯誤消息");
```

## 測試類別

### 1. TestRegistryItem

測試 `RegistryItem` 類別的基本功能：

- 構造函數測試
- Getter 方法測試（GetId, GetName, GetDescription, GetValue, GetType, GetCreateTime）
- ToString 方法測試
- 不同數據類型測試
- 時間戳測試
- 邊界情況和長值測試

### 2. TestRegistryResult

測試 `RegistryResult` 類別的結果處理功能：

- 構造函數測試
- IsSuccess 方法測試
- GetMessage, GetSource, GetKey 方法測試
- 成功和失敗結果場景測試
- 不同鍵類型測試
- 空值和邊界情況測試

### 3. TestItemRegistry

測試 `ItemRegistry` 類別的核心註冊功能：

- 構造函數和基本屬性測試
- Register 方法測試
- Unregister 方法測試
- Find 方法測試
- 最大項目數限制測試
- 重複鍵處理測試
- Clear 方法測試
- GetLastRegisteredKey 測試
- 不同值類型測試

### 4. TestRegistryIntegration

測試整合場景：

- 完整工作流程測試
- 多個註冊器協同工作
- 大規模操作測試
- 錯誤恢復測試
- 併發操作模擬測試
- 數據完整性測試
- 性能場景測試
- 邊界情況測試

## Mock 類別

### MockRegistry

提供用於測試的模擬註冊器：

```mql4
// 創建字符串鍵的 Mock 註冊器
MockRegistry<string, int>* mockRegistry = MockRegistryFactory::CreateStringKeyRegistry<int>("測試");

// 設置失敗場景
mockRegistry.SetShouldFailRegister(true);
mockRegistry.SetCustomFailMessage("測試失敗");

// 檢查調用次數
int callCount = mockRegistry.GetRegisterCallCount();
```

### MockRegistryFactory

提供便捷的 Mock 註冊器創建方法：

- `CreateStringKeyRegistry<Val>()` - 創建字符串鍵註冊器
- `CreateIntKeyRegistry<Val>()` - 創建整數鍵註冊器
- `CreateFailingRegistry<Key, Val>()` - 創建會失敗的註冊器
- `CreateLimitedRegistry<Key, Val>()` - 創建容量限制的註冊器
- `CreatePrefilledStringIntRegistry()` - 創建預填充的註冊器

## 測試最佳實踐

### 1. 測試命名

- 使用描述性的測試名稱
- 包含測試的功能和預期結果
- 使用中文註解說明測試目的

### 2. 測試結構

```mql4
void TestSpecificFeature()
{
    SetUp();        // 準備測試環境

    // 執行測試邏輯
    // 使用斷言驗證結果

    TearDown();     // 清理測試環境
}
```

### 3. 資源管理

- 在測試中創建的對象必須在測試結束時清理
- 使用 `SetUp()` 和 `TearDown()` 方法管理資源
- 避免記憶體洩漏

### 4. 測試獨立性

- 每個測試應該獨立運行
- 不依賴其他測試的執行順序
- 不共享狀態

## 高級測試功能

### 性能測試

```mql4
void OnInit()
{
    RunRegistryPerformanceTests();
}
```

### 壓力測試

```mql4
void OnInit()
{
    RegistryStressTest();
}
```

### 環境驗證

```mql4
void OnInit()
{
    ValidateRegistryTestEnvironment();
}
```

### 完整測試套件

```mql4
void OnInit()
{
    FullRegistryAdvanceTestSuite();
}
```

## 持續整合

### CI 測試

```mql4
void OnInit()
{
    CIRegistryAdvanceTests();
}
```

### 回歸測試

```mql4
void OnInit()
{
    RunRegistryRegressionTests();
}
```

### 測試報告

```mql4
void OnInit()
{
    GenerateRegistryTestReport();
}
```

## 故障排除

### 常見問題

1. **編譯錯誤**

   - 檢查所有依賴檔案是否正確包含
   - 確認檔案路徑正確
   - 檢查模板語法

2. **測試失敗**

   - 查看詳細的錯誤消息
   - 檢查被測試的代碼是否有變更
   - 驗證測試數據的正確性

3. **記憶體問題**
   - 確保所有 `new` 操作都有對應的 `delete`
   - 檢查指針是否正確初始化
   - 注意模板類別的記憶體管理

### 調試技巧

1. **使用詳細模式**

   ```mql4
   RegistryAdvanceTestRunner* runner = new RegistryAdvanceTestRunner(true, true, true);
   ```

2. **運行單個測試**

   ```mql4
   RunSpecificRegistryAdvanceTest("TestRegistryItem");
   ```

3. **檢查測試環境**

   ```mql4
   ValidateRegistryTestEnvironment();
   ```

4. **開發者快速測試**
   ```mql4
   DevQuickRegistryTest();
   ```

## 擴展測試

### 添加新測試

1. 在適當的目錄創建新的測試檔案
2. 繼承 `TestCase` 基類
3. 實現 `RunTests()` 方法
4. 在 `TestRunner.mqh` 中添加新測試

### 添加新斷言

在 `TestFramework.mqh` 的 `Assert` 類別中添加新方法。

### 添加新 Mock 類別

在 `mock/` 目錄下創建新的 Mock 實現。

## 文檔輸出功能

### 概述

RegistryAdvance 測試模組現在支援將測試結果輸出為 txt 文檔，提供詳細的測試報告和摘要。

### 文檔輸出功能

#### 基本使用

```mql4
// 運行所有測試並生成文檔
RunAllRegistryAdvanceTestsWithDocs();

// 運行單元測試並生成文檔
RunRegistryAdvanceUnitTestsWithDocs();

// 運行整合測試並生成文檔
RunRegistryAdvanceIntegrationTestsWithDocs();

// 運行特定測試並生成文檔
RunSpecificRegistryAdvanceTestWithDocs("TestRegistryItem");
```

#### 自定義文檔選項

```mql4
// 基本自定義文檔輸出選項
RunRegistryAdvanceTestsWithCustomDocs(
    true,                           // 生成完整報告
    true,                           // 生成摘要
    "MyCustomTestReports"           // 自定義輸出目錄
);
```

#### 通過測試顯示選項

```mql4
// 限制顯示通過測試數量
RunRegistryAdvanceTestsWithLimitedDisplay(5);          // 只顯示前5個
RunRegistryAdvanceTestsWithLimitedDisplay(0);          // 不顯示通過的測試

// 無限制顯示所有通過的測試
RunRegistryAdvanceTestsWithUnlimitedDisplay();

// 程式化設置顯示選項
RegistryAdvanceTestRunnerWithDocs* runner = new RegistryAdvanceTestRunnerWithDocs();
runner.SetPassedTestsDisplayOptions(20);               // 顯示前20個
runner.SetUnlimitedPassedTestsDisplay();               // 無限制顯示
runner.RunAllTestsWithDocs();
delete runner;
```

#### 完整測試套件

```mql4
// 運行完整測試套件並生成文檔
FullRegistryAdvanceTestSuiteWithDocs();
```

### 生成的文檔

#### 完整報告 (RegistryAdvance_TestReport_YYYYMMDD_HHMM.txt)

- **測試執行摘要**: 總測試數、通過數、失敗數、成功率
- **詳細統計信息**: 通過率、失敗率等
- **執行時間統計**: 總時間、平均時間、最長/最短執行時間
- **失敗測試詳情**: 包含具體錯誤信息和測試類別（始終完整顯示）
- **通過測試列表**: 根據顯示設置顯示通過的測試及執行時間
- **按類別分組結果**: 每個測試類別的詳細統計

#### 通過測試顯示選項說明

- **默認設置**: 顯示前 10 個通過的測試
- **限制顯示**:
  - 正數 (如 5, 15, 20): 顯示指定數量的通過測試
  - 0: 不顯示通過的測試（只顯示失敗的測試）
- **無限制顯示**: -1 或使用 `SetUnlimitedPassedTestsDisplay()` 顯示所有通過的測試
- **智能提示**: 當有更多測試時，會顯示省略信息和設置提示

#### 測試摘要 (RegistryAdvance_TestReport_Summary_YYYYMMDD_HHMM.txt)

- **簡化摘要**: 基本測試統計信息
- **快速查看**: 適合快速了解測試結果
- **輕量級**: 文件較小，便於分享

### 文檔保存位置

- **默認位置**: `MQL4\Files\TestReports\`
- **自定義位置**: `MQL4\Files\[自定義目錄名]\`
- **文件命名**: 包含時間戳，避免文件覆蓋

### 使用示例

詳細的使用示例請參考以下文件：

- `DocumentOutputExample.mqh`: 基本文檔輸出功能示例

包含的示例：

- 基本文檔輸出示例
- 分類測試文檔輸出
- 特定測試類別文檔輸出
- 自定義文檔選項示例
- 通過測試顯示選項配置
- 故障排除指南

### 最佳實踐建議

#### 顯示數量選擇指南

- **開發階段**: 使用無限制顯示 (`-1`)，查看所有測試詳情
- **日常測試**: 使用默認設置 (10 個) 或適中限制 (5-15 個)
- **生產環境**: 使用較小限制 (3-5 個)，保持文檔簡潔
- **調試失敗**: 使用限制 0，只關注失敗的測試
- **報告展示**: 使用適中限制 (5-10 個)，平衡詳細度和可讀性

#### 性能考慮

- **文檔大小**: 無限制顯示會產生較大的文檔文件
- **生成速度**: 限制顯示可以提高文檔生成速度
- **記憶體使用**: 測試結果收集不受顯示限制影響

### 故障排除

如果文檔生成失敗，請檢查：

1. **文件權限**: 確保 `MQL4\Files\` 目錄可寫
2. **磁盤空間**: 確保有足夠的磁盤空間
3. **防毒軟件**: 檢查是否阻止文件創建
4. **目錄結構**: MQL4 會自動創建必要的子目錄

## 版本歷史

- **v1.0** - 初始版本

  - 完整的單元測試覆蓋
  - 整合測試場景
  - Mock 類別支援
  - 性能和壓力測試
  - 詳細的測試報告

- **v1.1** - 文檔輸出功能
  - 增強版測試運行器 (TestRunnerAdvanced)
  - 測試文檔生成器 (TestDocumentGeneratorFixed)
  - 支援文檔輸出的測試運行器 (TestRunnerWithDocuments)
  - 完整的測試報告和摘要生成
  - 可配置的通過測試顯示選項
  - 文檔輸出功能使用示例
  - 詳細的執行時間統計
  - 按測試類別分組的結果統計

## 貢獻指南

1. 遵循現有的代碼風格
2. 為新功能添加相應的測試
3. 確保所有測試通過
4. 更新相關文檔
5. 考慮性能影響
