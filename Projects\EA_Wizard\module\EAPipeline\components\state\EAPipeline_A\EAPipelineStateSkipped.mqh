//+------------------------------------------------------------------+
//|                                      EAPipelineStateSkipped.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "EAPipelineState.mqh"
#include "EAPipelineStatePending.mqh"

//+------------------------------------------------------------------+
//| 流水線已跳過狀態 - 實現已跳過狀態的行為                           |
//+------------------------------------------------------------------+

class EAPipelineStateSkipped : public EAPipelineState
{
public:
    // 構造函數
    EAPipelineStateSkipped()
    : EAPipelineState(EA_PIPELINE_STATE_SKIPPED, "已跳過") {}
    
    // 析構函數
    ~EAPipelineStateSkipped() {}
    
    // 執行流水線
    void Execute(EAPipeline_A* pipeline) override;
    
    // 重置流水線狀態
    void Restore(EAPipeline_A* pipeline) override;
};


void EAPipelineStateSkipped::Execute(EAPipeline_A* pipeline)
{
    // 已跳過狀態下不應該再次執行
    Print("警告: 流水線 '", pipeline.GetName(), "' 已被跳過，不能執行");
}


void EAPipelineStateSkipped::Restore(EAPipeline_A* pipeline)
{
    // 將狀態轉換為待執行
    pipeline.SetState(new EAPipelineStatePending());
    pipeline.Restore();
}
