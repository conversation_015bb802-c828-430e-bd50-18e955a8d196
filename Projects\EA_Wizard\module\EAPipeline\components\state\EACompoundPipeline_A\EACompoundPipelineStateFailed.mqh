//+------------------------------------------------------------------+
//|                                       EACompoundPipelineStateFailed.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "EACompoundPipelineState.mqh"
#include "EACompoundPipelineStatePending.mqh"

//+------------------------------------------------------------------+
//| 流水線執行失敗狀態 - 實現執行失敗狀態的行為                       |
//+------------------------------------------------------------------+

class EACompoundPipelineStateFailed : public EACompoundPipelineState
{
public:
    // 構造函數
    EACompoundPipelineStateFailed()
    : EACompoundPipelineState(EA_PIPELINE_STATE_FAILED, "執行失敗") {}
    
    // 析構函數
    ~EACompoundPipelineStateFailed() {}
    
    // 執行流水線
    void Execute(EACompoundPipeline_A* pipeline) override;
    
    // 重置流水線狀態
    void Restore(EACompoundPipeline_A* pipeline) override;
};


void EACompoundPipelineStateFailed::Execute(EACompoundPipeline_A* pipeline)
{
    // 執行失敗狀態下不應該再次執行
    Print("警告: 流水線 '", pipeline.GetName(), "' 執行失敗，需要先重置才能再次執行");
}


void EACompoundPipelineStateFailed::Restore(EACompoundPipeline_A* pipeline)
{
    // 將狀態轉換為待執行
    pipeline.SetState(new EACompoundPipelineStatePending());
    pipeline.GetComposite().Restore();
}
