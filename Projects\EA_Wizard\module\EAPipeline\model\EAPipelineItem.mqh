//+------------------------------------------------------------------+
//|                                               EAPipelineItem.mqh |
//|                                                       EA_Wizard |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../../PipelineAdvance/interface/IPipeline.mqh"
#include "../../RegistryAdvance/model/RegistryItem.mqh"

//+------------------------------------------------------------------+
//| 流水線項目基本結構 - 實現接口                                    |
//+------------------------------------------------------------------+
class EAPipelineItem
{
private:
    RegistryItem<IPipeline*>* m_item;  // 項目值
    IPipeline* m_value;    // 項目值

public:
    // 構造函數
    EAPipelineItem(RegistryItem<IPipeline*>* item)
        : m_item(item), m_value(dynamic_cast<IPipeline*>(item.GetValue()))
    {
    }

    // 析構函數
    ~EAPipelineItem()
    {
        // 不需要釋放資源
    }

    // 獲取項目ID
    string GetId() const { return m_item.GetId(); }

    // 獲取項目名稱
    string GetName() const { return m_item.GetName(); }

    // 獲取項目描述
    string GetDescription() const { return m_item.GetDescription(); }

    // 獲取項目值
    IPipeline* GetValue() const { return m_value; }

    // 獲取創建時間
    datetime GetCreateTime() const { return m_item.GetCreateTime(); }

    // 獲取項目類型
    string GetType() const { return m_item.GetType(); }

    // 轉換為字符串（用於調試）
    string ToString() const
    {
        return StringFormat("ID: %s, Name: %s, Description: %s, Type: %s, CreateTime: %s",
                           m_item.GetId(), m_item.GetName(), m_item.GetDescription(), m_item.GetType(),
                           TimeToString(m_item.GetCreateTime()));
    }
};
