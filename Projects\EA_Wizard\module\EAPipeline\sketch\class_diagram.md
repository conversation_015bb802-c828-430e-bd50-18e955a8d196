```mermaid
classDiagram
    class Pipeline {
        <<abstract>>
        -string m_name
        -string m_type
        -PipelineResult* m_result
        -bool m_executed
        +<PERSON>peline(string name, string type)
        +virtual void Execute() = 0
        +string GetName()
        +string GetType()
        +PipelineResult* GetResult()
        +void Restore()
        +bool IsExecuted()
        #void SetResult(bool success, string message, string source)
    }

    class ItemRegistry~Val~ {
        -string m_name
        -string m_type
        -int m_maxItems
        +ItemRegistry(string name, string type, int maxItems)
        +RegistryResult~string~* Register(string name, string description, Val value)
        +RegistryItem~Val~* GetItem(string key)
    }

    class PipelineComposite {
        -IPipeline[] m_children
        +PipelineComposite(string name, string type)
        +void Add(IPipeline* pipeline)
        +void Remove(IPipeline* pipeline)
        +IPipeline* GetChild(int index)
        +void Execute() override
    }

    %% 新類
    class EAPipeline {
        <<abstract>>
        +EAPipeline(string name)
        +~EAPipeline()
        +RegistryResult~string~* Register(string name, string description, void* value)
        +RegistryItem~void*~* GetItem(string name)
        +void Execute() virtual
    }
    note for EAPipeline "單例模式實現"

    class EARegistry {
        -static EARegistry* m_instance
        -EARegistry(string name, string type, int maxItems)
        -~EARegistry()
        +static EARegistry* GetInstance()
    }
    note for EARegistry "單例模式實現"

    class EACompoundPipeline {
        -PipelineComposite* m_composite
        -string m_name
        -string m_type
        +EACompoundPipeline(PipelineComposite* composite, string name, string type)
        +virtual ~EACompoundPipeline()
        +virtual void Execute() override
        +virtual string GetName() override
        +virtual string GetType() override
        +virtual PipelineResult* GetResult() override
        +virtual void Restore() override
        +virtual bool IsExecuted() override
        +virtual PipelineComposite* GetComposite()
        +virtual void SetComposite(PipelineComposite* composite)
        +virtual PipelineResult* Add(IPipeline* child)
        +virtual PipelineResult* Remove(IPipeline* child)
        +virtual int GetMaxItems()
        +virtual int GetCount()
        +virtual int GetAllItems(IPipeline* &items[])
    }
    note for EACompoundPipeline "裝飾者模式實現，擁有PipelineComposite的所有方法"

    class EALogCompoundPipeline {
        -PipelineComposite* m_composite
        -string m_name
        -string m_type
        -EAFileLog* m_logger
        +EALogCompoundPipeline(PipelineComposite* composite, string name, string type)
        +virtual ~EALogCompoundPipeline()
        +virtual void Execute() override
        +virtual string GetName() override
        +virtual string GetType() override
        +virtual PipelineResult* GetResult() override
        +virtual void Restore() override
        +virtual bool IsExecuted() override
        +virtual PipelineComposite* GetComposite()
        +virtual void SetComposite(PipelineComposite* composite)
        +virtual PipelineResult* Add(IPipeline* child)
        +virtual PipelineResult* Remove(IPipeline* child)
        +virtual int GetMaxItems()
        +virtual int GetCount()
        +virtual int GetAllItems(IPipeline* &items[])
    }
    note for EALogCompoundPipeline "日誌裝飾者模式實現，擁有PipelineComposite的所有方法"

    class EAPipelineBase {
        -Pipeline* m_wrappee
        -string m_name
        -string m_type
        +EAPipelineBase(Pipeline* pipeline, string type)
        +EAPipelineBase(EAPipelineBase* pipeline, string type)
        +virtual ~EAPipelineBase()
        +virtual void Execute() override
        +virtual string GetName() override
        +virtual string GetType() override
        +virtual PipelineResult* GetResult() override
        +virtual void Restore() override
        +virtual bool IsExecuted() override
        +virtual Pipeline* GetPipeline()
        #virtual void SetPipeline(Pipeline* pipeline)
    }
    note for EAPipelineBase "裝飾者模式基類，包裝Pipeline對象"

    class EALogPipeline {
        -Pipeline* m_wrappee
        -string m_name
        -string m_type
        -EAFileLog* m_logger
        +EALogPipeline(Pipeline* pipeline, string type, EAFileLog* logger)
        +EALogPipeline(EAPipelineBase* pipeline, string type, EAFileLog* logger)
        +virtual ~EALogPipeline()
        +virtual void Execute() override
        +virtual string GetName() override
        +virtual string GetType() override
        +virtual PipelineResult* GetResult() override
        +virtual void Restore() override
        +virtual bool IsExecuted() override
        +virtual Pipeline* GetPipeline()
        #virtual void SetPipeline(Pipeline* pipeline)
    }
    note for EALogPipeline "日誌裝飾者模式實現，包裝EAPipelineBase"

    class EAErrorHandlingPipeline {
        -Pipeline* m_wrappee
        -string m_name
        -string m_type
        -EAErrorHandler* m_error_handler
        +EAErrorHandlingPipeline(Pipeline* pipeline, string type, EAErrorHandler* error_handler)
        +EAErrorHandlingPipeline(EAPipelineBase* pipeline, string type, EAErrorHandler* error_handler)
        +virtual ~EAErrorHandlingPipeline()
        +virtual PipelineResult* GetResult() override
    }
    note for EAErrorHandlingPipeline "錯誤處理裝飾者模式實現，只提供GetResult()方法"

    class EAPipelineManager {
        <<singleton>>
        -static EAPipelineManager* m_instance
        -EAPipelineManager()
        -~EAPipelineManager()
        +static EAPipelineManager* GetInstance()
        +PipelineResult* AddPipeline(EAPipeline* pipeline)
        +PipelineResult* RemovePipeline(EAPipeline* pipeline)
        void Clear()
        +int GetPipelineCount()
        +int GetMaxPipelines()
        +PipelineResult* ExecutePipeline(string name)
    }
    note for EAPipelineManager "管理所有 EAPipeline 實例"

    %% 關係
    Pipeline <|-- EAPipeline
    Pipeline <|-- PipelineComposite
    IPipeline <|.. EACompoundPipeline
    IPipeline <|.. EALogCompoundPipeline
    IPipeline <|.. EAPipelineBase
    EAPipelineBase <|-- EALogPipeline
    EAPipelineBase <|-- EAErrorHandlingPipeline
    EACompoundPipeline o-- PipelineComposite
    EALogCompoundPipeline o-- PipelineComposite
    EALogCompoundPipeline o-- EAFileLog
    EAPipelineBase o-- Pipeline
    EALogPipeline o-- EAFileLog
    EAErrorHandlingPipeline o-- EAErrorHandler
    ItemRegistry~Val~ <|-- EARegistry
    EAPipelineManager o-- EAPipeline
```
