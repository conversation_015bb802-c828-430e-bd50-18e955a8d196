//+------------------------------------------------------------------+
//|                                        DocumentOutputExample.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "RunAllTests.mqh"

//+------------------------------------------------------------------+
//| PipelineAdvance 測試文檔輸出功能使用示例                          |
//| 這個檔案展示了如何使用新的文檔輸出功能                             |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 基本使用示例                                                     |
//+------------------------------------------------------------------+
void BasicDocumentOutputExample()
{
    Print("=== 基本文檔輸出示例 ===");
    
    // 1. 運行所有測試並生成完整文檔
    Print("1. 運行所有測試並生成完整文檔:");
    RunAllPipelineAdvanceTestsWithDocs();
    
    Print("");
    Print("文檔將保存在 MQL4\\Files\\TestReports\\ 目錄中");
    Print("包含完整報告和摘要兩個文件");
}

//+------------------------------------------------------------------+
//| 分類測試文檔輸出示例                                             |
//+------------------------------------------------------------------+
void CategoryTestDocumentExample()
{
    Print("=== 分類測試文檔輸出示例 ===");
    
    // 1. 只運行單元測試並生成文檔
    Print("1. 運行單元測試並生成文檔:");
    RunPipelineAdvanceUnitTestsWithDocs();
    
    Print("");
    
    // 2. 只運行整合測試並生成文檔
    Print("2. 運行整合測試並生成文檔:");
    RunPipelineAdvanceIntegrationTestsWithDocs();
    
    Print("");
    Print("每種測試類型都會生成獨立的文檔");
}

//+------------------------------------------------------------------+
//| 特定測試類別文檔輸出示例                                         |
//+------------------------------------------------------------------+
void SpecificTestDocumentExample()
{
    Print("=== 特定測試類別文檔輸出示例 ===");
    
    // 測試不同的測試類別
    string testClasses[] = {
        "TestPipelineResult",
        "TestPipelineComposite", 
        "TestPipelineStates",
        "TestPipelineIntegration"
    };
    
    for(int i = 0; i < ArraySize(testClasses); i++)
    {
        Print(StringFormat("%d. 運行 %s 測試並生成文檔:", i+1, testClasses[i]));
        RunSpecificPipelineAdvanceTestWithDocs(testClasses[i]);
        Print("");
    }
    
    Print("每個測試類別都會生成獨立的文檔");
}

//+------------------------------------------------------------------+
//| 自定義文檔輸出選項示例                                           |
//+------------------------------------------------------------------+
void CustomDocumentOptionsExample()
{
    Print("=== 自定義文檔輸出選項示例 ===");
    
    // 1. 只生成完整報告，不生成摘要
    Print("1. 只生成完整報告:");
    RunPipelineAdvanceTestsWithCustomDocs(true, false, "FullReportsOnly");
    
    Print("");
    
    // 2. 只生成摘要，不生成完整報告
    Print("2. 只生成摘要:");
    RunPipelineAdvanceTestsWithCustomDocs(false, true, "SummaryOnly");
    
    Print("");
    
    // 3. 生成到自定義目錄
    Print("3. 生成到自定義目錄:");
    RunPipelineAdvanceTestsWithCustomDocs(true, true, "CustomTestReports");
    
    Print("");
    Print("文檔已保存到不同的自定義目錄中");
}

//+------------------------------------------------------------------+
//| 完整測試套件文檔輸出示例                                         |
//+------------------------------------------------------------------+
void FullTestSuiteDocumentExample()
{
    Print("=== 完整測試套件文檔輸出示例 ===");
    
    // 運行完整的測試套件並生成文檔
    Print("運行完整測試套件（包含環境驗證、性能測試等）:");
    FullPipelineAdvanceTestSuiteWithDocs();
    
    Print("");
    Print("完整測試套件包含:");
    Print("• 環境驗證");
    Print("• 所有單元測試和整合測試");
    Print("• 性能測試");
    Print("• 完整的文檔輸出");
}

//+------------------------------------------------------------------+
//| 文檔內容說明示例                                                 |
//+------------------------------------------------------------------+
void DocumentContentExplanationExample()
{
    Print("=== 文檔內容說明 ===");
    
    Print("生成的文檔包含以下內容:");
    Print("");
    
    Print("📄 完整報告 (PipelineAdvance_TestReport_YYYYMMDD_HHMM.txt):");
    Print("• 測試執行摘要（總數、通過、失敗、成功率）");
    Print("• 詳細統計信息");
    Print("• 執行時間統計（總時間、平均時間、最長/最短時間）");
    Print("• 失敗測試詳情（包含錯誤信息）");
    Print("• 通過測試列表");
    Print("• 按測試類別分組的結果");
    Print("");
    
    Print("📄 測試摘要 (PipelineAdvance_TestReport_Summary_YYYYMMDD_HHMM.txt):");
    Print("• 簡化的測試執行摘要");
    Print("• 基本統計信息");
    Print("• 適合快速查看測試結果");
    Print("");
    
    Print("📁 文檔保存位置:");
    Print("• 默認目錄: MQL4\\Files\\TestReports\\");
    Print("• 自定義目錄: MQL4\\Files\\[自定義目錄名]\\");
    Print("• 文件名包含時間戳，避免覆蓋");
}

//+------------------------------------------------------------------+
//| 故障排除示例                                                     |
//+------------------------------------------------------------------+
void TroubleshootingExample()
{
    Print("=== 故障排除示例 ===");
    
    Print("如果文檔生成失敗，請檢查:");
    Print("");
    
    Print("1. 文件權限:");
    Print("   • 確保 MQL4\\Files\\ 目錄可寫");
    Print("   • 檢查防毒軟件是否阻止文件創建");
    Print("");
    
    Print("2. 磁盤空間:");
    Print("   • 確保有足夠的磁盤空間");
    Print("   • 測試報告通常很小，但仍需檢查");
    Print("");
    
    Print("3. 目錄結構:");
    Print("   • MQL4 會自動創建 Files 子目錄");
    Print("   • 確保沒有同名文件被其他程序佔用");
    Print("");
    
    Print("4. 調試信息:");
    Print("   • 查看 Print 輸出中的錯誤信息");
    Print("   • 檢查 GetLastError() 的返回值");
}

//+------------------------------------------------------------------+
//| 主要示例函數 - 運行所有示例                                       |
//+------------------------------------------------------------------+
void RunAllDocumentOutputExamples()
{
    Print("╔══════════════════════════════════════════════════════════════╗");
    Print("║           PipelineAdvance 文檔輸出功能示例                   ║");
    Print("║                      EA_Wizard                               ║");
    Print("╚══════════════════════════════════════════════════════════════╝");
    Print("");
    
    // 運行所有示例
    BasicDocumentOutputExample();
    Print("");
    
    CategoryTestDocumentExample();
    Print("");
    
    SpecificTestDocumentExample();
    Print("");
    
    CustomDocumentOptionsExample();
    Print("");
    
    FullTestSuiteDocumentExample();
    Print("");
    
    DocumentContentExplanationExample();
    Print("");
    
    TroubleshootingExample();
    Print("");
    
    Print("╔══════════════════════════════════════════════════════════════╗");
    Print("║                    示例執行完成                              ║");
    Print("║     請檢查 MQL4\\Files\\ 目錄中生成的測試文檔                ║");
    Print("╚══════════════════════════════════════════════════════════════╝");
}

//+------------------------------------------------------------------+
//| 快速開始示例                                                     |
//+------------------------------------------------------------------+
void QuickStartExample()
{
    Print("=== 快速開始示例 ===");
    Print("最簡單的使用方式:");
    Print("");
    Print("// 在您的代碼中調用:");
    Print("TestPipelineAdvanceWithDocs();");
    Print("");
    Print("這將:");
    Print("• 運行所有 PipelineAdvance 測試");
    Print("• 生成完整的測試報告");
    Print("• 生成測試摘要");
    Print("• 保存到默認目錄");
    Print("");
    
    // 實際執行
    TestPipelineAdvanceWithDocs();
}
