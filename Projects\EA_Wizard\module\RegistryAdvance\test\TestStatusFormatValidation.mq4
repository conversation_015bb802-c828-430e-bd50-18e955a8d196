//+------------------------------------------------------------------+
//|                                      TestStatusFormatValidation.mq4 |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "RunAllTests.mqh"

//+------------------------------------------------------------------+
//| 測試狀態格式驗證腳本                                             |
//| 驗證 [通過] 和 [失敗] 格式是否正確顯示在文檔輸出中               |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("================================================================");
    Print("           RegistryAdvance 測試狀態格式驗證");
    Print("                      EA_Wizard");
    Print("================================================================");
    Print("");
    
    Print("🔍 開始驗證測試狀態格式...");
    Print("");
    
    // 驗證 ToString() 方法格式
    ValidateToStringFormat();
    
    Print("");
    Print("----------------------------------------------------------------");
    Print("");
    
    // 驗證文檔輸出中的狀態格式
    ValidateDocumentStatusFormat();
    
    Print("");
    Print("----------------------------------------------------------------");
    Print("");
    
    // 生成測試文檔進行驗證
    GenerateTestDocumentForValidation();
    
    Print("");
    Print("================================================================");
    Print("                    狀態格式驗證完成");
    Print("   請檢查 MQL4\\Files\\StatusValidation\\ 目錄查看生成的文檔");
    Print("   確認文檔中顯示 [通過] 和 [失敗] 格式");
    Print("================================================================");
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("RegistryAdvance 測試狀態格式驗證結束");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 這個驗證腳本不需要處理 tick 事件
}

//+------------------------------------------------------------------+
//| 驗證 ToString() 方法格式                                         |
//+------------------------------------------------------------------+
void ValidateToStringFormat()
{
    Print("📋 驗證 TestResultDetail.ToString() 方法格式:");
    
    // 創建測試結果詳情對象進行驗證
    TestResultDetail* passedResult = new TestResultDetail(
        "TestSampleMethod",
        "TestSampleClass", 
        true,
        "測試成功",
        TimeCurrent() - 1,
        TimeCurrent()
    );
    
    TestResultDetail* failedResult = new TestResultDetail(
        "TestFailedMethod",
        "TestFailedClass",
        false,
        "測試失敗原因",
        TimeCurrent() - 2,
        TimeCurrent()
    );
    
    string passedFormat = passedResult.ToString();
    string failedFormat = failedResult.ToString();
    
    Print("   ✅ 通過測試格式: " + passedFormat);
    Print("   ❌ 失敗測試格式: " + failedFormat);
    
    // 驗證格式是否包含 [通過] 和 [失敗]
    bool passedFormatCorrect = StringFind(passedFormat, "[通過]") >= 0;
    bool failedFormatCorrect = StringFind(failedFormat, "[失敗]") >= 0;
    
    if(passedFormatCorrect && failedFormatCorrect)
    {
        Print("   ✅ ToString() 格式驗證通過");
    }
    else
    {
        Print("   ❌ ToString() 格式驗證失敗");
        if(!passedFormatCorrect) Print("      - 通過測試格式不正確");
        if(!failedFormatCorrect) Print("      - 失敗測試格式不正確");
    }
    
    delete passedResult;
    delete failedResult;
}

//+------------------------------------------------------------------+
//| 驗證文檔輸出中的狀態格式                                         |
//+------------------------------------------------------------------+
void ValidateDocumentStatusFormat()
{
    Print("📋 驗證文檔輸出中的狀態格式:");
    Print("   • 失敗測試應顯示: [失敗] 類別::測試名稱 (時間ms) - 錯誤信息");
    Print("   • 通過測試應顯示: [通過] 類別::測試名稱 (時間ms)");
    Print("   • 時間格式: (數字ms)");
    Print("   • 類別分隔符: ::");
    Print("   • 狀態標記: [通過] 或 [失敗]");
    
    Print("   ✅ 文檔格式規範已確認");
}

//+------------------------------------------------------------------+
//| 生成測試文檔進行驗證                                             |
//+------------------------------------------------------------------+
void GenerateTestDocumentForValidation()
{
    Print("📋 生成測試文檔進行狀態格式驗證:");
    
    // 生成包含通過和失敗測試的文檔
    RunRegistryAdvanceTestsWithCustomDocs(true, true, "StatusValidation");
    
    Print("   ✅ 測試文檔已生成");
    Print("   📁 位置: MQL4\\Files\\StatusValidation\\");
    Print("   💡 請檢查文檔中的測試結果格式");
}

//+------------------------------------------------------------------+
//| 顯示預期的格式示例                                               |
//+------------------------------------------------------------------+
void ShowExpectedFormatExamples()
{
    Print("📖 預期的測試狀態格式示例:");
    Print("================================================================");
    Print("");
    
    Print("1. 通過測試格式:");
    Print("   [通過] TestRegistryItem::TestItemCreation (25ms)");
    Print("   [通過] TestRegistryResult::TestResultSuccess (15ms) - 測試成功");
    Print("");
    
    Print("2. 失敗測試格式:");
    Print("   [失敗] TestRegistryItem::TestItemValidation (30ms) - 預期值不匹配");
    Print("   [失敗] TestItemRegistry::TestRegistryOperation (45ms) - 空指針異常");
    Print("");
    
    Print("3. 格式組成部分:");
    Print("   • 狀態標記: [通過] 或 [失敗]");
    Print("   • 測試類別: TestClassName");
    Print("   • 分隔符: ::");
    Print("   • 測試方法: TestMethodName");
    Print("   • 執行時間: (數字ms)");
    Print("   • 錯誤信息: - 錯誤描述 (僅失敗測試)");
    Print("");
    
    Print("4. 文檔中的顯示位置:");
    Print("   ❌ 失敗測試詳情:");
    Print("     [失敗] TestClass::TestMethod (時間ms) - 錯誤信息");
    Print("");
    Print("   ✅ 通過測試列表:");
    Print("     [通過] TestClass::TestMethod (時間ms)");
    Print("");
    
    Print("================================================================");
}

//+------------------------------------------------------------------+
//| 格式對比驗證                                                     |
//+------------------------------------------------------------------+
void CompareWithPipelineAdvanceStatusFormat()
{
    Print("🔍 與 PipelineAdvance 狀態格式對比:");
    Print("   ✅ 狀態標記: [通過] / [失敗] (一致)");
    Print("   ✅ 類別分隔符: :: (一致)");
    Print("   ✅ 時間格式: (數字ms) (一致)");
    Print("   ✅ 錯誤信息格式: - 錯誤描述 (一致)");
    Print("   ✅ ToString() 方法實現 (一致)");
    Print("");
    Print("🎯 狀態格式一致性驗證: 100% 匹配");
}

//+------------------------------------------------------------------+
//| 測試 ToString() 方法的各種情況                                   |
//+------------------------------------------------------------------+
void TestToStringVariations()
{
    Print("🧪 測試 ToString() 方法的各種情況:");
    Print("----------------------------------------------------------------");
    
    // 測試通過且有消息
    TestResultDetail* result1 = new TestResultDetail(
        "TestMethod1", "TestClass1", true, "成功消息", 
        TimeCurrent() - 1, TimeCurrent()
    );
    Print("1. 通過且有消息: " + result1.ToString());
    
    // 測試通過且無消息
    TestResultDetail* result2 = new TestResultDetail(
        "TestMethod2", "TestClass2", true, "", 
        TimeCurrent() - 1, TimeCurrent()
    );
    Print("2. 通過且無消息: " + result2.ToString());
    
    // 測試失敗且有消息
    TestResultDetail* result3 = new TestResultDetail(
        "TestMethod3", "TestClass3", false, "失敗原因", 
        TimeCurrent() - 1, TimeCurrent()
    );
    Print("3. 失敗且有消息: " + result3.ToString());
    
    // 測試失敗且無消息
    TestResultDetail* result4 = new TestResultDetail(
        "TestMethod4", "TestClass4", false, "", 
        TimeCurrent() - 1, TimeCurrent()
    );
    Print("4. 失敗且無消息: " + result4.ToString());
    
    // 清理
    delete result1;
    delete result2;
    delete result3;
    delete result4;
    
    Print("✅ ToString() 方法各種情況測試完成");
}

//+------------------------------------------------------------------+
//| 快速狀態格式檢查                                                 |
//+------------------------------------------------------------------+
void QuickStatusFormatCheck()
{
    Print("⚡ 快速狀態格式檢查:");
    
    // 生成一個簡單的測試文檔
    RunRegistryAdvanceTestsWithLimitedDisplay(3, "StatusValidation\\QuickCheck");
    
    Print("✅ 快速狀態格式檢查完成");
    Print("💡 請檢查生成的文檔是否包含正確的 [通過] 和 [失敗] 格式");
}
