//+------------------------------------------------------------------+
//|                                                    BasicTest.mq4 |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== 基本測試開始 ===");
    
    // 測試基本的 Print 功能
    Print("測試 Print 功能正常");
    
    // 測試字符串格式化
    string testString = StringFormat("測試數字: %d, 測試字符串: %s", 123, "Hello");
    Print(testString);
    
    // 測試時間功能
    datetime currentTime = TimeCurrent();
    Print(StringFormat("當前時間: %s", TimeToString(currentTime)));
    
    Print("=== 基本測試完成 ===");
}
