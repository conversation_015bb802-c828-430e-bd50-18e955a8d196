#property strict

enum ENUM_ORDER_PROPERTY
{
    ORDER_PROPERTY_TICKET,
    ORDER_PROPERTY_SYMBOL,
    ORDER_PROPERTY_TYPE,
    ORDER_PROPERTY_LOTS,
    ORDER_PROPERTY_OPEN_PRICE,
    ORDER_PROPERTY_OPEN_TIME,
    ORDER_PROPERTY_STOP_LOSS,
    ORDER_PROPERTY_TAKE_PROFIT,
    ORDER_PROPERTY_CLOSE_TIME,
    ORDER_PROPERTY_CLOSE_PRICE,
    ORDER_PROPERTY_COMMISSION,
    ORDER_PROPERTY_SWAP,
    ORDER_PROPERTY_PROFIT,
    ORDER_PROPERTY_COMMENT,
    ORDER_PROPERTY_MAGIC_NUMBER,
    ORDER_PROPERTY_EXPIRATION
};