# PipelineAdvance_v1 模組文檔

本目錄包含 PipelineAdvance_v1 模組的設計文檔和圖表，用於說明模組的架構、類別關係和使用方法。

## 📋 文檔列表

### 設計文檔

- [類別圖](./class_diagram.md)：PipelineAdvance_v1 模組的完整類別結構和關係圖
- [序列圖](./sequence_diagram.md)：展示流水線執行的時序關係和交互流程
- [架構文檔](./architecture.md)：詳細的架構設計說明和設計原則

### 即將添加的文檔

- [API 參考](./api_reference.md)：完整的 API 文檔

## 🏗️ 模組概述

PipelineAdvance_v1 是一個簡化的流水線處理架構，專注於交易流水線的管理和執行。相比原始的 PipelineAdvance 模組，它具有以下特點：

### 核心組件

1. **ITradingPipeline**：交易流水線介面
2. **TradingPipeline**：抽象基類，實現基本功能
3. **TradingPipelineContainer**：統一容器類，合併了原有 CompositePipeline 和 PipelineGroup 功能
4. **TradingPipelineContainerManager**：動態容器管理器
5. **PipelineResult**：執行結果類

### 設計模式

- **組合模式**：TradingPipelineContainer 可以包含多個子流水線
- **模板方法模式**：TradingPipeline 定義執行流程骨架
- **統一容器管理模式**：動態管理多個容器
- **策略模式**：支持不同的執行模式和事件驅動

## 🚀 主要改進

### 相比原始 PipelineAdvance 模組

| 方面         | 原始模組                   | PipelineAdvance_v1 |
| ------------ | -------------------------- | ------------------ |
| **複雜度**   | 高（多層繼承、裝飾者模式） | 低（簡化架構）     |
| **學習成本** | 高                         | 低                 |
| **維護性**   | 中等                       | 高                 |
| **性能**     | 中等（多層抽象）           | 高（減少抽象層）   |
| **可測試性** | 中等                       | 高                 |

### 架構簡化

1. **移除複雜的裝飾者模式**
2. **減少繼承層次**
3. **專注核心功能**
4. **提高代碼可讀性**

## 📖 快速開始

### 基本使用

```mql4
// 1. 創建具體流水線
class MyPipeline : public TradingPipeline
{
public:
    MyPipeline(string name) : TradingPipeline(name) {}

protected:
    void Main() override
    {
        // 實現具體邏輯
        Print("執行流水線: ", GetName());
    }
};

// 2. 使用流水線
MyPipeline* pipeline = new MyPipeline("測試流水線");
pipeline.Execute();
delete pipeline;
```

### 容器和管理器

```mql4
// 創建管理器
TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("主管理器");

// 創建容器（統一了原有的複合流水線和流水線組功能）
TradingPipelineContainer* container = new TradingPipelineContainer(
    "Tick處理容器",
    "處理Tick事件的流水線容器",
    "TradingPipelineContainer",
    TRADING_TICK
);

// 添加子流水線到容器
MyPipeline* child1 = new MyPipeline("子流水線1");
MyPipeline* child2 = new MyPipeline("子流水線2");

container.AddPipeline(child1);
container.AddPipeline(child2);

// 添加容器到管理器
manager.AddContainer(container);

// 執行指定事件類型的所有容器
manager.Execute(TRADING_TICK);

// 清理
delete manager;
delete child1;
delete child2;
```

## 🧪 測試

模組包含完整的測試套件：

```
test/
├── RunAllTests.mqh                    # 測試入口
├── TestFramework.mqh                  # 測試框架
├── unit/                              # 單元測試
│   ├── TestTradingPipelineContainer.mqh
│   └── TestTradingPipelineContainerManager.mqh
└── integration/                       # 整合測試
    ├── TradingPipelineContainerTest.mq4
    └── CompileTest_TradingPipelineContainer.mq4
```

### 運行測試

```mql4
#include "test/RunAllTests.mqh"

void OnStart()
{
    RunAllPipelineAdvanceV1Tests();
}
```

## 📁 文件結構

```
PipelineAdvance_v1/
├── docs/                                    # 文檔目錄
│   ├── README.md                           # 本文檔
│   ├── class_diagram.md                    # 類別圖
│   ├── TradingPipelineContainer_Migration_Guide.md  # 遷移指南
│   └── Implementation_Summary.md           # 實施總結
├── test/                                   # 測試目錄
│   ├── RunAllTests.mqh
│   ├── TestFramework.mqh
│   ├── CompileTest.mq4
│   ├── unit/
│   └── integration/
├── TradingPipeline.mqh                     # 核心流水線類
├── TradingPipelineContainer.mqh            # 統一容器類
├── TradingPipelineContainerManager.mqh     # 容器管理器類
└── TradingEvent.mqh                        # 事件和枚舉定義
```

## 🔧 配置

### 常量配置

```mql4
const int DEFAULT_MAX_PIPELINES = 50;     // 默認最大流水線數量
const int DEFAULT_MAX_CONTAINERS = 10;    // 默認最大容器數量
const int DEFAULT_MAX_RETRIES = 3;        // 默認重試次數
const int DEFAULT_TIMEOUT_MS = 5000;      // 默認超時時間
```

### 類型配置

```mql4
const string TRADING_PIPELINE_TYPE = "TradingPipeline";
const string TRADING_PIPELINE_CONTAINER_TYPE = "TradingPipelineContainer";
```

## 🤝 貢獻

歡迎對模組進行改進和擴展：

1. **添加新的流水線類型**
2. **改進錯誤處理機制**
3. **增加性能監控功能**
4. **完善測試覆蓋率**

## 📝 版本歷史

- **v1.0.0**：初始版本，實現基本的流水線架構
- **v1.1.0**（計劃）：添加異步執行支持
- **v1.2.0**（計劃）：添加性能監控功能

## 📞 支援

如有問題或建議，請：

1. 查看文檔和示例代碼
2. 運行測試套件確認功能
3. 檢查錯誤日誌和執行結果
4. 參考原始 PipelineAdvance 模組的設計

---

_本文檔隨模組更新而持續維護_
