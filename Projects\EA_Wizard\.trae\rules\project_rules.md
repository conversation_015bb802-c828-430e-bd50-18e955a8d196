### Project Rules（MQL4 專案範例）

1. **程式碼風格（Code Style）**

   - 所有程式碼必須使用 4 個空格縮排，不可使用 tab。
   - 變數與函式命名一律採用 camelCase。
   - 每個函式上方需加上簡短註解，說明用途與參數。
   - 每行程式碼不得超過 100 字元。
   - 內容不可超過 500 字元。
   - 程式碼內容需保持一致，不可出現混亂的程式碼風格。

2. **語言與框架（Languages and Frameworks）**

   - 僅允許使用 MQL4 語言，程式必須能在 MetaTrader 4 平台運行。
   - 不可使用 MQL5 語法或功能。
   - 僅可使用標準 MQL4 函式庫，不可引用外部 DLL 或第三方庫。

3. **API 限制（API Restrictions）**

   - 禁止調用外部網路 API。
   - 不可使用 FileWrite、FileRead 等檔案操作函式，除非有明確需求並經過審核。
   - 不可使用 SendMail、SendNotification 等外部通訊功能。

4. **測試與文件（Testing and Documentation）**

   - 每個 EA 或指標需附上簡要使用說明（如：輸入參數、主要邏輯）。
   - 重要邏輯區塊需加上註解，說明設計思路。
   - 程式碼需通過 MetaEditor 編譯，無警告或錯誤。

5. **其他專案規範**
   - 所有回覆請以英文撰寫。
   - 程式碼範例需附上簡要說明。
   - 若有多種實現方式，僅提供最簡潔且效能良好的解法。
   - 不得提供任何交易建議或保證獲利的承諾。
