# TradingPipelineExplorer 模組文檔

## 📋 概述

TradingPipelineExplorer 是 PipelineAdvance_v1 模組中的一個新組件，專門用於探索和查詢 TradingPipelineContainerManager 中的流水線。它提供了豐富的查詢功能，讓開發者能夠輕鬆地根據交易階段或交易事件來查找和分析流水線。

## 🎯 核心功能

### 主要方法

1. **GetPipeline(ENUM_TRADING_STAGE)** - 根據交易階段獲取流水線
2. **GetPipeline(ENUM_TRADING_EVENT)** - 根據交易事件獲取流水線

### 組合成員

- **TradingPipelineContainerManager\*** - 容器管理器指針

## 🏗️ 類別結構

```mql4
class TradingPipelineExplorer
{
private:
    TradingPipelineContainerManager* m_manager;     // 容器管理器指針
    string m_name;                                  // 探索器名稱
    string m_type;                                  // 探索器類型
    string m_description;                           // 探索器描述

public:
    // 構造函數和析構函數
    TradingPipelineExplorer(TradingPipelineContainerManager* manager,
                           string name, string type, string description);
    virtual ~TradingPipelineExplorer();

    // 核心查詢方法
    ITradingPipeline* GetPipeline(ENUM_TRADING_STAGE stage);
    ITradingPipeline* GetPipeline(ENUM_TRADING_EVENT event);

    // 批量查詢方法
    int GetAllPipelinesByStage(ENUM_TRADING_STAGE stage, ITradingPipeline* &pipelines[]);
    int GetAllPipelinesByEvent(ENUM_TRADING_EVENT event, ITradingPipeline* &pipelines[]);

    // 存在性檢查方法
    bool HasPipelineForStage(ENUM_TRADING_STAGE stage);
    bool HasPipelineForEvent(ENUM_TRADING_EVENT event);

    // 統計方法
    int GetPipelineCountByEvent(ENUM_TRADING_EVENT event);
    int GetPipelineCountByStage(ENUM_TRADING_STAGE stage);
    int GetTotalPipelineCount();

    // 報告生成方法
    string GenerateExplorationReport();
    string GenerateStageReport();

    // 信息獲取方法
    string GetName() const;
    string GetType() const;
    string GetDescription() const;
    TradingPipelineContainerManager* GetManager() const;
    bool IsValid() const;
};
```

## 📚 使用方法

### 基本使用

```mql4
// 1. 創建管理器和容器
TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("MyManager");
TradingPipelineContainer* initContainer = new TradingPipelineContainer("InitContainer", "", INIT_START);
manager.SetContainer(TRADING_INIT, initContainer);

// 2. 創建探索器
TradingPipelineExplorer* explorer = new TradingPipelineExplorer(
    manager,
    "MyExplorer",
    "TradingPipelineExplorer",
    "我的探索器"
);

// 3. 根據階段查詢流水線
ITradingPipeline* pipeline = explorer.GetPipeline(INIT_START);
if(pipeline != NULL)
{
    Print("找到流水線: ", pipeline.GetName());
}

// 4. 根據事件查詢流水線
ITradingPipeline* eventPipeline = explorer.GetPipeline(TRADING_INIT);
if(eventPipeline != NULL)
{
    Print("找到事件流水線: ", eventPipeline.GetName());
}
```

### 批量查詢

```mql4
// 獲取所有初始化事件的流水線
ITradingPipeline* pipelines[];
int count = explorer.GetAllPipelinesByEvent(TRADING_INIT, pipelines);

Print("找到 ", count, " 個初始化流水線:");
for(int i = 0; i < count; i++)
{
    if(pipelines[i] != NULL)
    {
        Print("  - ", pipelines[i].GetName());
    }
}
```

### 統計信息

```mql4
// 獲取統計信息
int totalCount = explorer.GetTotalPipelineCount();
int initCount = explorer.GetPipelineCountByEvent(TRADING_INIT);
int tickCount = explorer.GetPipelineCountByEvent(TRADING_TICK);

Print("總流水線數: ", totalCount);
Print("初始化流水線數: ", initCount);
Print("Tick流水線數: ", tickCount);
```

### 報告生成

```mql4
// 生成探索報告
string report = explorer.GenerateExplorationReport();
Print(report);

// 生成階段映射報告
string stageReport = explorer.GenerateStageReport();
Print(stageReport);
```

## 🔍 階段到事件的映射

TradingPipelineExplorer 自動處理階段到事件的映射關係：

### 初始化階段 → TRADING_INIT

- INIT_START
- INIT_PARAMETERS
- INIT_VARIABLES
- INIT_ENVIRONMENT
- INIT_INDICATORS
- INIT_COMPLETE

### 交易階段 → TRADING_TICK

- TICK_DATA_FEED
- TICK_SIGNAL_ANALYSIS
- TICK_ORDER_MANAGEMENT
- TICK_RISK_CONTROL
- TICK_LOGGING

### 清理階段 → TRADING_DEINIT

- DEINIT_CLEANUP
- DEINIT_SAVE_STATE
- DEINIT_COMPLETE

## 🧪 測試

模組包含完整的測試套件：

```mql4
#include "test/TestTradingPipelineExplorer.mqh"

void OnStart()
{
    TestTradingPipelineExplorer* test = new TestTradingPipelineExplorer();
    test.RunAllTests();
    delete test;
}
```

## 📖 示例

完整的使用示例請參考：

- `examples/TradingPipelineExplorerExample.mqh`

## 🎨 設計特點

1. **只讀操作**：探索器只提供查詢功能，不修改容器狀態
2. **靈活查詢**：支援按階段和按事件兩種查詢方式
3. **批量處理**：支援批量查詢和統計
4. **豐富報告**：提供詳細的探索和映射報告
5. **不可變設計**：移除所有 Setter 方法，確保探索器狀態穩定
6. **遞歸搜索**：支援在嵌套容器中遞歸查找流水線
7. **類型安全**：新增 m_type 成員，提供類型信息

## 🔧 依賴關係

- TradingPipelineContainerManager.mqh
- TradingEvent.mqh
- TradingPipeline.mqh

## 📝 注意事項

1. 探索器不擁有管理器的所有權，不會自動刪除管理器
2. 查詢方法返回的指針可能為 NULL，使用前請檢查
3. 批量查詢方法會動態調整輸出數組大小
4. 階段映射基於 TradingEventUtils 的靜態方法
5. 探索器創建後其基本屬性（名稱、類型、描述）不可修改

## 🚀 未來擴展

可能的擴展功能：

- 支援自定義過濾條件
- 支援流水線執行狀態查詢
- 支援性能分析和監控
- 支援流水線依賴關係分析
