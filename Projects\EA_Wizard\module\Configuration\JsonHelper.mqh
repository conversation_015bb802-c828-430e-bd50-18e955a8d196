//+------------------------------------------------------------------+
//| Module: Configuration/JsonHelper.mqh                             |
//| This file is part of the EA_Wizard project.                      |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

// 防止重複包含
#ifndef _JSON_HELPER_MQH_
#define _JSON_HELPER_MQH_

#include "../mql4-lib-master/Format/Json.mqh"

//+------------------------------------------------------------------+
//| JSON輔助類                                                        |
//+------------------------------------------------------------------+
class JsonHelper
{
public:
    /**
     * 獲取布爾值，帶默認值
     * @param obj JSON對象
     * @param key 鍵
     * @param defaultValue 默認值
     * @return 布爾值
     */
    static bool getBoolean(const JsonObject* obj, const string key, const bool defaultValue)
    {
        if(obj == NULL || !obj.contains(key))
        {
            return defaultValue;
        }
        
        JsonBoolean* value = dynamic_cast<JsonBoolean*>(obj[key]);
        if(value == NULL)
        {
            return defaultValue;
        }
        
        return value.value;
    }
    
    /**
     * 獲取數值，帶默認值
     * @param obj JSON對象
     * @param key 鍵
     * @param defaultValue 默認值
     * @return 數值
     */
    static double getNumber(const JsonObject* obj, const string key, const double defaultValue)
    {
        if(obj == NULL || !obj.contains(key))
        {
            return defaultValue;
        }
        
        JsonNumber* value = dynamic_cast<JsonNumber*>(obj[key]);
        if(value == NULL)
        {
            return defaultValue;
        }
        
        return value.value;
    }
    
    /**
     * 獲取字符串，帶默認值
     * @param obj JSON對象
     * @param key 鍵
     * @param defaultValue 默認值
     * @return 字符串
     */
    static string getString(const JsonObject* obj, const string key, const string defaultValue)
    {
        if(obj == NULL || !obj.contains(key))
        {
            return defaultValue;
        }
        
        JsonString* value = dynamic_cast<JsonString*>(obj[key]);
        if(value == NULL)
        {
            return defaultValue;
        }
        
        return value.value;
    }
    
    /**
     * 獲取數組
     * @param obj JSON對象
     * @param key 鍵
     * @return 數組，不存在時返回NULL
     */
    static JsonArray* getArray(const JsonObject* obj, const string key)
    {
        if(obj == NULL || !obj.contains(key))
        {
            return NULL;
        }
        
        return dynamic_cast<JsonArray*>(obj[key]);
    }
    
    /**
     * 獲取對象
     * @param obj JSON對象
     * @param key 鍵
     * @return 對象，不存在時返回NULL
     */
    static JsonObject* getObject(const JsonObject* obj, const string key)
    {
        if(obj == NULL || !obj.contains(key))
        {
            return NULL;
        }
        
        return dynamic_cast<JsonObject*>(obj[key]);
    }
};

#endif // _JSON_HELPER_MQH_
