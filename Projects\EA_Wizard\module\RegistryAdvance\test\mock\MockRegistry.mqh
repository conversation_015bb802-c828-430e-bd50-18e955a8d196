//+------------------------------------------------------------------+
//|                                               MockRegistry.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../../Registry.mqh"

//+------------------------------------------------------------------+
//| Mock註冊器類別 - 用於測試的模擬註冊器                             |
//+------------------------------------------------------------------+
template<typename Key, typename Val>
class MockRegistry : public Registry<Key, Val>
{
private:
    bool m_shouldFailRegister;       // 是否應該註冊失敗
    bool m_shouldFailUnregister;     // 是否應該取消註冊失敗
    string m_customFailMessage;      // 自定義失敗消息
    int m_registerCallCount;         // Register方法調用次數
    int m_unregisterCallCount;       // Unregister方法調用次數
    int m_clearCallCount;            // Clear方法調用次數

public:
    // 構造函數
    MockRegistry(string name = "MockRegistry", string type = "MockRegistry", int maxItems = 100)
    : Registry<Key, Val>(name, type, maxItems), m_shouldFailRegister(false),
      m_shouldFailUnregister(false), m_customFailMessage(""),
      m_registerCallCount(0), m_unregisterCallCount(0), m_clearCallCount(0)
    {
    }

    // 析構函數
    ~MockRegistry()
    {
        // 基類析構函數會處理清理
    }

    // 實現抽象的Register方法
    RegistryResult<Key>* Register(const string name, const string description, Val value) override
    {
        m_registerCallCount++;

        // 模擬註冊失敗
        if(m_shouldFailRegister)
        {
            string message = (m_customFailMessage != "") ? m_customFailMessage : "模擬註冊失敗";
            Key defaultKey;
            return new RegistryResult<Key>(false, message, defaultKey, this.m_name);
        }

        // 檢查是否達到最大項目數量
        if(this.m_items.size() >= this.m_maxItems)
        {
            Key defaultKey;
            return new RegistryResult<Key>(false, "已達到最大項目數量", defaultKey, this.m_name);
        }

        // 創建鍵（這裡簡化處理，實際應該根據Key類型處理）
        Key key = (Key)name;

        // 檢查鍵是否已存在
        if(this.m_items.contains(key))
        {
            Key defaultKey;
            return new RegistryResult<Key>(false, "鍵已存在", defaultKey, this.m_name);
        }

        // 創建新項目
        string idStr = StringFormat("mock_%d", GetTickCount());
        RegistryItem<Val>* item = new RegistryItem<Val>(idStr, name, description, value, "Val");

        // 添加到哈希表
        this.m_items.set(key, item);

        // 保存最後註冊的鍵
        this.m_lastRegisteredKey = key;

        return new RegistryResult<Key>(true, "註冊成功", key, this.m_name);
    }

    // 重寫Unregister方法以支援Mock功能
    bool Unregister(const Key key) override
    {
        m_unregisterCallCount++;

        if(m_shouldFailUnregister)
        {
            return false;
        }

        return Registry<Key, Val>::Unregister(key);
    }

    // 重寫Clear方法以支援Mock功能
    void Clear() override
    {
        m_clearCallCount++;
        Registry<Key, Val>::Clear();
    }

    // === Mock特有的方法 ===

    // 設置註冊是否應該失敗
    void SetShouldFailRegister(bool shouldFail) { m_shouldFailRegister = shouldFail; }

    // 設置取消註冊是否應該失敗
    void SetShouldFailUnregister(bool shouldFail) { m_shouldFailUnregister = shouldFail; }

    // 設置自定義失敗消息
    void SetCustomFailMessage(string message) { m_customFailMessage = message; }

    // 獲取Register方法調用次數
    int GetRegisterCallCount() const { return m_registerCallCount; }

    // 獲取Unregister方法調用次數
    int GetUnregisterCallCount() const { return m_unregisterCallCount; }

    // 獲取Clear方法調用次數
    int GetClearCallCount() const { return m_clearCallCount; }

    // 重置調用計數
    void ResetCallCounts()
    {
        m_registerCallCount = 0;
        m_unregisterCallCount = 0;
        m_clearCallCount = 0;
    }

    // 手動添加項目（用於測試特殊情況）
    void ManuallyAddItem(Key key, Val value, string name = "Manual", string description = "Manually added")
    {
        string idStr = StringFormat("manual_%d", GetTickCount());
        RegistryItem<Val>* item = new RegistryItem<Val>(idStr, name, description, value, "Val");
        this.m_items.set(key, item);
    }

    // 獲取內部HashMap的大小（用於測試）
    int GetInternalSize() const
    {
        return this.m_items.size();
    }
};

//+------------------------------------------------------------------+
//| Mock註冊器工廠 - 創建各種類型的Mock註冊器                         |
//+------------------------------------------------------------------+
class MockRegistryFactory
{
public:
    // 創建字符串鍵的Mock註冊器
    template<typename Val>
    static MockRegistry<string, Val>* CreateStringKeyRegistry(string name = "StringMockRegistry", int maxItems = 10)
    {
        return new MockRegistry<string, Val>(name, "StringMock", maxItems);
    }

    // 創建整數鍵的Mock註冊器
    template<typename Val>
    static MockRegistry<int, Val>* CreateIntKeyRegistry(string name = "IntMockRegistry", int maxItems = 10)
    {
        return new MockRegistry<int, Val>(name, "IntMock", maxItems);
    }

    // 創建會失敗的Mock註冊器
    template<typename Key, typename Val>
    static MockRegistry<Key, Val>* CreateFailingRegistry(string name = "FailingMockRegistry")
    {
        MockRegistry<Key, Val>* registry = new MockRegistry<Key, Val>(name, "FailingMock", 10);
        registry.SetShouldFailRegister(true);
        registry.SetCustomFailMessage("測試失敗場景");
        return registry;
    }

    // 創建容量限制的Mock註冊器
    template<typename Key, typename Val>
    static MockRegistry<Key, Val>* CreateLimitedRegistry(string name = "LimitedMockRegistry", int maxItems = 2)
    {
        return new MockRegistry<Key, Val>(name, "LimitedMock", maxItems);
    }

    // 創建預填充的Mock註冊器
    static MockRegistry<string, int>* CreatePrefilledStringIntRegistry(string name = "PrefilledMockRegistry")
    {
        MockRegistry<string, int>* registry = new MockRegistry<string, int>(name, "PrefilledMock", 10);

        // 預填充一些數據
        registry.Register("item1", "第一個項目", 100);
        registry.Register("item2", "第二個項目", 200);
        registry.Register("item3", "第三個項目", 300);

        // 重置調用計數，因為預填充不應該計入測試
        registry.ResetCallCounts();

        return registry;
    }

    // 清理Mock註冊器
    template<typename Key, typename Val>
    static void CleanupRegistry(MockRegistry<Key, Val>* &registry)
    {
        if(registry != NULL)
        {
            delete registry;
            registry = NULL;
        }
    }
};
