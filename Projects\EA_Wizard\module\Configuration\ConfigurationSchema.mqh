//+------------------------------------------------------------------+
//| Module: Configuration/ConfigurationSchema.mqh                    |
//| This file is part of the EA_Wizard project.                      |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

// 防止重複包含
#ifndef _CONFIGURATION_SCHEMA_MQH_
#define _CONFIGURATION_SCHEMA_MQH_

#include "ConfigurationTypes.mqh"
#include "../mql4-lib-master/Format/Json.mqh"

//+------------------------------------------------------------------+
//| 配置架構類                                                        |
//+------------------------------------------------------------------+
class ConfigurationSchema
{
private:
    JsonObject* m_schema;  // 架構對象
    
public:
    /**
     * 構造函數
     */
    ConfigurationSchema()
    {
        m_schema = new JsonObject();
    }
    
    /**
     * 析構函數
     */
    ~ConfigurationSchema()
    {
        if(m_schema != NULL)
        {
            delete m_schema;
            m_schema = NULL;
        }
    }
    
    /**
     * 添加字符串屬性
     * @param key 屬性鍵
     * @param required 是否必填
     * @param defaultValue 默認值
     */
    void addString(string key, bool required, string defaultValue="")
    {
        JsonObject* property = new JsonObject();
        property.setString("type", "string");
        property.setBoolean("required", required);
        property.setString("default", defaultValue);
        
        m_schema.setObject(key, property);
    }
    
    /**
     * 添加數值屬性
     * @param key 屬性鍵
     * @param required 是否必填
     * @param defaultValue 默認值
     */
    void addNumber(string key, bool required, double defaultValue=0.0)
    {
        JsonObject* property = new JsonObject();
        property.setString("type", "number");
        property.setBoolean("required", required);
        property.setNumber("default", defaultValue);
        
        m_schema.setObject(key, property);
    }
    
    /**
     * 添加布爾屬性
     * @param key 屬性鍵
     * @param required 是否必填
     * @param defaultValue 默認值
     */
    void addBoolean(string key, bool required, bool defaultValue=false)
    {
        JsonObject* property = new JsonObject();
        property.setString("type", "boolean");
        property.setBoolean("required", required);
        property.setBoolean("default", defaultValue);
        
        m_schema.setObject(key, property);
    }
    
    /**
     * 添加對象屬性
     * @param key 屬性鍵
     * @param required 是否必填
     * @param properties 子屬性架構
     */
    void addObject(string key, bool required, JsonObject* properties)
    {
        JsonObject* property = new JsonObject();
        property.setString("type", "object");
        property.setBoolean("required", required);
        
        if(properties != NULL)
        {
            property.setObject("properties", properties);
        }
        
        m_schema.setObject(key, property);
    }
    
    /**
     * 添加數組屬性
     * @param key 屬性鍵
     * @param required 是否必填
     * @param itemType 數組項類型
     * @param itemProperties 數組項屬性（如果是對象類型）
     */
    void addArray(string key, bool required, string itemType, JsonObject* itemProperties=NULL)
    {
        JsonObject* property = new JsonObject();
        property.setString("type", "array");
        property.setBoolean("required", required);
        
        JsonObject* items = new JsonObject();
        items.setString("type", itemType);
        
        if(itemType == "object" && itemProperties != NULL)
        {
            items.setObject("properties", itemProperties);
        }
        
        property.setObject("items", items);
        
        m_schema.setObject(key, property);
    }
    
    /**
     * 獲取架構對象
     * @return 架構對象
     */
    JsonObject* getSchema()
    {
        return m_schema;
    }
    
    /**
     * 創建默認EA配置架構
     * @return 配置架構對象
     */
    static ConfigurationSchema* createDefaultEASchema()
    {
        ConfigurationSchema* schema = new ConfigurationSchema();
        
        // 基本屬性
        schema.addString("name", true, "My EA");
        schema.addNumber("version", true, 1.0);
        schema.addBoolean("enabled", true, true);
        schema.addString("symbol", true, Symbol());
        schema.addNumber("timeframe", true, Period());
        schema.addNumber("takeProfit", true, 50.0);
        schema.addNumber("stopLoss", true, 30.0);
        
        // 風險管理屬性
        JsonObject* riskProperties = new JsonObject();
        ConfigurationSchema riskSchema;
        riskSchema.addBoolean("enabled", true, true);
        riskSchema.addNumber("maxRiskPercent", true, 2.0);
        riskSchema.addBoolean("useATR", false, false);
        riskSchema.addNumber("atrPeriod", false, 14);
        riskSchema.addNumber("atrMultiplier", false, 2.0);
        
        schema.addObject("riskManagement", false, riskSchema.getSchema());
        
        // 指標屬性
        JsonObject* maProperties = new JsonObject();
        ConfigurationSchema maSchema;
        maSchema.addString("name", true, "Moving Average");
        maSchema.addBoolean("enabled", true, true);
        maSchema.addNumber("period", true, 20);
        maSchema.addString("method", true, "SMA");
        maSchema.addString("appliedPrice", true, "PRICE_CLOSE");
        
        JsonObject* rsiProperties = new JsonObject();
        ConfigurationSchema rsiSchema;
        rsiSchema.addString("name", true, "RSI");
        rsiSchema.addBoolean("enabled", true, true);
        rsiSchema.addNumber("period", true, 14);
        rsiSchema.addNumber("overbought", true, 70);
        rsiSchema.addNumber("oversold", true, 30);
        
        // 創建默認指標數組
        JsonArray* defaultIndicators = new JsonArray();
        ArrayResize(defaultIndicators.value, 2);
        defaultIndicators.value[0] = maSchema.getSchema();
        defaultIndicators.value[1] = rsiSchema.getSchema();
        
        // 添加指標數組屬性
        schema.addArray("indicators", false, "object", maSchema.getSchema());
        
        return schema;
    }
};

#endif // _CONFIGURATION_SCHEMA_MQH_
