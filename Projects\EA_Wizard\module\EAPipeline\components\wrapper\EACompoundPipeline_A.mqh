#property strict


#include "../EACompoundPipeline.mqh"
#include "../../interface/IEAPipelineState.mqh"

//+------------------------------------------------------------------+
//| EACompoundPipeline_A 類 - 包裝複合流水線，提供流水線狀態的管理功能 |
//+------------------------------------------------------------------+
class EACompoundPipeline_A : public EACompoundPipeline
{
private:
    IEAPipelineState<EACompoundPipeline_A*>* m_state;

public:
    // 構造函數
    EACompoundPipeline_A(PipelineComposite* composite, string type = "EACompoundPipeline_A")
        : EACompoundPipeline(composite, type),
          m_state(NULL)
    {
        if(!this.IsExecuted())
            m_state = EACompoundPipelineStateFactory::GetState(EA_PIPELINE_STATE_PENDING);
        else if(this.GetResult().IsSuccess())
            m_state = EACompoundPipelineStateFactory::GetState(EA_PIPELINE_STATE_COMPLETED);
        else
            m_state = EACompoundPipelineStateFactory::GetState(EA_PIPELINE_STATE_FAILED);
    }
    EACompoundPipeline_A(EACompoundPipeline* composite, string type = "EACompoundPipeline_A")
        : EACompoundPipeline(composite, type),
          m_state(NULL)
    {
        EACompoundPipeline::SetComposite(composite.GetComposite());
        
        if(!this.IsExecuted())
            m_state = EACompoundPipelineStateFactory::GetState(EA_PIPELINE_STATE_PENDING);
        else if(this.GetResult().IsSuccess())
            m_state = EACompoundPipelineStateFactory::GetState(EA_PIPELINE_STATE_COMPLETED);
        else
            m_state = EACompoundPipelineStateFactory::GetState(EA_PIPELINE_STATE_FAILED);
    }
    EACompoundPipeline_A(string name, string type = "EACompoundPipeline_A", int maxItems = 100)
        : EACompoundPipeline(name, type, maxItems),
          m_state(EACompoundPipelineStateFactory::GetState(EA_PIPELINE_STATE_PENDING))
    {
    }

    // 析構函數
    virtual ~EACompoundPipeline_A()
    {
    }

    // 執行流水線
    virtual void Execute() override
    {   
        if(m_state != NULL)
        {
            m_state.Execute(GetPointer(this));
        }
    }

    // 重置流水線狀態
    virtual void Restore() override
    {
        if(m_state != NULL)
        {
            m_state.Restore(GetPointer(this));
        }
    }

    // 獲取流水線狀態
    ENUM_EA_PIPELINE_STATE GetState()
    {
        return m_state != NULL ? m_state.GetState() : EA_PIPELINE_STATE_PENDING;
    }

    // 獲取流水線狀態描述
    string GetStateDescription()
    {
        return m_state != NULL ? m_state.GetStateDescription() : "待執行";
    }

    // 設置流水線狀態
    virtual void SetState(IEAPipelineState<EACompoundPipeline_A*>* state)
    {
        m_state = state;
    }

};

#include "../state/EACompoundPipeline_A/package.mqh"
#include "../factory/EACompoundPipelineStateFactory.mqh"