#property strict

#include "../../PipelineAdvance/interface/IPipeline.mqh"
#include "../../PipelineAdvance/PipelineComposite.mqh"

//+------------------------------------------------------------------+
//| EACompoundPipeline 類 - 裝飾者模式實現                           |
//+------------------------------------------------------------------+
class EACompoundPipeline : public IPipeline
{
private:
    PipelineComposite* m_wrappee;  // 被裝飾的流水線
    string m_name;                   // 流水線名稱
    string m_type;                   // 流水線類型
    int m_max_children;             // 最大子流水線數量

public:
    // 構造函數
    EACompoundPipeline(PipelineComposite* composite, string type = "EACompoundPipeline")
        : m_wrappee(composite), 
          m_name(composite.GetName()), 
          m_type(type),
          m_max_children(composite.GetMaxItems())
    {
    }
    EACompoundPipeline(EACompoundPipeline* composite, string type = "EACompoundPipeline")
        : m_wrappee(NULL), 
          m_name(composite.GetName()), 
          m_type(type),
          m_max_children(NULL)
    {
        m_wrappee = composite.GetComposite();
        m_max_children = composite.GetMaxItems();
    }
    EACompoundPipeline(string name, string type = "EACompoundPipeline", int maxItems = 100)
        : m_wrappee(new PipelineComposite(name, type, maxItems)), 
          m_name(name), 
          m_type(type),
          m_max_children(maxItems)
    {
    }

    // 析構函數
    virtual ~EACompoundPipeline()
    {
        // 注意：不刪除被裝飾的流水線，因為它可能被其他對象引用
    }

    // 執行流水線
    virtual void Execute() override
    {
        // 檢查是否已執行
        if(IsExecuted())
        {
            // 如果已執行，直接返回，不重複執行
            return;
        }

        // 檢查被裝飾的流水線是否為空（理論上不應該為空，因為構造函數已經確保）
        if(m_wrappee == NULL)
        {
            // 如果為空，創建一個新的複合流水線
            m_wrappee = new PipelineComposite(m_name, m_type, m_max_children);
            return;
        }

        // 執行被裝飾的流水線
        m_wrappee.Execute();
    }

    // 獲取流水線名稱
    virtual string GetName() override
    {
        return m_name;
    }

    // 獲取流水線類型
    virtual string GetType() override
    {
        return m_type;
    }

    // 獲取流水線執行結果
    virtual PipelineResult* GetResult() override
    {
        // 直接返回被裝飾的流水線的結果
        return m_wrappee != NULL ? m_wrappee.GetResult() : NULL;
    }

    // 重置流水線狀態
    virtual void Restore() override
    {
        // 重置被裝飾的流水線
        if(m_wrappee != NULL)
        {
            m_wrappee.Restore();
        }
    }

    // 檢查流水線是否已執行
    virtual bool IsExecuted() override
    {
        // 直接返回被裝飾的流水線的執行狀態
        return m_wrappee != NULL ? m_wrappee.IsExecuted() : false;
    }

    // 獲取被裝飾的流水線
    virtual PipelineComposite* GetComposite()
    {
        return m_wrappee;
    }

    //--- PipelineComposite 特有方法 ---//

    // 添加子流水線
    virtual PipelineResult* Add(IPipeline* child)
    {
        if(m_wrappee == NULL)
        {
            return new PipelineResult(false, "被裝飾的流水線為空", m_name);
        }
        return m_wrappee.Add(child);
    }

    // 移除子流水線
    virtual PipelineResult* Remove(IPipeline* child)
    {
        if(m_wrappee == NULL)
        {
            return new PipelineResult(false, "被裝飾的流水線為空", m_name);
        }
        return m_wrappee.Remove(child);
    }

    // 獲取最大子流水線數量
    virtual int GetMaxItems()
    {
        if(m_wrappee == NULL)
        {
            return 0;
        }
        return m_max_children;
    }

    // 獲取子流水線數量
    virtual int GetCount()
    {
        if(m_wrappee == NULL)
        {
            return 0;
        }
        return m_wrappee.GetCount();
    }

    // 獲取所有子流水線
    virtual int GetAllItems(IPipeline* &items[])
    {
        if(m_wrappee == NULL)
        {
            return 0;
        }
        return m_wrappee.GetAllItems(items);
    }

protected:
    // 設置被裝飾的流水線
    virtual void SetComposite(PipelineComposite* composite)
    {
        m_wrappee = composite;
        m_max_children = composite.GetMaxItems();
    }
};
