//+------------------------------------------------------------------+
//|                              TradingPipelineExplorerExample.mqh |
//|                                            EAPipelineAdvance_v1     |
//|                                                                      |
//+------------------------------------------------------------------+
#property strict

#include "../TradingPipelineExplorer.mqh"
#include "../TradingPipelineContainer.mqh"
#include "../TradingPipelineContainerManager.mqh"

//+------------------------------------------------------------------+
//| TradingPipelineExplorer 使用示例類                               |
//+------------------------------------------------------------------+
class TradingPipelineExplorerExample
{
private:
    TradingPipelineContainerManager* m_manager;
    TradingPipelineRegistry* m_registry;
    TradingPipelineExplorer* m_explorer;

public:
    // 構造函數
    TradingPipelineExplorerExample()
        : m_manager(NULL),
          m_registry(NULL),
          m_explorer(NULL)
    {
    }

    // 析構函數
    ~TradingPipelineExplorerExample()
    {
        Cleanup();
    }

    //+------------------------------------------------------------------+
    //| 運行示例                                                         |
    //+------------------------------------------------------------------+
    void RunExample()
    {
        Print("=== TradingPipelineExplorer 使用示例 ===");

        // 1. 設置測試環境
        SetupTestEnvironment();

        // 2. 基本查詢示例
        BasicQueryExample();

        // 3. 階段映射示例
        StageMappingExample();

        // 4. 統計信息示例
        StatisticsExample();

        // 5. 報告生成示例
        ReportGenerationExample();

        // 6. 批量查詢示例
        BatchQueryExample();

        Print("=== 示例執行完成 ===");
    }

private:
    //+------------------------------------------------------------------+
    //| 設置測試環境                                                     |
    //+------------------------------------------------------------------+
    void SetupTestEnvironment()
    {
        Print("--- 設置測試環境 ---");

        // 創建管理器
        m_manager = new TradingPipelineContainerManager("ExampleManager", "示例管理器");

        // 創建不同階段的容器（使用新的 StagePipeline 類）
        StagePipeline* initContainer = new StagePipeline(
            "InitializationContainer",
            INIT_START,
            "初始化流水線容器",
            "StagePipeline"
        );

        StagePipeline* tickContainer = new StagePipeline(
            "TickProcessingContainer",
            TICK_DATA_FEED,
            "Tick處理流水線容器",
            "StagePipeline"
        );

        StagePipeline* deinitContainer = new StagePipeline(
            "CleanupContainer",
            DEINIT_CLEANUP,
            "清理流水線容器",
            "StagePipeline"
        );

        // 創建註冊器
        m_registry = new TradingPipelineRegistry(m_manager, "ExampleRegistry");

        // 註冊容器到註冊器
        m_registry.Register(TRADING_INIT, initContainer);
        m_registry.Register(TRADING_TICK, tickContainer);
        m_registry.Register(TRADING_DEINIT, deinitContainer);

        // 創建探索器
        m_explorer = new TradingPipelineExplorer(
            m_registry,
            "MainExplorer",
            "TradingPipelineExplorer",
            "主要交易流水線探索器"
        );

        Print("✓ 測試環境設置完成");
        Print("  - 管理器容器數量: ", m_manager.GetContainerCount());
        Print("  - 探索器狀態: ", m_explorer.IsValid() ? "有效" : "無效");
    }

    //+------------------------------------------------------------------+
    //| 基本查詢示例                                                     |
    //+------------------------------------------------------------------+
    void BasicQueryExample()
    {
        Print("--- 基本查詢示例 ---");

        // 根據事件查詢流水線
        ITradingPipeline* initPipeline = m_explorer.GetPipeline(TRADING_INIT);
        ITradingPipeline* tickPipeline = m_explorer.GetPipeline(TRADING_TICK);
        ITradingPipeline* deinitPipeline = m_explorer.GetPipeline(TRADING_DEINIT);

        Print("根據事件查詢結果:");
        Print("  - TRADING_INIT: ", (initPipeline != NULL) ? initPipeline.GetName() : "未找到");
        Print("  - TRADING_TICK: ", (tickPipeline != NULL) ? tickPipeline.GetName() : "未找到");
        Print("  - TRADING_DEINIT: ", (deinitPipeline != NULL) ? deinitPipeline.GetName() : "未找到");

        // 根據階段查詢流水線
        ITradingPipeline* initStagePipeline = m_explorer.GetPipeline(INIT_START);
        ITradingPipeline* tickStagePipeline = m_explorer.GetPipeline(TICK_DATA_FEED);
        ITradingPipeline* deinitStagePipeline = m_explorer.GetPipeline(DEINIT_CLEANUP);

        Print("根據階段查詢結果:");
        Print("  - INIT_START: ", (initStagePipeline != NULL) ? initStagePipeline.GetName() : "未找到");
        Print("  - TICK_DATA_FEED: ", (tickStagePipeline != NULL) ? tickStagePipeline.GetName() : "未找到");
        Print("  - DEINIT_CLEANUP: ", (deinitStagePipeline != NULL) ? deinitStagePipeline.GetName() : "未找到");
    }

    //+------------------------------------------------------------------+
    //| 階段映射示例                                                     |
    //+------------------------------------------------------------------+
    void StageMappingExample()
    {
        Print("--- 階段映射示例 ---");

        // 測試不同階段的事件映射
        ENUM_TRADING_STAGE testStages[] = {
            INIT_START, INIT_PARAMETERS, INIT_COMPLETE,
            TICK_DATA_FEED, TICK_SIGNAL_ANALYSIS, TICK_LOGGING,
            DEINIT_CLEANUP, DEINIT_SAVE_STATE, DEINIT_COMPLETE
        };

        Print("階段到事件的映射關係:");
        for(int i = 0; i < ArraySize(testStages); i++)
        {
            ENUM_TRADING_STAGE stage = testStages[i];
            ENUM_TRADING_EVENT event = m_explorer.GetEventFromStage(stage);
            string stageName = TradingEventUtils::StageToString(stage);
            string eventName = (event != -1) ? TradingEventUtils::EventToString(event) : "UNKNOWN";

            Print(StringFormat("  %s -> %s", stageName, eventName));
        }
    }

    //+------------------------------------------------------------------+
    //| 統計信息示例                                                     |
    //+------------------------------------------------------------------+
    void StatisticsExample()
    {
        Print("--- 統計信息示例 ---");

        // 獲取各種統計信息
        int totalCount = m_explorer.GetTotalPipelineCount();
        int initCount = m_explorer.GetPipelineCountByEvent(TRADING_INIT);
        int tickCount = m_explorer.GetPipelineCountByEvent(TRADING_TICK);
        int deinitCount = m_explorer.GetPipelineCountByEvent(TRADING_DEINIT);

        Print("流水線統計信息:");
        Print("  - 總流水線數量: ", totalCount);
        Print("  - 初始化事件流水線: ", initCount);
        Print("  - Tick事件流水線: ", tickCount);
        Print("  - 清理事件流水線: ", deinitCount);

        // 檢查特定階段的存在性
        bool hasInitStart = m_explorer.HasPipelineForStage(INIT_START);
        bool hasTickData = m_explorer.HasPipelineForStage(TICK_DATA_FEED);
        bool hasOrderMgmt = m_explorer.HasPipelineForStage(TICK_ORDER_MANAGEMENT);

        Print("階段存在性檢查:");
        Print("  - INIT_START: ", hasInitStart ? "存在" : "不存在");
        Print("  - TICK_DATA_FEED: ", hasTickData ? "存在" : "不存在");
        Print("  - TICK_ORDER_MANAGEMENT: ", hasOrderMgmt ? "存在" : "不存在");
    }

    //+------------------------------------------------------------------+
    //| 報告生成示例                                                     |
    //+------------------------------------------------------------------+
    void ReportGenerationExample()
    {
        Print("--- 報告生成示例 ---");

        // 生成探索報告
        string explorationReport = m_explorer.GenerateExplorationReport();
        Print("探索報告:");
        Print(explorationReport);

        Print(""); // 空行分隔

        // 生成階段報告
        string stageReport = m_explorer.GenerateStageReport();
        Print("階段映射報告:");
        Print(stageReport);
    }

    //+------------------------------------------------------------------+
    //| 批量查詢示例                                                     |
    //+------------------------------------------------------------------+
    void BatchQueryExample()
    {
        Print("--- 批量查詢示例 ---");

        // 獲取所有初始化事件的流水線
        ITradingPipeline* initPipelines[];
        int initCount = m_explorer.GetAllPipelinesByEvent(TRADING_INIT, initPipelines);

        Print("初始化事件的所有流水線 (", initCount, " 個):");
        for(int i = 0; i < initCount; i++)
        {
            if(initPipelines[i] != NULL)
            {
                Print(StringFormat("  [%d] %s (%s)",
                                 i,
                                 initPipelines[i].GetName(),
                                 initPipelines[i].GetType()));
            }
        }

        // 獲取所有 TICK_DATA_FEED 階段的流水線
        ITradingPipeline* dataFeedPipelines[];
        int dataFeedCount = m_explorer.GetAllPipelinesByStage(TICK_DATA_FEED, dataFeedPipelines);

        Print("TICK_DATA_FEED 階段的所有流水線 (", dataFeedCount, " 個):");
        for(int i = 0; i < dataFeedCount; i++)
        {
            if(dataFeedPipelines[i] != NULL)
            {
                Print(StringFormat("  [%d] %s (%s)",
                                 i,
                                 dataFeedPipelines[i].GetName(),
                                 dataFeedPipelines[i].GetType()));
            }
        }
    }

    //+------------------------------------------------------------------+
    //| 清理資源                                                         |
    //+------------------------------------------------------------------+
    void Cleanup()
    {
        if(m_explorer != NULL)
        {
            delete m_explorer;
            m_explorer = NULL;
        }

        if(m_registry != NULL)
        {
            delete m_registry;
            m_registry = NULL;
        }

        if(m_manager != NULL)
        {
            delete m_manager;
            m_manager = NULL;
        }
    }
};

//+------------------------------------------------------------------+
//| 全局函數：運行示例                                               |
//+------------------------------------------------------------------+
void RunTradingPipelineExplorerExample()
{
    TradingPipelineExplorerExample* example = new TradingPipelineExplorerExample();
    example.RunExample();
    delete example;
}
