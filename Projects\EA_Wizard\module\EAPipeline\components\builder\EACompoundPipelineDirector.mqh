#property strict

#include "../../enum/EAPipelineEnum.mqh"
#include "../../model/EABuiltPipelineItem.mqh"
#include "EACompoundPipelineBuilder.mqh"
#include "../../../Validation/Validators/ListValidator.mqh"
#include "../../../mql4-lib-master/Collection/HashMap.mqh"
#include "../../../RegistryAdvance/ItemRegistry.mqh"

class EACompoundPipelineTemplateDirector
{
    protected:
        Vector<string> m_valid_decorators;
        ItemRegistry<EACompoundPipelineDecoratorFactory*> m_decorator_factory_registry;

        string m_field;

    public:
        EACompoundPipelineTemplateDirector()
        : m_valid_decorators(),
          m_decorator_factory_registry(),
          m_field("CompoundPipelineDecorators")
        {
            m_valid_decorators.add("EALogCompoundPipeline");
            m_valid_decorators.add("EAErrorHandlingCompoundPipeline");
            m_valid_decorators.add("EACompoundPipeline_A");
            m_decorator_factory_registry.Register("EALogCompoundPipeline", "Decorator for logging", new EALogCompoundPipelineFactory());
            m_decorator_factory_registry.Register("EAErrorHandlingCompoundPipeline", "Decorator for error handling", new EAErrorHandlingCompoundPipelineFactory());
            m_decorator_factory_registry.Register("EACompoundPipeline_A", "Decorator for EACompoundPipeline_A", new EACompoundPipeline_AFactory());
        }
};

class EACompoundPipelineDirector : public EACompoundPipelineTemplateDirector
{
    private:
        static EACompoundPipelineDirector* s_instance;

        EACompoundPipelineBuilder m_builder;
        int m_maxItems;

        // 私有建構函數
        EACompoundPipelineDirector()
        : m_builder(),
          m_maxItems(100)
        {
        }

        // 根據字串建立流水線
        EABuiltPipelineItem* CreateCompoundPipelineByString(string name, string decorator)
        {
            string message = "";
            bool is_built = true;
            bool is_completed_built = true;
            // 檢查裝飾者是否有效
            CListValidator<string>* m_list_validator = new CListValidator<string>(decorator, GetPointer(m_valid_decorators), m_field);
            CValidationResult* result = m_list_validator.Validate();
            if(!result.IsValid()){
                message += result.GetMessage() + "\r\n";
                is_completed_built = false;
            }

            // 建立流水線
            m_builder.Reset();
            m_builder.SetName(name);
            m_builder.SetType("EACompoundPipeline");
            m_builder.SetMaxItems(m_maxItems);

            // 取得裝飾者工廠
            EACompoundPipelineDecoratorFactory* factory = m_decorator_factory_registry.GetItem(decorator).GetValue();
            if(factory != NULL){
                m_builder.AddDecorator(factory);
            }
            else{
                is_completed_built = false;
            }
            EACompoundPipeline* pipeline = m_builder.Build();
            if(pipeline == NULL){
                is_built = false;
                is_completed_built = false;
                return new EABuiltPipelineItem("NULL", "NULL", "建立失敗，流水線為空", NULL, is_built, is_completed_built);
            }

            return new EABuiltPipelineItem(pipeline.GetName(), pipeline.GetType(), message + "建立成功", pipeline, is_built, is_completed_built);
        }
        
        // 根據字串建立流水線
        EABuiltPipelineItem* CreateCompoundPipelineByString(string name, string &decorators[])
        {
            string message [];
            bool is_built = true;
            bool is_completed_built = true;
            // 檢查裝飾者是否有效
            for(int i = 0; i < ArraySize(decorators); i++)
            {
                CListValidator<string>* m_list_validator = new CListValidator<string>(decorators[i], GetPointer(m_valid_decorators), m_field);
                CValidationResult* result = m_list_validator.Validate();
                if(!result.IsValid()){
                    ArrayResize(message, ArraySize(message) + 1);
                    message[ArraySize(message) - 1] = result.GetMessage();
                    is_completed_built = false;
                }
            }

            // 建立流水線
            m_builder.Reset();
            m_builder.SetName(name);
            m_builder.SetType("EACompoundPipeline");
            m_builder.SetMaxItems(m_maxItems);
            
            // 取得裝飾者工廠
            for(int i = 0; i < ArraySize(decorators); i++) {
                EACompoundPipelineDecoratorFactory* factory = m_decorator_factory_registry.GetItem(decorators[i]).GetValue();
                if(factory != NULL){
                    m_builder.AddDecorator(factory);
                }
                else{
                    is_completed_built = false;
                }
            }

            EACompoundPipeline* pipeline = m_builder.Build();
            if(pipeline == NULL){
                ArrayResize(message, ArraySize(message) + 1);
                message[ArraySize(message) - 1] = "建立失敗，流水線為空";
                is_built = false;
                is_completed_built = false;
                return new EABuiltPipelineItem("NULL", "NULL", message, NULL, is_built, is_completed_built);
            }
            else{
                ArrayResize(message, ArraySize(message) + 1); 
                message[ArraySize(message) - 1] = pipeline.GetName() + "建立成功";;
            }

            return new EABuiltPipelineItem(pipeline.GetName(), pipeline.GetType(), message, pipeline, is_built, is_completed_built);
        }

    public:
        // 單例模式
        static EACompoundPipelineDirector* GetInstance()
        {
            if(s_instance == NULL)
            {
                s_instance = new EACompoundPipelineDirector();
                s_instance.m_builder.SetMaxItems(s_instance.m_maxItems);
            }
            return s_instance;
        }

        // 設置最大子流水線數量
        void SetMaxItems(int maxItems)
        {
            m_maxItems = maxItems;
        }
        
        // 根據字串建立流水線
        EABuiltPipelineItem* CreateCompoundPipeline(ENUM_EA_EVENT event, string decorator)
        {
            return CreateCompoundPipelineByString(EnumToString(event), decorator);
        }

        // 根據字串建立流水線
        EABuiltPipelineItem* CreateCompoundPipeline(ENUM_EA_SUB_EVENT event, string decorator)
        {
            return CreateCompoundPipelineByString(EnumToString(event), decorator);
        }

        // 根據枚舉建立流水線
        EABuiltPipelineItem* CreateCompoundPipeline(ENUM_EA_EVENT event, string &decorators[])
        {
            return CreateCompoundPipelineByString(EnumToString(event), decorators);
        }

        // 根據子枚舉建立流水線
        EABuiltPipelineItem* CreateCompoundPipeline(ENUM_EA_SUB_EVENT event, string &decorators[])
        {
            return CreateCompoundPipelineByString(EnumToString(event), decorators);
        }

};

EACompoundPipelineDirector* EACompoundPipelineDirector::s_instance = NULL;

class EACompoundPipelineAssistingDirector : public EACompoundPipelineTemplateDirector
{
    private:
        static EACompoundPipelineAssistingDirector* s_instance;

        EACompoundPipelineAssistingBuilder m_builder;

        // 私有建構函數
        EACompoundPipelineAssistingDirector()
        : m_builder()
        {
        }

    public:
        // 單例模式
        static EACompoundPipelineAssistingDirector* GetInstance()
        {
            if(s_instance == NULL)
            {
                s_instance = new EACompoundPipelineAssistingDirector();
            }
            return s_instance;
        }

        // 根據字串建立流水線
        EABuiltPipelineItem* ModifyCompoundPipeline(EACompoundPipeline* pipeline, string decorator)
        {
            string message = "";
            bool is_built = true;
            bool is_completed_built = true;
            // 檢查裝飾者是否有效
            CListValidator<string>* m_list_validator = new CListValidator<string>(decorator, GetPointer(m_valid_decorators), m_field);
            CValidationResult* result = m_list_validator.Validate();
            if(!result.IsValid()){
                message += result.GetMessage() + "\r\n";
                is_completed_built = false;
            }

            // 建立流水線
            m_builder.Reset();
            m_builder.SetOriginalPipeline(pipeline);
            EACompoundPipelineDecoratorFactory* factory = m_decorator_factory_registry.GetItem(decorator).GetValue();
            if(factory != NULL){
                m_builder.AddDecorator(factory);
            }
            else{
                is_completed_built = false;
            }
            EACompoundPipeline* new_pipeline = m_builder.Build();
            if(new_pipeline == NULL){
                message += "建立失敗，流水線為空";
                is_built = false;
                is_completed_built = false;
                return new EABuiltPipelineItem("NULL", "NULL", message, NULL, is_built, is_completed_built);
            }

            return new EABuiltPipelineItem(new_pipeline.GetName(), new_pipeline.GetType(), message + "建立成功", pipeline, is_built, is_completed_built);
        }

        // 根據字串建立流水線
        EABuiltPipelineItem* ModifyCompoundPipeline(EACompoundPipeline* pipeline, string &decorators[])
        {
            string message[];
            bool is_built = true;
            bool is_completed_built = true;
            // 檢查裝飾者是否有效
            for(int i = 0; i < ArraySize(decorators); i++)
            {
                CListValidator<string>* m_list_validator = new CListValidator<string>(decorators[i], GetPointer(m_valid_decorators), m_field);
                CValidationResult* result = m_list_validator.Validate();
                if(!result.IsValid()){
                    ArrayResize(message, ArraySize(message) + 1);
                    message[ArraySize(message) - 1] = result.GetMessage();
                    is_completed_built = false;
                }
            }

            // 建立流水線
            m_builder.Reset();
            m_builder.SetOriginalPipeline(pipeline);
            for(int i = 0; i < ArraySize(decorators); i++) {
                EACompoundPipelineDecoratorFactory* factory = m_decorator_factory_registry.GetItem(decorators[i]).GetValue();
                if(factory != NULL){
                    m_builder.AddDecorator(factory);
                }
                else{
                    is_completed_built = false;
                }
            }
            EACompoundPipeline* new_pipeline = m_builder.Build();
            if(new_pipeline == NULL){
                is_built = false;
                is_completed_built = false;
                return new EABuiltPipelineItem("NULL", "NULL", "建立失敗，流水線為空", NULL, is_built, is_completed_built);
            }
            else{
                ArrayResize(message, ArraySize(message) + 1);
                message[ArraySize(message) - 1] = "建立成功";
            }

            return new EABuiltPipelineItem(new_pipeline.GetName(), new_pipeline.GetType(), message, new_pipeline, is_built, is_completed_built);
        }
};

EACompoundPipelineAssistingDirector* EACompoundPipelineAssistingDirector::s_instance = NULL;
