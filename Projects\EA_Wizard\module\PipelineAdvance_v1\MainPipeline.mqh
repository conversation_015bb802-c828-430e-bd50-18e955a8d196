#property strict

#include "TradingPipeline.mqh"
#include "TradingPipelineDriver.mqh"

class MainPipeline : public TradingPipeline
{
public:
    MainPipeline(string name = "",
                      string type = "MainPipeline",
                      ENUM_TRADING_STAGE stage = INIT_START,
                      ITradingPipelineDriver* driver = NULL)
        : TradingPipeline(name, type, stage, driver!=NULL?driver:TradingPipelineDriver::GetInstance())
    {
        
        if(m_driver != NULL)
        {
            TradingPipelineRegistry* registry = m_driver.GetRegistry();
            if(registry != NULL)
            {
                registry.Register(GetPointer(this));
            }
        }
        
    }

protected:
    // 主程序 - 子類必須實現
    virtual void Main() = 0;
};