//+------------------------------------------------------------------+
//|                                   TestEAPipelineIntegration.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../components/EAPipelineManager.mqh"
#include "../../components/EACompoundPipeline.mqh"
#include "../mock/MockEAPipeline.mqh"

//+------------------------------------------------------------------+
//| EA流水線整合測試類別                                             |
//+------------------------------------------------------------------+
class TestEAPipelineIntegration : public TestCase
{
private:
    TestRunner* m_runner;    // 測試運行器引用

public:
    // 構造函數
    TestEAPipelineIntegration(TestRunner* runner)
    : TestCase("TestEAPipelineIntegration"), m_runner(runner) {}

    // 運行所有測試
    void RunTests() override
    {
        TestSimpleWorkflow();
        TestErrorHandling();
        TestOnInitWorkflow();
    }

private:
    // 測試簡單工作流程
    void TestSimpleWorkflow()
    {
        SetUp();

        // 創建管理器和複合流水線
        EAPipelineManager* manager = EAPipelineManager::GetInstance();
        EACompoundPipeline* compound = new EACompoundPipeline("SimpleCompound");

        // 創建子流水線
        MockEAPipeline* child1 = MockEAPipelineFactory::CreateSuccessfulPipeline("Child1");
        MockEAPipeline* child2 = MockEAPipelineFactory::CreateSuccessfulPipeline("Child2");

        // 構建工作流程
        compound.Add(child1);
        compound.Add(child2);
        manager.AddPipeline(compound);

        // 執行工作流程
        PipelineResult* result = manager.ExecutePipeline("SimpleCompound");

        // 驗證結果
        m_runner.RecordResult(Assert::AssertNotNull("簡單工作流程_結果不為空", result));

        if(result != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("簡單工作流程_執行成功", result.IsSuccess()));
            delete result;
        }

        m_runner.RecordResult(Assert::AssertTrue("簡單工作流程_複合流水線已執行", compound.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("簡單工作流程_子流水線1已執行", child1.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("簡單工作流程_子流水線2已執行", child2.IsExecuted()));

        // 清理測試數據
        manager.RemovePipelineByName("SimpleCompound");
        TearDown();
    }



    // 測試錯誤處理
    void TestErrorHandling()
    {
        SetUp();

        EAPipelineManager* manager = EAPipelineManager::GetInstance();
        EACompoundPipeline* compound = new EACompoundPipeline("ErrorCompound");

        // 創建混合的成功和失敗流水線
        MockEAPipeline* success = MockEAPipelineFactory::CreateSuccessfulPipeline("Success");
        MockEAPipeline* failed = MockEAPipelineFactory::CreateFailedPipeline("Failed");

        compound.Add(success);
        compound.Add(failed);
        manager.AddPipeline(compound);

        // 執行包含錯誤的工作流程
        PipelineResult* result = manager.ExecutePipeline("ErrorCompound");

        // 驗證錯誤處理
        m_runner.RecordResult(Assert::AssertNotNull("錯誤處理_結果不為空", result));
        m_runner.RecordResult(Assert::AssertTrue("錯誤處理_成功流水線已執行", success.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("錯誤處理_失敗流水線已執行", failed.IsExecuted()));

        // 檢查失敗流水線的結果
        PipelineResult* failedResult = failed.GetResult();
        if(failedResult != NULL)
        {
            m_runner.RecordResult(Assert::AssertFalse("錯誤處理_失敗流水線結果為失敗", failedResult.IsSuccess()));
        }

        delete result;

        // 清理測試數據
        manager.RemovePipelineByName("ErrorCompound");
        TearDown();
    }



    // 測試OnInit工作流程
    void TestOnInitWorkflow()
    {
        SetUp();

        EACompoundPipeline* onInitPipeline = new EACompoundPipeline("OnInit");

        // 模擬OnInit階段的流水線
        MockEAPipeline* paramRead = MockEAPipelineFactory::CreateOnInitPipeline("ParameterRead");
        MockEAPipeline* varInit = MockEAPipelineFactory::CreateOnInitPipeline("VariableInit");
        MockEAPipeline* envCheck = MockEAPipelineFactory::CreateOnInitPipeline("EnvironmentCheck");

        onInitPipeline.Add(paramRead);
        onInitPipeline.Add(varInit);
        onInitPipeline.Add(envCheck);

        // 執行OnInit工作流程
        onInitPipeline.Execute();

        // 驗證OnInit流程
        m_runner.RecordResult(Assert::AssertTrue("OnInit工作流程_主流水線已執行", onInitPipeline.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("OnInit工作流程_參數讀取已執行", paramRead.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("OnInit工作流程_變數初始化已執行", varInit.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("OnInit工作流程_環境檢查已執行", envCheck.IsExecuted()));

        delete onInitPipeline;
        TearDown();
    }


};
