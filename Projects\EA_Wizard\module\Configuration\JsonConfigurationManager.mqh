//+------------------------------------------------------------------+
//| Module: Configuration/JsonConfigurationManager.mqh               |
//| This file is part of the EA_Wizard project.                      |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

// 防止重複包含
#ifndef _JSON_CONFIGURATION_MANAGER_MQH_
#define _JSON_CONFIGURATION_MANAGER_MQH_

#include "IConfigurationProvider.mqh"
#include "ConfigurationTypes.mqh"
#include "ConfigurationReader.mqh"
#include "ConfigurationValidator.mqh"
#include "ConfigurationSchema.mqh"
#include "../mql4-lib-master/Format/Json.mqh"
#include "JsonExtensions.mqh"


//+------------------------------------------------------------------+
//| JSON配置管理器類                                                  |
//+------------------------------------------------------------------+
class JsonConfigurationManager : public IConfigurationProvider
{
private:
    static JsonConfigurationManager* m_instance;  // 單例實例
    JsonObject* m_config;                         // 配置對象
    string m_filename;                            // 配置文件名
    bool m_isLoaded;                              // 是否已加載
    bool m_useCommon;                             // 是否使用公共目錄
    ConfigurationSchema* m_schema;                // 配置架構

    /**
     * 私有構造函數（單例模式）
     * @param filename 配置文件名
     * @param useCommon 是否使用公共目錄
     */
    JsonConfigurationManager(string filename="config.json", bool useCommon=false)
        : m_config(NULL),
          m_filename(filename),
          m_isLoaded(false),
          m_useCommon(useCommon),
          m_schema(NULL)
    {
        // 創建空配置對象
        m_config = new JsonObject();

        // 創建默認架構
        m_schema = ConfigurationSchema::createDefaultEASchema();
    }

    /**
     * 私有析構函數
     */
    ~JsonConfigurationManager()
    {
        // 清理資源
        if(m_config != NULL)
        {
            delete m_config;
            m_config = NULL;
        }

        if(m_schema != NULL)
        {
            delete m_schema;
            m_schema = NULL;
        }
    }

public:
    /**
     * 獲取單例實例
     * @param filename 配置文件名
     * @param useCommon 是否使用公共目錄
     * @return 配置管理器實例
     */
    static JsonConfigurationManager* GetInstance(string filename="config.json", bool useCommon=false)
    {
        if(m_instance == NULL)
        {
            m_instance = new JsonConfigurationManager(filename, useCommon);
        }
        else if(m_instance.m_filename != filename || m_instance.m_useCommon != useCommon)
        {
            // 如果文件名或目錄設置變更，則重新加載
            m_instance.m_filename = filename;
            m_instance.m_useCommon = useCommon;
            m_instance.m_isLoaded = false;
        }

        return m_instance;
    }

    /**
     * 加載配置
     * @return 是否成功
     */
    bool load()
    {
        // 獲取完整文件路徑
        string fullPath = ConfigurationReader::getFullPath(m_filename, m_useCommon);

        // 檢查文件是否存在
        if(!ConfigurationReader::fileExists(fullPath, m_useCommon))
        {
            // 文件不存在，創建默認配置
            Print("配置文件不存在，創建默認配置: ", fullPath);

            // 應用默認值
            if(m_schema != NULL)
            {
                ConfigurationValidator::applyDefaults(m_config, m_schema.getSchema());
            }

            // 保存默認配置
            if(!save())
            {
                Print("創建默認配置失敗: ", GetLastError());
                return false;
            }

            m_isLoaded = true;
            return true;
        }

        // 讀取文件內容
        string content = ConfigurationReader::readJsonFile(fullPath, m_useCommon);
        if(content == "")
        {
            Print("讀取配置文件失敗: ", GetLastError());
            return false;
        }

        // 解析JSON
        Json parser;
        JsonValue* parsedValue = parser.parse(content);

        if(parser.hasError() || parsedValue == NULL)
        {
            Print("解析配置文件失敗: ", parser.getError());
            ConfigSetLastError(CONFIG_ERROR_PARSE);
            return false;
        }

        // 檢查是否為對象
        JsonObject* parsedObject = dynamic_cast<JsonObject*>(parsedValue);
        if(parsedObject == NULL)
        {
            Print("配置文件不是有效的JSON對象");
            ConfigSetLastError(CONFIG_ERROR_INVALID_TYPE);
            delete parsedValue;
            return false;
        }

        // 清理舊配置
        if(m_config != NULL)
        {
            delete m_config;
        }

        // 設置新配置
        m_config = parsedObject;

        // 驗證配置
        if(m_schema != NULL)
        {
            if(!ConfigurationValidator::validate(m_config, m_schema.getSchema()))
            {
                Print("配置驗證失敗: ", ConfigurationValidator::getLastError());
                ConfigSetLastError(CONFIG_ERROR_VALIDATION);

                // 應用默認值
                ConfigurationValidator::applyDefaults(m_config, m_schema.getSchema());
            }
        }

        m_isLoaded = true;
        return true;
    }

    /**
     * 保存配置
     * @return 是否成功
     */
    bool save()
    {
        if(m_config == NULL)
        {
            Print("配置對象為空，無法保存");
            ConfigSetLastError(CONFIG_ERROR_INVALID_TYPE);
            return false;
        }

        // 獲取完整文件路徑
        string fullPath = ConfigurationReader::getFullPath(m_filename, m_useCommon);

        // 將配置轉換為JSON字符串
        string content = JsonExtensions::dumps(*m_config);

        // 寫入文件
        bool result = ConfigurationReader::writeJsonFile(fullPath, content, m_useCommon);
        if(!result)
        {
            Print("保存配置文件失敗: ", GetLastError());
            return false;
        }

        return true;
    }

    /**
     * 重置配置
     * @return 是否成功
     */
    bool reset()
    {
        // 清理舊配置
        if(m_config != NULL)
        {
            delete m_config;
        }

        // 創建新配置
        m_config = new JsonObject();

        // 應用默認值
        if(m_schema != NULL)
        {
            ConfigurationValidator::applyDefaults(m_config, m_schema.getSchema());
        }

        // 保存默認配置
        if(!save())
        {
            Print("保存默認配置失敗: ", GetLastError());
            return false;
        }

        m_isLoaded = true;
        return true;
    }

    /**
     * 設置配置架構
     * @param schema 配置架構
     */
    void setSchema(ConfigurationSchema* schema)
    {
        if(m_schema != NULL)
        {
            delete m_schema;
        }

        m_schema = schema;
    }

    // IConfigurationProvider 接口實現

    /**
     * 獲取字符串值
     * @param key 鍵
     * @param defaultValue 默認值
     * @return 字符串值
     */
    string getString(string key, string defaultValue="")
    {
        if(m_config == NULL || !m_config.contains(key))
        {
            return defaultValue;
        }

        JsonString* value = dynamic_cast<JsonString*>(m_config[key]);
        if(value == NULL)
        {
            return defaultValue;
        }

        return value.value;
    }

    /**
     * 獲取數值
     * @param key 鍵
     * @param defaultValue 默認值
     * @return 數值
     */
    double getNumber(string key, double defaultValue=0.0)
    {
        if(m_config == NULL || !m_config.contains(key))
        {
            return defaultValue;
        }

        JsonNumber* value = dynamic_cast<JsonNumber*>(m_config[key]);
        if(value == NULL)
        {
            return defaultValue;
        }

        return value.value;
    }

    /**
     * 獲取布爾值
     * @param key 鍵
     * @param defaultValue 默認值
     * @return 布爾值
     */
    bool getBoolean(string key, bool defaultValue=false)
    {
        if(m_config == NULL || !m_config.contains(key))
        {
            return defaultValue;
        }

        JsonBoolean* value = dynamic_cast<JsonBoolean*>(m_config[key]);
        if(value == NULL)
        {
            return defaultValue;
        }

        return value.value;
    }

    /**
     * 獲取數組
     * @param key 鍵
     * @return 數組對象，不存在時返回NULL
     */
    JsonArray* getArray(string key)
    {
        if(m_config == NULL || !m_config.contains(key))
        {
            return NULL;
        }

        return dynamic_cast<JsonArray*>(m_config[key]);
    }

    /**
     * 獲取對象
     * @param key 鍵
     * @return 對象，不存在時返回NULL
     */
    JsonObject* getObject(string key)
    {
        if(m_config == NULL || !m_config.contains(key))
        {
            return NULL;
        }

        return dynamic_cast<JsonObject*>(m_config[key]);
    }

    /**
     * 檢查鍵是否存在
     * @param key 鍵
     * @return 是否存在
     */
    bool hasKey(string key)
    {
        if(m_config == NULL)
        {
            return false;
        }

        return m_config.contains(key);
    }

    /**
     * 獲取值類型
     * @param key 鍵
     * @return 值類型
     */
    ENUM_CONFIG_VALUE_TYPE getValueType(string key)
    {
        if(m_config == NULL || !m_config.contains(key))
        {
            return CONFIG_TYPE_NULL;
        }

        JsonValue* value = m_config[key];

        if(value == null)
        {
            return CONFIG_TYPE_NULL;
        }
        else if(CheckPointer(dynamic_cast<JsonBoolean*>(value)) != POINTER_INVALID)
        {
            return CONFIG_TYPE_BOOLEAN;
        }
        else if(CheckPointer(dynamic_cast<JsonNumber*>(value)) != POINTER_INVALID)
        {
            return CONFIG_TYPE_NUMBER;
        }
        else if(CheckPointer(dynamic_cast<JsonString*>(value)) != POINTER_INVALID)
        {
            return CONFIG_TYPE_STRING;
        }
        else if(CheckPointer(dynamic_cast<JsonArray*>(value)) != POINTER_INVALID)
        {
            return CONFIG_TYPE_ARRAY;
        }
        else if(CheckPointer(dynamic_cast<JsonObject*>(value)) != POINTER_INVALID)
        {
            return CONFIG_TYPE_OBJECT;
        }

        return CONFIG_TYPE_NULL;
    }

    // 配置更新方法

    /**
     * 設置字符串值
     * @param key 鍵
     * @param value 值
     */
    void setString(string key, string value)
    {
        if(m_config == NULL) return;

        m_config.setString(key, value);
    }

    /**
     * 設置數值
     * @param key 鍵
     * @param value 值
     */
    void setNumber(string key, double value)
    {
        if(m_config == NULL) return;

        m_config.setNumber(key, value);
    }

    /**
     * 設置布爾值
     * @param key 鍵
     * @param value 值
     */
    void setBoolean(string key, bool value)
    {
        if(m_config == NULL) return;

        m_config.setBoolean(key, value);
    }

    /**
     * 設置數組
     * @param key 鍵
     * @param value 值
     */
    void setArray(string key, JsonArray* value)
    {
        if(m_config == NULL) return;

        m_config.setArray(key, value);
    }

    /**
     * 設置對象
     * @param key 鍵
     * @param value 值
     */
    void setObject(string key, JsonObject* value)
    {
        if(m_config == NULL) return;

        m_config.setObject(key, value);
    }

    /**
     * 移除鍵
     * @param key 鍵
     * @return 是否成功
     */
    bool remove(string key)
    {
        if(m_config == NULL)
        {
            return false;
        }

        return m_config.remove(key);
    }

    /**
     * 清空配置
     */
    void clear()
    {
        if(m_config == NULL) return;

        m_config.clear();
    }

    /**
     * 獲取配置對象
     * @return 配置對象
     */
    JsonObject* getConfig()
    {
        return m_config;
    }

    /**
     * 檢查是否已加載
     * @return 是否已加載
     */
    bool isLoaded()
    {
        return m_isLoaded;
    }
};

// 初始化靜態成員
JsonConfigurationManager* JsonConfigurationManager::m_instance = NULL;

#endif // _JSON_CONFIGURATION_MANAGER_MQH_
