#property strict

#include "../EACompoundPipeline.mqh"
#include "../EAErrorHandler.mqh"

//+------------------------------------------------------------------+
//| EAErrorHandlingCompoundPipeline 類 - 錯誤處理裝飾者模式實現的 EACompoundPipeline |
//+------------------------------------------------------------------+
class EAErrorHandlingCompoundPipeline : public EACompoundPipeline
{
private:
    EAErrorHandler* m_error_handler;  // 錯誤處理器

public:
    // 構造函數
    EAErrorHandlingCompoundPipeline(PipelineComposite* composite, string type = "EAErrorHandlingCompoundPipeline", EAErrorHandler* error_handler = NULL)
        : EACompoundPipeline(composite, type),
          m_error_handler(error_handler?error_handler:EAErrorHandler::GetInstance())
    {
    }
    
    EAErrorHandlingCompoundPipeline(EACompoundPipeline* composite, string type = "EAErrorHandlingCompoundPipeline", EAErrorHandler* error_handler = NULL)
        : EACompoundPipeline(composite, type),
          m_error_handler(error_handler?error_handler:EAErrorHandler::GetInstance())
    {
        EACompoundPipeline::SetComposite(composite.GetComposite());
    }
    
    EAErrorHandlingCompoundPipeline(string name, string type = "EAErrorHandlingCompoundPipeline", EAErrorHandler* error_handler = NULL, int maxItems = 100)
        : EACompoundPipeline(name, type, maxItems),
          m_error_handler(error_handler?error_handler:EAErrorHandler::GetInstance())
    {
    }

    // 獲取結果
    virtual PipelineResult* GetResult() override {
        PipelineResult* result = EACompoundPipeline::GetResult();
        if(!result.IsSuccess()) {
            m_error_handler.HandleError(result.GetMessage());
        }
        return result;
    }

    // 添加子流水線
    virtual PipelineResult* Add(IPipeline* child) override {
        PipelineResult* result = EACompoundPipeline::Add(child);
        if(!result.IsSuccess()) {
            m_error_handler.HandleError(result.GetMessage());
        }
        return result;
    }

    // 移除子流水線
    virtual PipelineResult* Remove(IPipeline* child) override {
        PipelineResult* result = EACompoundPipeline::Remove(child);
        if(!result.IsSuccess()) {
            m_error_handler.HandleError(result.GetMessage());
        }
        return result;
    }
};
