//+------------------------------------------------------------------+
//|                                        TestDocumentOutputDemo.mq4 |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "RunAllTests.mqh"
#include "DocumentOutputExample.mqh"

//+------------------------------------------------------------------+
//| PipelineAdvance 測試文檔輸出功能演示腳本                          |
//| 這個腳本演示如何使用新的文檔輸出功能                               |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 腳本程序開始函數                                                 |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("╔══════════════════════════════════════════════════════════════╗");
    Print("║         PipelineAdvance 測試文檔輸出功能演示                  ║");
    Print("║                      EA_Wizard                               ║");
    Print("╚══════════════════════════════════════════════════════════════╝");
    Print("");
    
    // 演示基本文檔輸出功能
    DemoBasicDocumentOutput();
    
    Print("");
    Print("═══════════════════════════════════════════════════════════════");
    Print("");
    
    // 演示自定義文檔輸出選項
    DemoCustomDocumentOptions();
    
    Print("");
    Print("═══════════════════════════════════════════════════════════════");
    Print("");
    
    // 演示特定測試類別的文檔輸出
    DemoSpecificTestDocuments();
    
    Print("");
    Print("╔══════════════════════════════════════════════════════════════╗");
    Print("║                    演示完成                                  ║");
    Print("║     請檢查 MQL4\\Files\\ 目錄中生成的測試文檔                ║");
    Print("╚══════════════════════════════════════════════════════════════╝");
}

//+------------------------------------------------------------------+
//| 演示基本文檔輸出功能                                             |
//+------------------------------------------------------------------+
void DemoBasicDocumentOutput()
{
    Print("🎯 演示 1: 基本文檔輸出功能");
    Print("────────────────────────────────────────────────────────────");
    
    Print("正在運行所有測試並生成文檔...");
    
    // 運行所有測試並生成文檔
    TestPipelineAdvanceWithDocs();
    
    Print("");
    Print("✅ 基本文檔輸出演示完成");
    Print("📁 生成的文檔:");
    Print("   • 完整報告: PipelineAdvance_TestReport_[時間戳].txt");
    Print("   • 測試摘要: PipelineAdvance_TestReport_Summary_[時間戳].txt");
    Print("   • 保存位置: MQL4\\Files\\TestReports\\");
}

//+------------------------------------------------------------------+
//| 演示自定義文檔輸出選項                                           |
//+------------------------------------------------------------------+
void DemoCustomDocumentOptions()
{
    Print("🎯 演示 2: 自定義文檔輸出選項");
    Print("────────────────────────────────────────────────────────────");
    
    Print("正在使用自定義選項生成文檔...");
    
    // 只生成完整報告，保存到自定義目錄
    RunPipelineAdvanceTestsWithCustomDocs(
        true,                           // 生成完整報告
        false,                          // 不生成摘要
        "Demo_FullReportsOnly"          // 自定義目錄
    );
    
    Print("");
    
    // 只生成摘要，保存到另一個自定義目錄
    RunPipelineAdvanceTestsWithCustomDocs(
        false,                          // 不生成完整報告
        true,                           // 生成摘要
        "Demo_SummaryOnly"              // 自定義目錄
    );
    
    Print("");
    Print("✅ 自定義文檔輸出演示完成");
    Print("📁 生成的文檔:");
    Print("   • 完整報告: MQL4\\Files\\Demo_FullReportsOnly\\");
    Print("   • 摘要報告: MQL4\\Files\\Demo_SummaryOnly\\");
}

//+------------------------------------------------------------------+
//| 演示特定測試類別的文檔輸出                                       |
//+------------------------------------------------------------------+
void DemoSpecificTestDocuments()
{
    Print("🎯 演示 3: 特定測試類別文檔輸出");
    Print("────────────────────────────────────────────────────────────");
    
    // 演示不同測試類別的文檔輸出
    string testClasses[] = {
        "TestPipelineResult",
        "TestPipelineComposite"
    };
    
    for(int i = 0; i < ArraySize(testClasses); i++)
    {
        Print(StringFormat("正在運行 %s 測試並生成文檔...", testClasses[i]));
        RunSpecificPipelineAdvanceTestWithDocs(testClasses[i]);
        Print("");
    }
    
    Print("✅ 特定測試類別文檔輸出演示完成");
    Print("📁 每個測試類別都生成了獨立的文檔");
}

//+------------------------------------------------------------------+
//| 演示完整測試套件文檔輸出                                         |
//+------------------------------------------------------------------+
void DemoFullTestSuite()
{
    Print("🎯 演示 4: 完整測試套件文檔輸出");
    Print("────────────────────────────────────────────────────────────");
    
    Print("正在運行完整測試套件並生成文檔...");
    Print("注意: 這包含環境驗證、性能測試等額外功能");
    
    // 運行完整測試套件
    FullPipelineAdvanceTestSuiteWithDocs();
    
    Print("");
    Print("✅ 完整測試套件文檔輸出演示完成");
    Print("📁 生成了包含所有測試結果的完整文檔");
}

//+------------------------------------------------------------------+
//| 演示文檔內容說明                                                 |
//+------------------------------------------------------------------+
void ShowDocumentContentInfo()
{
    Print("📋 生成的文檔內容說明:");
    Print("────────────────────────────────────────────────────────────");
    Print("");
    
    Print("📄 完整報告包含:");
    Print("   • 測試執行摘要（總數、通過、失敗、成功率）");
    Print("   • 詳細統計信息（通過率、失敗率）");
    Print("   • 執行時間統計（總時間、平均、最長/最短）");
    Print("   • 失敗測試詳情（錯誤信息、測試類別）");
    Print("   • 通過測試列表（包含執行時間）");
    Print("   • 按測試類別分組的結果");
    Print("");
    
    Print("📄 測試摘要包含:");
    Print("   • 簡化的測試執行摘要");
    Print("   • 基本統計信息");
    Print("   • 適合快速查看測試結果");
    Print("");
    
    Print("📁 文檔保存位置:");
    Print("   • 默認: MQL4\\Files\\TestReports\\");
    Print("   • 自定義: MQL4\\Files\\[自定義目錄]\\");
    Print("   • 文件名包含時間戳，避免覆蓋");
}

//+------------------------------------------------------------------+
//| 使用提示                                                         |
//+------------------------------------------------------------------+
void ShowUsageTips()
{
    Print("💡 使用提示:");
    Print("────────────────────────────────────────────────────────────");
    Print("");
    
    Print("1. 快速開始:");
    Print("   TestPipelineAdvanceWithDocs();");
    Print("");
    
    Print("2. 自定義選項:");
    Print("   RunPipelineAdvanceTestsWithCustomDocs(true, true, \"MyReports\");");
    Print("");
    
    Print("3. 特定測試:");
    Print("   RunSpecificPipelineAdvanceTestWithDocs(\"TestPipelineResult\");");
    Print("");
    
    Print("4. 查看示例:");
    Print("   參考 DocumentOutputExample.mqh 文件");
    Print("");
    
    Print("5. 故障排除:");
    Print("   • 檢查 MQL4\\Files\\ 目錄權限");
    Print("   • 確保有足夠磁盤空間");
    Print("   • 查看控制台錯誤信息");
}
