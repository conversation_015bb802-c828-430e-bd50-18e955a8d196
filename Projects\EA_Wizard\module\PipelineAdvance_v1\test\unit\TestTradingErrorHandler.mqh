#property strict

//+------------------------------------------------------------------+
//| TestTradingErrorHandler.mqh                                      |
//| TradingErrorHandler 類的單元測試                                 |
//+------------------------------------------------------------------+

#include "../TestFramework.mqh"
#include "../../TradingErrorHandler.mqh"
#include "../../TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| Mock 訪問者類 - 用於測試                                        |
//+------------------------------------------------------------------+
class MockErrorVisitor : public TradingMessageVisitor
{
private:
    int m_visitCount;
    string m_lastMessage;
    ENUM_ERROR_LEVEL m_lastLevel;

public:
    MockErrorVisitor() : m_visitCount(0), m_lastMessage(""), m_lastLevel(ERROR_LEVEL_INFO) {}

    virtual void Visit(const TradingMessageRecord& record) override
    {
        m_visitCount++;
        m_lastMessage = record.m_message;
        m_lastLevel = record.m_errorLevel;
    }

    int GetVisitCount() const { return m_visitCount; }
    string GetLastMessage() const { return m_lastMessage; }
    ENUM_ERROR_LEVEL GetLastLevel() const { return m_lastLevel; }
    void Reset() { m_visitCount = 0; m_lastMessage = ""; m_lastLevel = ERROR_LEVEL_INFO; }
};

//+------------------------------------------------------------------+
//| TradingMessageHandler 單元測試類                                |
//+------------------------------------------------------------------+
class TestTradingErrorHandler : public TestCase
{
private:
    TestRunner* m_runner;

public:
    TestTradingErrorHandler(TestRunner* runner = NULL)
        : TestCase("TestTradingErrorHandler"), m_runner(runner) {}

    virtual void RunTests() override
    {
        Print("開始執行 TradingMessageHandler 單元測試...");

        TestConstructorAndBasicProperties();
        TestAddMessage();
        TestMessageManagement();
        TestVisitorPattern();
        TestPipelineResultHandling();
        TestMessageStatistics();
        TestMessageLevelFiltering();
        TestMaxMessagesLimit();

        Print("TradingMessageHandler 單元測試完成");
    }

private:
    // 測試構造函數和基本屬性
    void TestConstructorAndBasicProperties()
    {
        Print("--- 測試 TradingMessageHandler 構造函數和基本屬性 ---");

        TradingMessageHandler* handler = new TradingMessageHandler();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestConstructorAndBasicProperties - 初始狀態為空",
                handler.IsEmpty(),
                "初始狀態應該為空"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestConstructorAndBasicProperties - 初始訊息數量為0",
                handler.GetMessageCount() == 0,
                "初始訊息數量應該為0"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestConstructorAndBasicProperties - 初始無嚴重訊息",
                !handler.HasMessages(ERROR_LEVEL_CRITICAL),
                "初始狀態不應該有嚴重訊息"
            ));

            TradingMessageRecord* lastRecord = handler.GetLastMessage();
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestConstructorAndBasicProperties - 初始最後訊息為空",
                lastRecord != NULL && lastRecord.m_errorLevel == ERROR_LEVEL_NULL,
                "初始最後訊息應該為 NULL 級別記錄"
            ));
            if(lastRecord != NULL) delete lastRecord;
        }

        delete handler;
    }

    // 測試添加訊息
    void TestAddMessage()
    {
        Print("--- 測試 TradingMessageHandler 添加訊息 ---");

        TradingMessageHandler* handler = new TradingMessageHandler();

        // 添加第一個訊息
        handler.AddMessage("測試訊息1", "TestSource", ERROR_LEVEL_WARNING);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestAddMessage - 訊息數量增加",
                handler.GetMessageCount() == 1,
                "添加訊息後數量應該為1"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestAddMessage - 不再為空",
                !handler.IsEmpty(),
                "添加訊息後不應該為空"
            ));

            TradingMessageRecord* lastRecord = handler.GetLastMessage(ERROR_LEVEL_WARNING);
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestAddMessage - 最後訊息正確",
                lastRecord != NULL && lastRecord.m_message == "測試訊息1",
                "最後訊息消息應該正確"
            ));
            if(lastRecord != NULL) delete lastRecord;

            TradingMessageRecord* firstRecord = handler.GetFirstMessage(ERROR_LEVEL_WARNING);
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestAddMessage - 第一個訊息正確",
                firstRecord != NULL && firstRecord.m_message == "測試訊息1",
                "第一個訊息消息應該正確"
            ));
            if(firstRecord != NULL) delete firstRecord;
        }

        // 添加第二個訊息
        handler.AddMessage("測試訊息2", "TestSource2", ERROR_LEVEL_ERROR);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestAddMessage - 多個訊息數量",
                handler.GetMessageCount() == 2,
                "添加第二個訊息後數量應該為2"
            ));

            TradingMessageRecord* lastRecord = handler.GetLastMessage(ERROR_LEVEL_ERROR);
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestAddMessage - 最後訊息更新",
                lastRecord != NULL && lastRecord.m_message == "測試訊息2",
                "最後訊息應該更新為最新的"
            ));
            if(lastRecord != NULL) delete lastRecord;

            TradingMessageRecord* firstRecord = handler.GetFirstMessage(ERROR_LEVEL_WARNING);
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestAddMessage - 第一個訊息保持",
                firstRecord != NULL && firstRecord.m_message == "測試訊息1",
                "第一個訊息應該保持不變"
            ));
            if(firstRecord != NULL) delete firstRecord;
        }

        delete handler;
    }

    // 測試訊息管理功能
    void TestMessageManagement()
    {
        Print("--- 測試 TradingMessageHandler 訊息管理 ---");

        TradingMessageHandler* handler = new TradingMessageHandler();

        // 添加多個訊息
        handler.AddMessage("訊息1", "Source1", ERROR_LEVEL_INFO);
        handler.AddMessage("訊息2", "Source2", ERROR_LEVEL_WARNING);
        handler.AddMessage("訊息3", "Source3", ERROR_LEVEL_ERROR);

        // 測試 PopMessage
        TradingMessageRecord* poppedRecord = handler.PopMessage();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestMessageManagement - PopMessage返回正確",
                poppedRecord != NULL && poppedRecord.m_message == "訊息3",
                "PopMessage應該返回最後一個訊息"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestMessageManagement - PopMessage後數量減少",
                handler.GetMessageCount() == 2,
                "PopMessage後訊息數量應該減少"
            ));
        }
        if(poppedRecord != NULL) delete poppedRecord;

        // 測試 ShiftMessage
        TradingMessageRecord* shiftedRecord = handler.ShiftMessage();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestMessageManagement - ShiftMessage返回正確",
                shiftedRecord != NULL && shiftedRecord.m_message == "訊息1",
                "ShiftMessage應該返回第一個訊息"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestMessageManagement - ShiftMessage後數量減少",
                handler.GetMessageCount() == 1,
                "ShiftMessage後訊息數量應該減少"
            ));
        }
        if(shiftedRecord != NULL) delete shiftedRecord;

        // 測試 ClearMessages
        handler.ClearMessages();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestMessageManagement - ClearMessages清空",
                handler.IsEmpty(),
                "ClearMessages後應該為空"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestMessageManagement - ClearMessages數量為0",
                handler.GetMessageCount() == 0,
                "ClearMessages後數量應該為0"
            ));
        }

        delete handler;
    }

    // 測試訪問者模式
    void TestVisitorPattern()
    {
        Print("--- 測試 TradingMessageHandler 訪問者模式 ---");

        TradingMessageHandler* handler = new TradingMessageHandler();
        MockErrorVisitor* visitor = new MockErrorVisitor();

        // 添加訊息
        handler.AddMessage("訪問者測試訊息", "VisitorTest", ERROR_LEVEL_CRITICAL);

        // 使用訪問者處理訊息
        handler.HandleMessage(visitor);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestVisitorPattern - 訪問次數正確",
                visitor.GetVisitCount() == 1,
                "訪問者應該被調用一次"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestVisitorPattern - 訪問消息正確",
                visitor.GetLastMessage() == "訪問者測試訊息",
                "訪問者應該收到正確的訊息消息"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestVisitorPattern - 訪問級別正確",
                visitor.GetLastLevel() == ERROR_LEVEL_CRITICAL,
                "訪問者應該收到正確的訊息級別"
            ));
        }

        delete visitor;
        delete handler;
    }

    // 測試 PipelineResult 處理
    void TestPipelineResultHandling()
    {
        Print("--- 測試 TradingMessageHandler PipelineResult 處理 ---");

        TradingMessageHandler* handler = new TradingMessageHandler();

        // 創建失敗的 PipelineResult
        PipelineResult* failedResult = new PipelineResult(false, "流水線執行失敗", "TestPipeline", ERROR_LEVEL_ERROR);

        // 處理 PipelineResult
        handler.HandlePipelineResult(failedResult);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestPipelineResultHandling - 處理失敗結果",
                handler.GetMessageCount() == 1,
                "處理失敗的PipelineResult應該添加訊息"
            ));

            TradingMessageRecord* lastRecord = handler.GetLastMessage(ERROR_LEVEL_ERROR);
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestPipelineResultHandling - 訊息消息正確",
                lastRecord != NULL && lastRecord.m_message == "流水線執行失敗",
                "訊息消息應該來自PipelineResult"
            ));
            if(lastRecord != NULL) delete lastRecord;
        }

        // 創建成功的 PipelineResult
        PipelineResult* successResult = new PipelineResult(true, "流水線執行成功", "TestPipeline", ERROR_LEVEL_INFO);

        // 處理成功的結果（不應該添加錯誤）
        handler.HandlePipelineResult(successResult);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestPipelineResultHandling - 忽略成功結果",
                handler.GetMessageCount() == 1,
                "處理成功的PipelineResult不應該添加訊息"
            ));
        }

        delete failedResult;
        delete successResult;
        delete handler;
    }

    // 測試訊息統計
    void TestMessageStatistics()
    {
        Print("--- 測試 TradingMessageHandler 訊息統計 ---");

        TradingMessageHandler* handler = new TradingMessageHandler();

        // 添加不同級別的訊息
        handler.AddMessage("信息", "Test", ERROR_LEVEL_INFO);
        handler.AddMessage("警告", "Test", ERROR_LEVEL_WARNING);
        handler.AddMessage("錯誤", "Test", ERROR_LEVEL_ERROR);
        handler.AddMessage("嚴重", "Test", ERROR_LEVEL_CRITICAL);
        handler.AddMessage("另一個錯誤", "Test", ERROR_LEVEL_ERROR);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestMessageStatistics - INFO級別統計",
                handler.GetMessageCountByLevel(ERROR_LEVEL_INFO) == 1,
                "INFO級別訊息數量應該為1"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestMessageStatistics - WARNING級別統計",
                handler.GetMessageCountByLevel(ERROR_LEVEL_WARNING) == 1,
                "WARNING級別訊息數量應該為1"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestMessageStatistics - ERROR級別統計",
                handler.GetMessageCountByLevel(ERROR_LEVEL_ERROR) == 2,
                "ERROR級別訊息數量應該為2"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestMessageStatistics - CRITICAL級別統計",
                handler.GetMessageCountByLevel(ERROR_LEVEL_CRITICAL) == 1,
                "CRITICAL級別訊息數量應該為1"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestMessageStatistics - 有嚴重訊息",
                handler.HasMessages(ERROR_LEVEL_CRITICAL),
                "應該檢測到嚴重訊息"
            ));
        }

        delete handler;
    }

    // 測試訊息級別過濾
    void TestMessageLevelFiltering()
    {
        Print("--- 測試 TradingMessageHandler 訊息級別過濾 ---");

        TradingMessageHandler* handler = new TradingMessageHandler();
        MockErrorVisitor* targetVisitor = new MockErrorVisitor();
        MessageFilterVisitor* filterVisitor = new MessageFilterVisitor(ERROR_LEVEL_ERROR, targetVisitor);

        // 添加不同級別的訊息
        handler.AddMessage("信息訊息", "Test", ERROR_LEVEL_INFO);
        handler.AddMessage("普通訊息", "Test", ERROR_LEVEL_ERROR);
        handler.AddMessage("警告訊息", "Test", ERROR_LEVEL_WARNING);

        // 使用過濾訪問者（只處理ERROR級別）
        handler.HandleMessage(filterVisitor);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestMessageLevelFiltering - 過濾器只處理指定級別",
                targetVisitor.GetVisitCount() == 1,
                "過濾器應該只處理ERROR級別的訊息"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestMessageLevelFiltering - 過濾器消息正確",
                targetVisitor.GetLastMessage() == "普通訊息",
                "過濾器應該傳遞正確的訊息消息"
            ));
        }

        delete filterVisitor;
        delete targetVisitor;
        delete handler;
    }

    // 測試最大訊息數量限制
    void TestMaxMessagesLimit()
    {
        Print("--- 測試 TradingMessageHandler 最大訊息數量限制 ---");

        TradingMessageHandler* handler = new TradingMessageHandler();

        // 設置較小的最大訊息數量
        handler.SetMaxMessageCount(3);

        // 添加超過限制的訊息
        handler.AddMessage("訊息1", "Test", ERROR_LEVEL_INFO);
        handler.AddMessage("訊息2", "Test", ERROR_LEVEL_INFO);
        handler.AddMessage("訊息3", "Test", ERROR_LEVEL_INFO);
        handler.AddMessage("訊息4", "Test", ERROR_LEVEL_INFO);  // 應該替換最舊的訊息

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestMaxMessagesLimit - 訊息數量限制",
                handler.GetMessageCount() == 3,
                "訊息數量應該不超過設定的最大值"
            ));

            TradingMessageRecord* firstRecord = handler.GetFirstMessage(ERROR_LEVEL_INFO);
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestMaxMessagesLimit - 最舊訊息被替換",
                firstRecord != NULL && firstRecord.m_message == "訊息2",
                "最舊的訊息應該被新訊息替換"
            ));
            if(firstRecord != NULL) delete firstRecord;

            TradingMessageRecord* lastRecord = handler.GetLastMessage(ERROR_LEVEL_INFO);
            m_runner.RecordResult(new TestResult(
                "TestTradingErrorHandler::TestMaxMessagesLimit - 最新訊息保留",
                lastRecord != NULL && lastRecord.m_message == "訊息4",
                "最新的訊息應該被保留"
            ));
            if(lastRecord != NULL) delete lastRecord;
        }

        delete handler;
    }
};
