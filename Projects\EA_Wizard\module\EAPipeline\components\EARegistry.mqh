#property strict

#include "../../RegistryAdvance/ItemRegistry.mqh"
#include "EARegistryBase.mqh"

#define EA_REGISTRY_NAME "EARegistry"
#define EA_REGISTRY_TYPE "EARegistry"
#define EA_REGISTRY_MAX_ITEMS 100

//+------------------------------------------------------------------+
//| EARegistry 類 - EARegistryBase 的實現                            |
//+------------------------------------------------------------------+
class EARegistry : public EARegistryBase
{
private:
    static EARegistry* m_instance;  // 單例實例（保留以向後兼容）

    // 私有構造函數
    EARegistry()
        : EARegistryBase(EA_REGISTRY_NAME, EA_REGISTRY_TYPE, EA_REGISTRY_MAX_ITEMS)
    {
    }

    // 私有析構函數
    virtual ~EARegistry()
    {
        // 基類析構函數會處理資源釋放
    }
public:

    // 獲取單例實例（保留以向後兼容）
    static EARegistry* GetInstance()
    {
        if(m_instance == NULL)
        {
            m_instance = new EARegistry();
        }
        return m_instance;
    }
};

// 初始化靜態成員
EARegistry* EARegistry::m_instance = NULL;
