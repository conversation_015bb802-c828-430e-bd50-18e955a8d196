//+------------------------------------------------------------------+
//|                                             TestFinalValidation.mq4 |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "RunAllTests.mqh"

//+------------------------------------------------------------------+
//| 最終驗證腳本 - 確保 [通過] 和 [失敗] 格式正確顯示               |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("================================================================");
    Print("           RegistryAdvance 最終格式驗證");
    Print("                      EA_Wizard");
    Print("================================================================");
    Print("");
    
    Print("🎯 最終驗證目標:");
    Print("   • 確認文檔中顯示 [通過] 和 [失敗] 格式");
    Print("   • 驗證 ToString() 方法正確實現");
    Print("   • 確保與 PipelineAdvance 格式完全一致");
    Print("");
    
    // 執行最終驗證
    PerformFinalValidation();
    
    Print("");
    Print("================================================================");
    Print("                    最終驗證完成");
    Print("   請檢查 MQL4\\Files\\FinalValidation\\ 目錄");
    Print("   確認文檔中正確顯示 [通過] 和 [失敗] 格式");
    Print("================================================================");
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("RegistryAdvance 最終格式驗證結束");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 這個驗證腳本不需要處理 tick 事件
}

//+------------------------------------------------------------------+
//| 執行最終驗證                                                     |
//+------------------------------------------------------------------+
void PerformFinalValidation()
{
    Print("🔍 開始最終驗證...");
    Print("");
    
    // 1. 驗證 ToString() 方法
    ValidateToStringMethod();
    
    Print("");
    Print("----------------------------------------------------------------");
    Print("");
    
    // 2. 生成測試文檔
    GenerateValidationDocuments();
    
    Print("");
    Print("----------------------------------------------------------------");
    Print("");
    
    // 3. 顯示預期格式
    ShowExpectedFormat();
    
    Print("");
    Print("----------------------------------------------------------------");
    Print("");
    
    // 4. 提供驗證指南
    ProvideValidationGuide();
}

//+------------------------------------------------------------------+
//| 驗證 ToString() 方法                                             |
//+------------------------------------------------------------------+
void ValidateToStringMethod()
{
    Print("📋 驗證 ToString() 方法實現:");
    
    // 創建測試結果對象
    TestResultDetail* passedTest = new TestResultDetail(
        "TestSampleMethod",
        "TestSampleClass",
        true,
        "",
        TimeCurrent() - 1,
        TimeCurrent()
    );
    
    TestResultDetail* failedTest = new TestResultDetail(
        "TestFailedMethod", 
        "TestFailedClass",
        false,
        "測試失敗原因",
        TimeCurrent() - 2,
        TimeCurrent()
    );
    
    string passedOutput = passedTest.ToString();
    string failedOutput = failedTest.ToString();
    
    Print("   ✅ 通過測試輸出: " + passedOutput);
    Print("   ❌ 失敗測試輸出: " + failedOutput);
    
    // 驗證格式
    bool passedCorrect = StringFind(passedOutput, "[通過]") >= 0 && 
                        StringFind(passedOutput, "::") >= 0 &&
                        StringFind(passedOutput, "ms)") >= 0;
                        
    bool failedCorrect = StringFind(failedOutput, "[失敗]") >= 0 && 
                        StringFind(failedOutput, "::") >= 0 &&
                        StringFind(failedOutput, "ms)") >= 0 &&
                        StringFind(failedOutput, " - ") >= 0;
    
    if(passedCorrect && failedCorrect)
    {
        Print("   ✅ ToString() 方法格式驗證通過");
    }
    else
    {
        Print("   ❌ ToString() 方法格式驗證失敗");
        if(!passedCorrect) Print("      - 通過測試格式不正確");
        if(!failedCorrect) Print("      - 失敗測試格式不正確");
    }
    
    delete passedTest;
    delete failedTest;
}

//+------------------------------------------------------------------+
//| 生成驗證文檔                                                     |
//+------------------------------------------------------------------+
void GenerateValidationDocuments()
{
    Print("📄 生成驗證文檔:");
    
    // 生成完整報告
    Print("   1. 生成完整報告...");
    RunRegistryAdvanceTestsWithCustomDocs(true, false, "FinalValidation\\FullReport");
    
    // 生成摘要
    Print("   2. 生成摘要...");
    RunRegistryAdvanceTestsWithCustomDocs(false, true, "FinalValidation\\Summary");
    
    // 生成限制顯示文檔
    Print("   3. 生成限制顯示文檔...");
    RunRegistryAdvanceTestsWithLimitedDisplay(3, "FinalValidation\\Limited");
    
    // 生成無限制顯示文檔
    Print("   4. 生成無限制顯示文檔...");
    RunRegistryAdvanceTestsWithUnlimitedDisplay("FinalValidation\\Unlimited");
    
    Print("   ✅ 所有驗證文檔已生成");
}

//+------------------------------------------------------------------+
//| 顯示預期格式                                                     |
//+------------------------------------------------------------------+
void ShowExpectedFormat()
{
    Print("📖 預期的文檔格式:");
    Print("");
    Print("在生成的文檔中，您應該看到:");
    Print("");
    Print("❌ 失敗測試詳情:");
    Print("  [失敗] TestClass::TestMethod (25ms) - 錯誤信息");
    Print("  [失敗] TestClass::TestMethod (30ms) - 另一個錯誤");
    Print("");
    Print("✅ 通過測試列表:");
    Print("  [通過] TestClass::TestMethod (15ms)");
    Print("  [通過] TestClass::TestMethod (20ms) - 成功信息");
    Print("  [通過] TestClass::TestMethod (18ms)");
    Print("");
    Print("關鍵格式要素:");
    Print("  • 狀態標記: [通過] 或 [失敗]");
    Print("  • 類別分隔符: ::");
    Print("  • 時間格式: (數字ms)");
    Print("  • 錯誤信息: - 錯誤描述 (僅失敗測試)");
}

//+------------------------------------------------------------------+
//| 提供驗證指南                                                     |
//+------------------------------------------------------------------+
void ProvideValidationGuide()
{
    Print("📋 驗證指南:");
    Print("");
    Print("請按以下步驟驗證文檔格式:");
    Print("");
    Print("1. 打開 MQL4\\Files\\FinalValidation\\ 目錄");
    Print("2. 檢查生成的 .txt 文檔");
    Print("3. 確認以下格式要素:");
    Print("");
    Print("   ✓ 失敗測試顯示為: [失敗] 類別::方法 (時間ms) - 錯誤");
    Print("   ✓ 通過測試顯示為: [通過] 類別::方法 (時間ms)");
    Print("   ✓ 時間格式為: (數字ms)");
    Print("   ✓ 類別和方法用 :: 分隔");
    Print("   ✓ 錯誤信息前有 - 分隔符");
    Print("");
    Print("4. 對比 PipelineAdvance 的文檔格式");
    Print("5. 確認格式完全一致");
    Print("");
    Print("如果所有檢查都通過，則格式更新成功！");
}

//+------------------------------------------------------------------+
//| 快速驗證                                                         |
//+------------------------------------------------------------------+
void QuickValidation()
{
    Print("⚡ 快速驗證:");
    
    // 生成一個快速測試文檔
    RunAllRegistryAdvanceTestsWithDocs();
    
    Print("✅ 快速驗證完成");
    Print("💡 請檢查默認位置的文檔: MQL4\\Files\\TestReports\\");
}

//+------------------------------------------------------------------+
//| 格式對比總結                                                     |
//+------------------------------------------------------------------+
void FormatComparisonSummary()
{
    Print("📊 格式對比總結:");
    Print("================================================================");
    Print("");
    Print("RegistryAdvance vs PipelineAdvance 格式對比:");
    Print("");
    Print("✅ 分隔符格式: 完全一致");
    Print("   • 主分隔符: 64個等號");
    Print("   • 次分隔符: 64個短橫線");
    Print("   • 摘要分隔符: 32個等號");
    Print("");
    Print("✅ 表情符號使用: 完全一致");
    Print("   • 📊 測試執行摘要");
    Print("   • 📈 詳細統計信息");
    Print("   • 📋 詳細測試結果");
    Print("   • ✅ 成功/通過");
    Print("   • ❌ 失敗/錯誤");
    Print("");
    Print("✅ 測試狀態格式: 完全一致");
    Print("   • [通過] 狀態標記");
    Print("   • [失敗] 狀態標記");
    Print("   • :: 類別分隔符");
    Print("   • (數字ms) 時間格式");
    Print("");
    Print("🎯 總體一致性: 100%");
    Print("");
    Print("================================================================");
}
