//+------------------------------------------------------------------+
//|                                   TestTradingPipelineExplorer.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                    |
//+------------------------------------------------------------------+
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipelineExplorer.mqh"
#include "TestTradingPipeline.mqh"  // 使用 MockTradingPipeline

//+------------------------------------------------------------------+
//| TradingPipelineExplorer 單元測試類                               |
//+------------------------------------------------------------------+
class TestTradingPipelineExplorer : public TestCase
{
private:
    TestRunner* m_runner;

public:
    // 構造函數
    TestTradingPipelineExplorer(TestRunner* runner = NULL)
        : TestCase("TestTradingPipelineExplorer"), m_runner(runner) {}

    // 析構函數
    virtual ~TestTradingPipelineExplorer() {}

    // 運行所有測試
    virtual void RunTests() override
    {
        Print("=== 開始執行 TradingPipelineExplorer 單元測試 ===");

        TestConstructor();
        TestBasicProperties();
        TestPipelineByStage();
        TestPipelineByEvent();
        TestQueryMethods();
        TestStatisticMethods();
        TestReportGeneration();

        Print("=== TradingPipelineExplorer 單元測試完成 ===");
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        Print("--- 測試 TradingPipelineExplorer 構造函數 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("TestManager");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "TestRegistry");

        // 測試基本構造函數
        TradingPipelineExplorer* explorer1 = new TradingPipelineExplorer(registry);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineExplorer::TestConstructor - 基本構造函數",
                explorer1 != NULL,
                explorer1 != NULL ? "構造函數成功" : "構造函數失敗"
            ));
        }

        // 測試帶參數的構造函數
        TradingPipelineExplorer* explorer2 = new TradingPipelineExplorer(
            registry, "TestExplorer", "TestType", "測試探索器");

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineExplorer::TestConstructor - 帶參數構造函數",
                explorer2 != NULL && explorer2.GetName() == "TestExplorer",
                explorer2 != NULL ? "構造函數成功，名稱正確" : "構造函數失敗"
            ));
        }

        // 測試空註冊器構造函數
        TradingPipelineExplorer* explorer3 = new TradingPipelineExplorer(NULL);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineExplorer::TestConstructor - 空註冊器構造函數",
                explorer3 != NULL && !explorer3.IsValid(),
                "空註冊器構造函數成功，IsValid 返回 false"
            ));
        }

        delete explorer1;
        delete explorer2;
        delete explorer3;
        delete registry;
        delete manager;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("--- 測試 TradingPipelineExplorer 基本屬性 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("PropManager");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "PropRegistry");
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(
            registry, "PropTest", "TestType", "屬性測試探索器");

        // 測試 GetName
        string name = explorer.GetName();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineExplorer::TestBasicProperties - GetName",
                name == "PropTest",
                name == "PropTest" ? "名稱正確" : "名稱錯誤: " + name
            ));
        }

        // 測試 GetType
        string type = explorer.GetType();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineExplorer::TestBasicProperties - GetType",
                type == "TestType",
                type == "TestType" ? "類型正確" : "類型錯誤: " + type
            ));
        }

        // 測試 GetDescription
        string desc = explorer.GetDescription();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineExplorer::TestBasicProperties - GetDescription",
                desc == "屬性測試探索器",
                desc == "屬性測試探索器" ? "描述正確" : "描述錯誤: " + desc
            ));
        }

        // 測試 IsValid
        bool valid = explorer.IsValid();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineExplorer::TestBasicProperties - IsValid",
                valid,
                "探索器有效性正確"
            ));
        }

        // 測試 GetRegistry
        TradingPipelineRegistry* retrievedRegistry = explorer.GetRegistry();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineExplorer::TestBasicProperties - GetRegistry",
                retrievedRegistry == registry,
                "註冊器指針正確"
            ));
        }

        delete explorer;
        delete registry;
        delete manager;
    }

    // 測試按階段獲取流水線
    void TestPipelineByStage()
    {
        Print("--- 測試 TradingPipelineExplorer 按階段獲取流水線 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("StageManager");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "StageRegistry");
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(registry, "StageExplorer");

        // 創建容器和流水線
        TradingPipelineContainer* container = new TradingPipelineContainer("StageContainer");
        MockTradingPipeline* pipeline = new MockTradingPipeline("StagePipeline", "Mock", INIT_START);

        container.AddPipeline(pipeline);
        registry.Register(TRADING_INIT, container);

        // 測試按階段獲取流水線
        ITradingPipeline* found = explorer.GetPipeline(INIT_START);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineExplorer::TestPipelineByStage - 按階段獲取流水線",
                found != NULL,
                found != NULL ? "按階段獲取流水線成功" : "按階段獲取流水線失敗"
            ));
        }

        // 測試獲取不存在的階段
        ITradingPipeline* notFound = explorer.GetPipeline(TICK_DATA_FEED);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineExplorer::TestPipelineByStage - 獲取不存在階段",
                notFound == NULL,
                "正確返回 NULL"
            ));
        }

        delete explorer;
        delete registry;
        delete manager;
        delete container;
        delete pipeline;
    }

    // 測試按事件獲取流水線
    void TestPipelineByEvent()
    {
        Print("--- 測試 TradingPipelineExplorer 按事件獲取流水線 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("EventManager");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "EventRegistry");
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(registry, "EventExplorer");

        // 創建容器
        TradingPipelineContainer* container = new TradingPipelineContainer("EventContainer");
        registry.Register(TRADING_TICK, container);

        // 測試按事件獲取流水線
        ITradingPipeline* found = explorer.GetPipeline(TRADING_TICK);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineExplorer::TestPipelineByEvent - 按事件獲取流水線",
                found == container,
                "按事件獲取流水線成功"
            ));
        }

        // 測試獲取不存在的事件
        ITradingPipeline* notFound = explorer.GetPipeline(TRADING_DEINIT);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineExplorer::TestPipelineByEvent - 獲取不存在事件",
                notFound == NULL,
                "正確返回 NULL"
            ));
        }

        delete explorer;
        delete registry;
        delete manager;
        delete container;
    }

    // 測試查詢方法
    void TestQueryMethods()
    {
        Print("--- 測試 TradingPipelineExplorer 查詢方法 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("QueryManager");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "QueryRegistry");
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(registry, "QueryExplorer");

        // 創建容器和流水線
        TradingPipelineContainer* container = new TradingPipelineContainer("QueryContainer");
        MockTradingPipeline* pipeline = new MockTradingPipeline("QueryPipeline", "Mock", INIT_START);

        container.AddPipeline(pipeline);
        registry.Register(TRADING_INIT, container);

        // 測試 HasPipelineForStage
        bool hasStage = explorer.HasPipelineForStage(INIT_START);
        bool noStage = explorer.HasPipelineForStage(TICK_DATA_FEED);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineExplorer::TestQueryMethods - HasPipelineForStage",
                hasStage && !noStage,
                "HasPipelineForStage 方法正確"
            ));
        }

        // 測試 HasPipelineForEvent
        bool hasEvent = explorer.HasPipelineForEvent(TRADING_INIT);
        bool noEvent = explorer.HasPipelineForEvent(TRADING_DEINIT);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineExplorer::TestQueryMethods - HasPipelineForEvent",
                hasEvent && !noEvent,
                "HasPipelineForEvent 方法正確"
            ));
        }

        delete explorer;
        delete registry;
        delete manager;
        delete container;
        delete pipeline;
    }

    // 測試統計方法
    void TestStatisticMethods()
    {
        Print("--- 測試 TradingPipelineExplorer 統計方法 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("StatManager");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "StatRegistry");
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(registry, "StatExplorer");

        // 創建容器和流水線
        TradingPipelineContainer* container1 = new TradingPipelineContainer("StatContainer1");
        TradingPipelineContainer* container2 = new TradingPipelineContainer("StatContainer2");
        MockTradingPipeline* pipeline1 = new MockTradingPipeline("StatPipeline1");
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("StatPipeline2");

        container1.AddPipeline(pipeline1);
        container2.AddPipeline(pipeline2);

        registry.Register(TRADING_INIT, container1);
        registry.Register(TRADING_TICK, container2);

        // 測試 GetPipelineCountByEvent
        int initCount = explorer.GetPipelineCountByEvent(TRADING_INIT);
        int tickCount = explorer.GetPipelineCountByEvent(TRADING_TICK);
        int deinitCount = explorer.GetPipelineCountByEvent(TRADING_DEINIT);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineExplorer::TestStatisticMethods - GetPipelineCountByEvent",
                initCount == 2 && tickCount == 2 && deinitCount == 0,
                StringFormat("事件流水線數量正確: INIT=%d, TICK=%d, DEINIT=%d", initCount, tickCount, deinitCount)
            ));
        }

        // 測試 GetTotalPipelineCount
        int totalCount = explorer.GetTotalPipelineCount();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineExplorer::TestStatisticMethods - GetTotalPipelineCount",
                totalCount == 4,  // 2 containers + 2 pipelines
                StringFormat("總流水線數量正確: %d", totalCount)
            ));
        }

        delete explorer;
        delete registry;
        delete manager;
        delete container1;
        delete container2;
        delete pipeline1;
        delete pipeline2;
    }

    // 測試報告生成
    void TestReportGeneration()
    {
        Print("--- 測試 TradingPipelineExplorer 報告生成 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("ReportManager");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "ReportRegistry");
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(registry, "ReportExplorer");

        // 創建容器
        TradingPipelineContainer* container = new TradingPipelineContainer("ReportContainer");
        registry.Register(TRADING_INIT, container);

        // 測試生成探索報告
        string explorationReport = explorer.GenerateExplorationReport();
        bool hasExplorationContent = StringFind(explorationReport, "交易流水線探索報告") >= 0;

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineExplorer::TestReportGeneration - GenerateExplorationReport",
                hasExplorationContent,
                "探索報告生成成功"
            ));
        }

        // 測試生成階段報告
        string stageReport = explorer.GenerateStageReport();
        bool hasStageContent = StringFind(stageReport, "交易階段映射報告") >= 0;

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineExplorer::TestReportGeneration - GenerateStageReport",
                hasStageContent,
                "階段報告生成成功"
            ));
        }

        delete explorer;
        delete registry;
        delete manager;
        delete container;
    }
};
