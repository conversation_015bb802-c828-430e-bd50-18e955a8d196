# PipelineAdvance_v1 修改指南

## 📋 修改概述

本次修改對 PipelineAdvance_v1 模組進行了以下重要更新：

### 1. TradingPipeline 增強

- ✅ 添加 `IPipelineManager* m_manager` 成員
- ✅ 添加 `ENUM_TRADING_STAGE m_stage` 成員
- ✅ 修改建構子支援新參數
- ✅ 添加 `GetStage()`, `GetManager()`, `SetManager()` 方法

### 2. 簡化 Manager 架構

- ✅ 移除 `IPipelineManager` 介面（簡化設計）
- ✅ 實現 `PipelineGroupManager` 獨立類別
- ✅ 支援管理最多 3 個 `PipelineGroup`

### 3. CompositePipeline 重構

- ✅ 改為直接繼承 `ITradingPipeline`
- ✅ 重新實現所有介面方法
- ✅ 添加必要的基本成員變數

## 🚀 使用方法

### 基本使用示例

```mql4
// 1. 創建管理器
PipelineGroupManager* manager = new PipelineGroupManager("主管理器");

// 2. 創建具體流水線（帶有階段和管理器）
class DataFeedPipeline : public TradingPipeline
{
public:
    DataFeedPipeline(IPipelineManager* manager)
        : TradingPipeline("數據饋送", "DataFeed", TICK_DATA_FEED, manager) {}

protected:
    void Main() override
    {
        Print("執行數據饋送，階段：", EnumToString(GetStage()));
    }
};

// 3. 創建流水線實例
DataFeedPipeline* dataFeed = new DataFeedPipeline(manager);
SignalPipeline* signal = new SignalPipeline(manager);

// 4. 創建複合流水線
CompositePipeline* composite = new CompositePipeline("主流水線");
composite.AddPipeline(dataFeed);
composite.AddPipeline(signal);

// 5. 創建流水線組
PipelineGroup* tickGroup = new PipelineGroup("Tick處理組",
                                            "處理每個Tick",
                                            TRADING_TICK);
tickGroup.AddPipeline(composite);

// 6. 添加到管理器
manager.AddGroup(tickGroup);

// 7. 執行指定事件類型的流水線
manager.Execute(TRADING_TICK);
```

## 🔧 新增功能詳解

### PipelineGroupManager 類別（簡化版）

```mql4
class PipelineGroupManager
{
private:
    PipelineGroup* m_group1;    // 第一個流水線組
    PipelineGroup* m_group2;    // 第二個流水線組
    PipelineGroup* m_group3;    // 第三個流水線組

public:
    bool AddGroup(PipelineGroup* group);        // 添加流水線組（最多3個）
    bool RemoveGroup(PipelineGroup* group);     // 移除流水線組
    void Execute(ENUM_TRADING_EVENT eventType); // 執行指定事件類型的組
    void RestoreAll();                          // 重置所有組
    void Restore(ENUM_TRADING_EVENT eventType); // 重置指定事件類型的組
    PipelineGroup* FindGroupByName(string name); // 按名稱查找組
    int GetGroupCount();                        // 獲取組數量（0-3）
    int GetMaxGroups();                         // 獲取最大組數量（固定為3）
    PipelineGroup* GetGroup(int index);         // 按索引獲取組
    bool HasEmptySlot();                        // 檢查是否有空位置
    // ... 其他方法
};
```

### TradingPipeline 新建構子

```mql4
TradingPipeline(string name = "",
               string type = "TradingPipeline",
               ENUM_TRADING_STAGE stage = INIT_START,
               PipelineGroupManager* manager = NULL)
```

### ENUM_TRADING_STAGE 階段

```mql4
enum ENUM_TRADING_STAGE
{
    // 初始化階段
    INIT_START,
    INIT_PARAMETERS,
    INIT_VARIABLES,
    INIT_ENVIRONMENT,
    INIT_INDICATORS,
    INIT_COMPLETE,

    // 交易階段
    TICK_DATA_FEED,
    TICK_SIGNAL_ANALYSIS,
    TICK_ORDER_MANAGEMENT,
    TICK_RISK_CONTROL,
    TICK_LOGGING,

    // 清理階段
    DEINIT_CLEANUP,
    DEINIT_SAVE_STATE,
    DEINIT_COMPLETE
};
```

## 📊 架構變化

### 修改前

```
ITradingPipeline
    ↑
TradingPipeline
    ↑
CompositePipeline
```

### 修改後

```
ITradingPipeline
    ↑                    ↑
TradingPipeline    CompositePipeline
    ↑
具體流水線實現

PipelineGroupManager (獨立類別)
    ↓ 管理 (最多3個)
PipelineGroup
    ↓ 包含
CompositePipeline
```

## 🧪 測試

運行測試文件驗證修改：

```mql4
#include "test/TestModifications.mq4"
```

測試涵蓋：

- ✅ Manager 創建和管理（最多 3 個組）
- ✅ TradingPipeline 新參數
- ✅ CompositePipeline 新繼承關係
- ✅ 按事件類型執行流水線（Execute(ENUM_TRADING_EVENT)）
- ✅ 按事件類型重置流水線（Restore(ENUM_TRADING_EVENT)）
- ✅ 完整的執行流程
- ✅ 重置功能
- ✅ 3 個組的限制測試
- ✅ 按索引獲取組功能

## 🔄 向後兼容性

### 保持兼容的部分

- ✅ ITradingPipeline 介面未變
- ✅ PipelineGroup 基本功能未變
- ✅ CompositePipeline 公共方法未變

### 需要更新的部分

- ⚠️ TradingPipeline 子類需要更新建構子（IPipelineManager* 改為 PipelineGroupManager*）
- ⚠️ 直接使用 CompositePipeline 繼承的代碼需要檢查
- ⚠️ 移除了 IPipelineManager 介面，改為直接使用 PipelineGroupManager
- ⚠️ PipelineGroupManager 現在最多只能管理 3 個組
- ⚠️ ExecuteAll() 方法改為 Execute(ENUM_TRADING_EVENT)
- ⚠️ 移除了 TradingPipeline 的 SetManager() 方法
- ⚠️ 新增了 Restore(ENUM_TRADING_EVENT) 方法

## 📝 最佳實踐

1. **使用 Manager 模式**：通過 PipelineGroupManager 統一管理流水線組（最多 3 個）
2. **明確階段定義**：為每個流水線指定合適的 ENUM_TRADING_STAGE
3. **資源管理**：使用 owned 參數正確管理物件生命週期
4. **錯誤處理**：檢查 AddGroup, AddPipeline 等方法的返回值
5. **組數量限制**：注意 PipelineGroupManager 最多只能管理 3 個組
6. **索引訪問**：使用 GetGroup(index) 方法按索引訪問組（0-2）
7. **事件類型執行**：使用 Execute(ENUM_TRADING_EVENT) 按事件類型執行流水線
8. **事件類型重置**：使用 Restore(ENUM_TRADING_EVENT) 按事件類型重置流水線

## 🚨 注意事項

- Manager 指標可以為 NULL，但建議總是提供有效的管理器
- CompositePipeline 現在不再有 Manager 和 Stage，這些由其子流水線提供
- **重要**：PipelineGroupManager 最多只能管理 3 個 PipelineGroup
- 移除了 IPipelineManager 介面，直接使用 PipelineGroupManager 類別
- 確保正確的 #include 順序避免編譯錯誤
- 使用前向聲明避免循環依賴
- 當嘗試添加第 4 個組時，AddGroup() 會返回 false
- **重要**：ExecuteAll() 已改為 Execute(ENUM_TRADING_EVENT)，需要指定事件類型
- **重要**：移除了 SetManager() 方法，管理器只能在建構時設定
- 新增的 Restore(ENUM_TRADING_EVENT) 只重置指定事件類型的組
