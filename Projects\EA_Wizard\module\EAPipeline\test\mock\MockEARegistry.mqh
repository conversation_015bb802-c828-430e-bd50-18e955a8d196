//+------------------------------------------------------------------+
//|                                              MockEARegistry.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../../../RegistryAdvance/interface/IRegistry.mqh"
#include "../../../RegistryAdvance/model/RegistryResult.mqh"
#include "../../../RegistryAdvance/model/RegistryItem.mqh"

//+------------------------------------------------------------------+
//| Mock EA註冊器類別 - 用於測試的模擬EA註冊器                        |
//+------------------------------------------------------------------+
template<typename T>
class MockEARegistry : public IRegistry<T>
{
private:
    string m_name;                    // 註冊器名稱
    string m_type;                    // 註冊器類型
    int m_maxItems;                   // 最大項目數量
    int m_currentCount;               // 當前項目數量
    bool m_shouldSucceed;             // 是否應該成功
    string m_customMessage;           // 自定義消息
    int m_registerCallCount;          // Register方法調用次數
    int m_getItemCallCount;           // GetItem方法調用次數
    
    // 模擬存儲的項目
    string m_storedKeys[100];         // 存儲的鍵
    T m_storedValues[100];            // 存儲的值
    string m_storedDescriptions[100]; // 存儲的描述
    
public:
    // 構造函數
    MockEARegistry(string name, string type = "MockEARegistry", int maxItems = 100, bool shouldSucceed = true)
    : m_name(name), m_type(type), m_maxItems(maxItems), m_currentCount(0), 
      m_shouldSucceed(shouldSucceed), m_customMessage(""), 
      m_registerCallCount(0), m_getItemCallCount(0)
    {
        // 初始化數組
        ArrayInitialize(m_storedKeys, "");
        ArrayInitialize(m_storedDescriptions, "");
    }
    
    // 析構函數
    virtual ~MockEARegistry() {}
    
    // 註冊項目
    virtual RegistryResult<string>* Register(string name, string description, T value) override
    {
        m_registerCallCount++;
        
        if(!m_shouldSucceed)
        {
            string message = (m_customMessage != "") ? m_customMessage : "模擬註冊失敗";
            return new RegistryResult<string>("", false, message);
        }
        
        if(m_currentCount >= m_maxItems)
        {
            return new RegistryResult<string>("", false, "已達到最大項目數量");
        }
        
        // 檢查是否已存在
        for(int i = 0; i < m_currentCount; i++)
        {
            if(m_storedKeys[i] == name)
            {
                return new RegistryResult<string>("", false, "鍵已存在");
            }
        }
        
        // 添加新項目
        string key = StringFormat("key_%s_%d", name, m_currentCount);
        m_storedKeys[m_currentCount] = key;
        m_storedValues[m_currentCount] = value;
        m_storedDescriptions[m_currentCount] = description;
        m_currentCount++;
        
        return new RegistryResult<string>(key, true, "註冊成功");
    }
    
    // 獲取項目
    virtual RegistryItem<T>* GetItem(string key) override
    {
        m_getItemCallCount++;
        
        // 查找項目
        for(int i = 0; i < m_currentCount; i++)
        {
            if(m_storedKeys[i] == key)
            {
                return new RegistryItem<T>(key, m_storedDescriptions[i], m_storedValues[i]);
            }
        }
        
        return NULL; // 未找到
    }
    
    // 獲取註冊器名稱
    virtual string GetName() override { return m_name; }
    
    // 獲取註冊器類型
    virtual string GetType() override { return m_type; }
    
    // 獲取最大項目數量
    virtual int GetMaxItems() override { return m_maxItems; }
    
    // 獲取當前項目數量
    virtual int GetCount() override { return m_currentCount; }
    
    // === Mock特有的方法 ===
    
    // 設置是否應該成功
    void SetShouldSucceed(bool shouldSucceed) { m_shouldSucceed = shouldSucceed; }
    
    // 設置自定義消息
    void SetCustomMessage(string message) { m_customMessage = message; }
    
    // 獲取Register方法調用次數
    int GetRegisterCallCount() const { return m_registerCallCount; }
    
    // 獲取GetItem方法調用次數
    int GetGetItemCallCount() const { return m_getItemCallCount; }
    
    // 重置調用計數
    void ResetCallCounts()
    {
        m_registerCallCount = 0;
        m_getItemCallCount = 0;
    }
    
    // 清空所有項目
    void Clear()
    {
        m_currentCount = 0;
        ArrayInitialize(m_storedKeys, "");
        ArrayInitialize(m_storedDescriptions, "");
    }
    
    // 檢查是否包含指定鍵
    bool ContainsKey(string key)
    {
        for(int i = 0; i < m_currentCount; i++)
        {
            if(m_storedKeys[i] == key)
                return true;
        }
        return false;
    }
    
    // 獲取所有鍵
    int GetAllKeys(string &keys[])
    {
        ArrayResize(keys, m_currentCount);
        for(int i = 0; i < m_currentCount; i++)
        {
            keys[i] = m_storedKeys[i];
        }
        return m_currentCount;
    }
    
    // 模擬滿容量狀態
    void SimulateFullCapacity()
    {
        m_currentCount = m_maxItems;
    }
    
    // 模擬空狀態
    void SimulateEmpty()
    {
        m_currentCount = 0;
    }
};

//+------------------------------------------------------------------+
//| Mock EA註冊器工廠 - 創建各種類型的Mock EA註冊器                   |
//+------------------------------------------------------------------+
class MockEARegistryFactory
{
public:
    // 創建成功的Mock EA註冊器
    template<typename T>
    static MockEARegistry<T>* CreateSuccessfulRegistry(string name, int maxItems = 100)
    {
        return new MockEARegistry<T>(name, "SuccessfulMockRegistry", maxItems, true);
    }
    
    // 創建失敗的Mock EA註冊器
    template<typename T>
    static MockEARegistry<T>* CreateFailedRegistry(string name, int maxItems = 100)
    {
        return new MockEARegistry<T>(name, "FailedMockRegistry", maxItems, false);
    }
    
    // 創建滿容量的Mock EA註冊器
    template<typename T>
    static MockEARegistry<T>* CreateFullRegistry(string name, int maxItems = 10)
    {
        MockEARegistry<T>* registry = new MockEARegistry<T>(name, "FullMockRegistry", maxItems, true);
        registry.SimulateFullCapacity();
        return registry;
    }
    
    // 創建空的Mock EA註冊器
    template<typename T>
    static MockEARegistry<T>* CreateEmptyRegistry(string name, int maxItems = 100)
    {
        MockEARegistry<T>* registry = new MockEARegistry<T>(name, "EmptyMockRegistry", maxItems, true);
        registry.SimulateEmpty();
        return registry;
    }
};
