//+------------------------------------------------------------------+
//|                                           TestRunnerAdvanced.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "TestFramework.mqh"

//+------------------------------------------------------------------+
//| 測試結果詳細記錄 - 擴展的測試結果類別                             |
//+------------------------------------------------------------------+
class TestResultDetail
{
private:
    string m_testName;           // 測試名稱
    string m_testClass;          // 測試類別
    bool m_passed;               // 是否通過
    string m_message;            // 測試消息
    datetime m_startTime;        // 開始時間
    datetime m_endTime;          // 結束時間
    int m_executionTimeMs;       // 執行時間（毫秒）

public:
    // 構造函數
    TestResultDetail(string testName, string testClass, bool passed,
                    string message = "", datetime startTime = 0, datetime endTime = 0)
    : m_testName(testName), m_testClass(testClass), m_passed(passed),
      m_message(message), m_startTime(startTime), m_endTime(endTime)
    {
        m_executionTimeMs = (int)((m_endTime - m_startTime) * 1000);
    }

    // 獲取方法
    string GetTestName() const { return m_testName; }
    string GetTestClass() const { return m_testClass; }
    bool IsPassed() const { return m_passed; }
    string GetMessage() const { return m_message; }
    datetime GetStartTime() const { return m_startTime; }
    datetime GetEndTime() const { return m_endTime; }
    int GetExecutionTimeMs() const { return m_executionTimeMs; }

    // 格式化輸出
    string ToString() const
    {
        string status = m_passed ? "[通過]" : "[失敗]";
        string timeInfo = StringFormat("(%dms)", m_executionTimeMs);

        if(m_message != "")
            return StringFormat("%s %s::%s %s - %s", status, m_testClass, m_testName, timeInfo, m_message);
        else
            return StringFormat("%s %s::%s %s", status, m_testClass, m_testName, timeInfo);
    }
};

//+------------------------------------------------------------------+
//| 增強版測試運行器 - 支持詳細結果收集和文檔輸出                     |
//+------------------------------------------------------------------+
class TestRunnerAdvanced : public TestRunner
{
private:
    TestResultDetail* m_results[];   // 詳細測試結果數組
    int m_resultCount;               // 結果數量
    string m_currentTestClass;       // 當前測試類別
    datetime m_testStartTime;        // 測試開始時間
    bool m_collectDetails;           // 是否收集詳細信息

public:
    // 構造函數
    TestRunnerAdvanced(bool collectDetails = true)
    : TestRunner(), m_resultCount(0), m_currentTestClass(""),
      m_testStartTime(0), m_collectDetails(collectDetails)
    {
        ArrayResize(m_results, 0);
    }

    // 析構函數
    ~TestRunnerAdvanced()
    {
        ClearResults();
    }

    // 重寫記錄測試結果方法
    virtual void RecordResult(TestResult* result)
    {
        if(result == NULL) return;

        datetime endTime = TimeCurrent();

        // 先保存需要的信息（在基類刪除 result 之前）
        string testName = "";
        bool passed = false;
        string message = "";

        if(m_collectDetails)
        {
            testName = result.GetTestName();
            passed = result.IsPassed();
            message = result.GetMessage();
        }

        // 調用基類方法進行基本記錄（這會刪除 result）
        TestRunner::RecordResult(result);

        // 如果需要收集詳細信息，則使用保存的信息記錄詳細結果
        if(m_collectDetails)
        {
            TestResultDetail* detail = new TestResultDetail(
                testName,
                m_currentTestClass,
                passed,
                message,
                m_testStartTime,
                endTime
            );

            AddResultDetail(detail);
        }

        // 重置測試開始時間
        m_testStartTime = TimeCurrent();
    }

    // 重寫運行測試類別方法
    virtual void RunTestCase(TestCase* testCase)
    {
        if(testCase == NULL) return;

        m_currentTestClass = testCase.GetClassName();
        m_testStartTime = TimeCurrent();

        // 調用基類方法
        TestRunner::RunTestCase(testCase);
    }

    // 獲取詳細結果數量
    int GetResultCount() const { return m_resultCount; }

    // 獲取指定索引的詳細結果
    TestResultDetail* GetResult(int index) const
    {
        if(index >= 0 && index < m_resultCount)
            return m_results[index];
        return NULL;
    }

    // 獲取失敗的測試結果
    void GetFailedResults(TestResultDetail* &failedResults[], int &count)
    {
        count = 0;
        ArrayResize(failedResults, 0);

        for(int i = 0; i < m_resultCount; i++)
        {
            if(m_results[i] != NULL && !m_results[i].IsPassed())
            {
                ArrayResize(failedResults, count + 1);
                failedResults[count] = m_results[i];
                count++;
            }
        }
    }

    // 獲取通過的測試結果
    void GetPassedResults(TestResultDetail* &passedResults[], int &count)
    {
        count = 0;
        ArrayResize(passedResults, 0);

        for(int i = 0; i < m_resultCount; i++)
        {
            if(m_results[i] != NULL && m_results[i].IsPassed())
            {
                ArrayResize(passedResults, count + 1);
                passedResults[count] = m_results[i];
                count++;
            }
        }
    }

    // 按測試類別獲取結果
    void GetResultsByClass(string className, TestResultDetail* &classResults[], int &count)
    {
        count = 0;
        ArrayResize(classResults, 0);

        for(int i = 0; i < m_resultCount; i++)
        {
            if(m_results[i] != NULL && m_results[i].GetTestClass() == className)
            {
                ArrayResize(classResults, count + 1);
                classResults[count] = m_results[i];
                count++;
            }
        }
    }

    // 打印詳細結果
    void PrintDetailedResults()
    {
        Print("=== 詳細測試結果 ===");

        for(int i = 0; i < m_resultCount; i++)
        {
            if(m_results[i] != NULL)
            {
                Print(m_results[i].ToString());
            }
        }

        Print("=== 詳細結果結束 ===");
    }

    // 獲取執行時間統計
    void GetExecutionStats(int &totalTimeMs, int &avgTimeMs, int &maxTimeMs, int &minTimeMs)
    {
        totalTimeMs = 0;
        avgTimeMs = 0;
        maxTimeMs = 0;
        minTimeMs = INT_MAX;

        if(m_resultCount == 0)
        {
            minTimeMs = 0;
            return;
        }

        for(int i = 0; i < m_resultCount; i++)
        {
            if(m_results[i] != NULL)
            {
                int execTime = m_results[i].GetExecutionTimeMs();
                totalTimeMs += execTime;

                if(execTime > maxTimeMs) maxTimeMs = execTime;
                if(execTime < minTimeMs) minTimeMs = execTime;
            }
        }

        avgTimeMs = m_resultCount > 0 ? totalTimeMs / m_resultCount : 0;
    }

    // 設置是否收集詳細信息
    void SetCollectDetails(bool collect) { m_collectDetails = collect; }

    // 清除所有結果
    void ClearResults()
    {
        for(int i = 0; i < m_resultCount; i++)
        {
            if(m_results[i] != NULL)
            {
                delete m_results[i];
                m_results[i] = NULL;
            }
        }
        ArrayResize(m_results, 0);
        m_resultCount = 0;
    }

private:
    // 添加詳細結果
    void AddResultDetail(TestResultDetail* detail)
    {
        if(detail == NULL) return;

        ArrayResize(m_results, m_resultCount + 1);
        m_results[m_resultCount] = detail;
        m_resultCount++;
    }
};
