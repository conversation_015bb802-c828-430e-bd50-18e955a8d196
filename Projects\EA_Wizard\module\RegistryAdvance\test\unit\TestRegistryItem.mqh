//+------------------------------------------------------------------+
//|                                           TestRegistryItem.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../model/RegistryItem.mqh"

//+------------------------------------------------------------------+
//| RegistryItem測試類別                                             |
//+------------------------------------------------------------------+
class TestRegistryItem : public TestCase
{
private:
    TestRunner* m_runner;    // 測試運行器引用
    
public:
    // 構造函數
    TestRegistryItem(TestRunner* runner) 
    : TestCase("TestRegistryItem"), m_runner(runner) {}
    
    // 運行所有測試
    void RunTests() override
    {
        TestConstructor();
        TestGetters();
        TestToString();
        TestDifferentTypes();
        TestTimeStamps();
        TestEmptyValues();
        TestLongValues();
    }
    
private:
    // 測試構造函數
    void TestConstructor()
    {
        SetUp();
        
        // 測試字符串值的構造
        RegistryItem<string>* stringItem = new RegistryItem<string>("id1", "測試項目", "這是一個測試項目", "測試值", "string");
        m_runner.RecordResult(Assert::AssertNotNull("構造函數_字符串項目不為空", stringItem));
        
        // 測試整數值的構造
        RegistryItem<int>* intItem = new RegistryItem<int>("id2", "整數項目", "這是一個整數項目", 42, "int");
        m_runner.RecordResult(Assert::AssertNotNull("構造函數_整數項目不為空", intItem));
        
        // 測試雙精度值的構造
        RegistryItem<double>* doubleItem = new RegistryItem<double>("id3", "雙精度項目", "這是一個雙精度項目", 3.14159, "double");
        m_runner.RecordResult(Assert::AssertNotNull("構造函數_雙精度項目不為空", doubleItem));
        
        // 清理
        delete stringItem;
        delete intItem;
        delete doubleItem;
        
        TearDown();
    }
    
    // 測試Getter方法
    void TestGetters()
    {
        SetUp();
        
        string testId = "test_id_123";
        string testName = "測試項目名稱";
        string testDescription = "這是一個詳細的測試項目描述";
        string testValue = "測試值內容";
        string testType = "string";
        
        RegistryItem<string>* item = new RegistryItem<string>(testId, testName, testDescription, testValue, testType);
        
        // 測試GetId
        m_runner.RecordResult(Assert::AssertEquals("Getter_ID", testId, item.GetId()));
        
        // 測試GetName
        m_runner.RecordResult(Assert::AssertEquals("Getter_名稱", testName, item.GetName()));
        
        // 測試GetDescription
        m_runner.RecordResult(Assert::AssertEquals("Getter_描述", testDescription, item.GetDescription()));
        
        // 測試GetValue
        m_runner.RecordResult(Assert::AssertEquals("Getter_值", testValue, item.GetValue()));
        
        // 測試GetType
        m_runner.RecordResult(Assert::AssertEquals("Getter_類型", testType, item.GetType()));
        
        // 測試GetCreateTime（應該不為0）
        datetime createTime = item.GetCreateTime();
        m_runner.RecordResult(Assert::AssertTrue("Getter_創建時間不為零", createTime > 0));
        
        delete item;
        
        TearDown();
    }
    
    // 測試ToString方法
    void TestToString()
    {
        SetUp();
        
        RegistryItem<string>* item = new RegistryItem<string>("id123", "測試項目", "測試描述", "測試值", "string");
        
        string toString = item.ToString();
        
        // 檢查ToString結果包含所有關鍵信息
        m_runner.RecordResult(Assert::AssertContains("ToString_包含ID", toString, "id123"));
        m_runner.RecordResult(Assert::AssertContains("ToString_包含名稱", toString, "測試項目"));
        m_runner.RecordResult(Assert::AssertContains("ToString_包含描述", toString, "測試描述"));
        m_runner.RecordResult(Assert::AssertContains("ToString_包含類型", toString, "string"));
        m_runner.RecordResult(Assert::AssertContains("ToString_包含ID標籤", toString, "ID:"));
        m_runner.RecordResult(Assert::AssertContains("ToString_包含Name標籤", toString, "Name:"));
        
        delete item;
        
        TearDown();
    }
    
    // 測試不同數據類型
    void TestDifferentTypes()
    {
        SetUp();
        
        // 測試整數類型
        RegistryItem<int>* intItem = new RegistryItem<int>("int_id", "整數項目", "整數描述", 12345, "int");
        m_runner.RecordResult(Assert::AssertEquals("不同類型_整數值", 12345, intItem.GetValue()));
        
        // 測試雙精度類型
        RegistryItem<double>* doubleItem = new RegistryItem<double>("double_id", "雙精度項目", "雙精度描述", 123.456, "double");
        m_runner.RecordResult(Assert::AssertTrue("不同類型_雙精度值", 
            MathAbs(doubleItem.GetValue() - 123.456) < 0.001));
        
        // 測試布爾類型
        RegistryItem<bool>* boolItem = new RegistryItem<bool>("bool_id", "布爾項目", "布爾描述", true, "bool");
        m_runner.RecordResult(Assert::AssertEquals("不同類型_布爾值", true, boolItem.GetValue()));
        
        // 清理
        delete intItem;
        delete doubleItem;
        delete boolItem;
        
        TearDown();
    }
    
    // 測試時間戳
    void TestTimeStamps()
    {
        SetUp();
        
        datetime beforeCreate = TimeCurrent();
        Sleep(1000); // 等待1秒確保時間差異
        
        RegistryItem<string>* item = new RegistryItem<string>("time_id", "時間測試", "時間測試描述", "值", "string");
        
        datetime afterCreate = TimeCurrent();
        datetime createTime = item.GetCreateTime();
        
        // 檢查創建時間在合理範圍內
        m_runner.RecordResult(Assert::AssertTrue("時間戳_創建時間大於等於開始時間", createTime >= beforeCreate));
        m_runner.RecordResult(Assert::AssertTrue("時間戳_創建時間小於等於結束時間", createTime <= afterCreate));
        
        delete item;
        
        TearDown();
    }
    
    // 測試空值和邊界情況
    void TestEmptyValues()
    {
        SetUp();
        
        // 測試空字符串
        RegistryItem<string>* emptyStringItem = new RegistryItem<string>("", "", "", "", "");
        m_runner.RecordResult(Assert::AssertEquals("空值_空ID", "", emptyStringItem.GetId()));
        m_runner.RecordResult(Assert::AssertEquals("空值_空名稱", "", emptyStringItem.GetName()));
        m_runner.RecordResult(Assert::AssertEquals("空值_空描述", "", emptyStringItem.GetDescription()));
        m_runner.RecordResult(Assert::AssertEquals("空值_空值", "", emptyStringItem.GetValue()));
        m_runner.RecordResult(Assert::AssertEquals("空值_空類型", "", emptyStringItem.GetType()));
        
        // 測試零值
        RegistryItem<int>* zeroItem = new RegistryItem<int>("zero_id", "零值項目", "零值描述", 0, "int");
        m_runner.RecordResult(Assert::AssertEquals("空值_零值", 0, zeroItem.GetValue()));
        
        // 清理
        delete emptyStringItem;
        delete zeroItem;
        
        TearDown();
    }
    
    // 測試長值
    void TestLongValues()
    {
        SetUp();
        
        // 創建長字符串
        string longId = "這是一個非常長的ID字符串，用來測試系統對長字符串的處理能力，包含中文字符和特殊符號!@#$%^&*()";
        string longName = "這是一個非常長的名稱，包含了很多中文字符，用來測試系統的字符串處理能力和邊界情況";
        string longDescription = "這是一個非常詳細和冗長的描述，包含了大量的文字內容，用來測試系統對長文本的處理能力，包括存儲、檢索和顯示等各個方面的功能";
        string longValue = "這是一個非常長的值，包含了大量的數據內容，用來測試系統對大數據量的處理能力";
        string longType = "這是一個長類型名稱";
        
        RegistryItem<string>* longItem = new RegistryItem<string>(longId, longName, longDescription, longValue, longType);
        
        // 驗證長值能正確存儲和檢索
        m_runner.RecordResult(Assert::AssertEquals("長值_長ID", longId, longItem.GetId()));
        m_runner.RecordResult(Assert::AssertEquals("長值_長名稱", longName, longItem.GetName()));
        m_runner.RecordResult(Assert::AssertEquals("長值_長描述", longDescription, longItem.GetDescription()));
        m_runner.RecordResult(Assert::AssertEquals("長值_長值", longValue, longItem.GetValue()));
        m_runner.RecordResult(Assert::AssertEquals("長值_長類型", longType, longItem.GetType()));
        
        // 測試ToString對長值的處理
        string longToString = longItem.ToString();
        m_runner.RecordResult(Assert::AssertContains("長值_ToString包含長ID", longToString, longId));
        m_runner.RecordResult(Assert::AssertContains("長值_ToString包含長名稱", longToString, longName));
        
        delete longItem;
        
        TearDown();
    }
};
