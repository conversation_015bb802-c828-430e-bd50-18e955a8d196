//+------------------------------------------------------------------+
//|                                         EABuiltPipelineItem.mqh |
//|                                                       EA_Wizard |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../../PipelineAdvance/interface/IPipeline.mqh" // 包含接口類型
#include "../components/EACompoundPipeline.mqh" // 包含組合流水線類型

//+------------------------------------------------------------------+
//| 已構建流水線項目基本結構 - 實現接口                                |
//+------------------------------------------------------------------+
class EABuiltPipelineItem
{
private:
    string m_name;  // 項目名稱
    string m_type;  // 項目類型
    string m_message[]; // 建立消息數組
    IPipeline* m_value;    // 項目值
    bool m_isBuilt; // 是否已建立
    bool m_isCompletedBuilt; // 是否已完成建立

public:
    // 構造函數
    EABuiltPipelineItem(string name, string type, string &message[], IPipeline* value, bool isBuilt = true, bool isCompletedBuilt = true)
        : m_name(name),
          m_type(type),
          m_value(value),
          m_isBuilt(isBuilt),
          m_isCompletedBuilt(isCompletedBuilt)
    {
        ArrayCopy(m_message, message);
    }
    // 構造函數
    EABuiltPipelineItem(string name, string type, string message, IPipeline* value, bool isBuilt = true, bool isCompletedBuilt = true)
        : m_name(name),
          m_type(type),
          m_value(value),
          m_isBuilt(isBuilt),
          m_isCompletedBuilt(isCompletedBuilt)
    {
        ArrayResize(m_message, 1);
        m_message[0] = message;
    }

    // 析構函數
    ~EABuiltPipelineItem()
    {
        // 不需要釋放資源
    }

    // 獲取項目名稱
    string GetName() const { return m_name; }

    // 獲取項目值
    IPipeline* GetValue() const { return m_value; }

    // 獲取項目類型
    string GetType() const { return m_type; }
    
    // 獲取建立消息
    string GetMessage() const { return FormatMessage(); }

    // 獲取建立消息
    int GetMessage(string &message[]) const { return ArrayCopy(message, m_message); }

    // 獲取是否已建立
    bool IsBuilt() const { return m_isBuilt; }

    // 獲取是否已完成建立
    bool IsCompletedBuilt() const { return m_isCompletedBuilt; }

    // 轉換為字符串（用於調試）
    string ToString() const
    {
        return StringFormat("Name: %s, Type: %s, Message: %s, IsBuilt: %s, IsCompletedBuilt: %s, Value: %s", m_name, m_type, FormatMessage(), m_isBuilt, m_isCompletedBuilt, m_value);
    }

protected:
    string FormatMessage() const
    {
        string result = "";
        for(int i = 0; i < ArraySize(m_message); i++)
        {
            result += m_message[i] + "\n";
        }
        return result;
    }

};
