//+------------------------------------------------------------------+
//|                                           EAPipelineSimple.mq4 |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

// 包含基本測試框架
#include "../../Projects/EA_Wizard/module/EAPipeline/test/TestFramework.mqh"
#include "../../Projects/EA_Wizard/module/EAPipeline/components/EACompoundPipeline.mqh"
#include "../../Projects/EA_Wizard/module/EAPipeline/test/mock/MockEAPipeline.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== EAPipeline 簡單測試開始 ===");
    
    // 測試基本功能
    TestBasicFunctionality();
    
    Print("=== EAPipeline 簡單測試完成 ===");
}

//+------------------------------------------------------------------+
//| 測試基本功能                                                     |
//+------------------------------------------------------------------+
void TestBasicFunctionality()
{
    Print("開始基本功能測試...");
    
    // 測試 TestFramework
    TestResult* result1 = Assert::AssertTrue("基本測試_真值", true);
    if(result1 != NULL)
    {
        Print(StringFormat("測試結果: %s - %s", 
                          result1.IsPassed() ? "通過" : "失敗", 
                          result1.GetTestName()));
        delete result1;
    }
    
    // 測試 EACompoundPipeline 構造函數
    EACompoundPipeline* pipeline = new EACompoundPipeline("TestPipeline");
    if(pipeline != NULL)
    {
        Print("EACompoundPipeline 創建成功");
        Print(StringFormat("流水線名稱: %s", pipeline.GetName()));
        Print(StringFormat("流水線類型: %s", pipeline.GetType()));
        Print(StringFormat("初始數量: %d", pipeline.GetCount()));
        
        delete pipeline;
    }
    else
    {
        Print("EACompoundPipeline 創建失敗");
    }
    
    // 測試 MockEAPipeline
    MockEAPipeline* mockPipeline = MockEAPipelineFactory::CreateSuccessfulPipeline("MockTest");
    if(mockPipeline != NULL)
    {
        Print("MockEAPipeline 創建成功");
        Print(StringFormat("Mock流水線名稱: %s", mockPipeline.GetName()));
        Print(StringFormat("Mock流水線類型: %s", mockPipeline.GetType()));
        Print(StringFormat("是否已執行: %s", mockPipeline.IsExecuted() ? "是" : "否"));
        
        // 執行 Mock 流水線
        mockPipeline.Execute();
        Print(StringFormat("執行後狀態: %s", mockPipeline.IsExecuted() ? "已執行" : "未執行"));
        
        PipelineResult* result = mockPipeline.GetResult();
        if(result != NULL)
        {
            Print(StringFormat("執行結果: %s - %s", 
                              result.IsSuccess() ? "成功" : "失敗",
                              result.GetMessage()));
        }
        
        delete mockPipeline;
    }
    else
    {
        Print("MockEAPipeline 創建失敗");
    }
    
    // 測試 EAPipelineManager
    EAPipelineManager* manager = EAPipelineManager::GetInstance();
    if(manager != NULL)
    {
        Print("EAPipelineManager 獲取成功");
        Print(StringFormat("初始流水線數量: %d", manager.GetPipelineCount()));
        
        // 創建並添加一個流水線
        MockEAPipeline* testPipeline = MockEAPipelineFactory::CreateSuccessfulPipeline("ManagerTest");
        if(testPipeline != NULL)
        {
            PipelineResult* addResult = manager.AddPipeline(testPipeline);
            if(addResult != NULL)
            {
                Print(StringFormat("添加流水線結果: %s - %s", 
                                  addResult.IsSuccess() ? "成功" : "失敗",
                                  addResult.GetMessage()));
                delete addResult;
                
                Print(StringFormat("添加後流水線數量: %d", manager.GetPipelineCount()));
                
                // 執行流水線
                PipelineResult* execResult = manager.ExecutePipeline("ManagerTest");
                if(execResult != NULL)
                {
                    Print(StringFormat("執行流水線結果: %s - %s", 
                                      execResult.IsSuccess() ? "成功" : "失敗",
                                      execResult.GetMessage()));
                    delete execResult;
                }
                
                // 清理
                PipelineResult* removeResult = manager.RemovePipelineByName("ManagerTest");
                if(removeResult != NULL)
                {
                    Print(StringFormat("移除流水線結果: %s - %s", 
                                      removeResult.IsSuccess() ? "成功" : "失敗",
                                      removeResult.GetMessage()));
                    delete removeResult;
                }
                
                Print(StringFormat("清理後流水線數量: %d", manager.GetPipelineCount()));
            }
        }
    }
    else
    {
        Print("EAPipelineManager 獲取失敗");
    }
    
    Print("基本功能測試完成");
}
