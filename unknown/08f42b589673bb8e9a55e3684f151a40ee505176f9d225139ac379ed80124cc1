//+------------------------------------------------------------------+
//|                                       EAPipelineStateRunning.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "EAPipelineState.mqh"
#include "EAPipelineStateCompleted.mqh"
#include "EAPipelineStateFailed.mqh"

//+------------------------------------------------------------------+
//| 流水線執行中狀態 - 實現執行中狀態的行為                           |
//+------------------------------------------------------------------+

class EAPipelineStateRunning : public EAPipelineState
{
public:
    // 構造函數
    EAPipelineStateRunning()
    : EAPipelineState(EA_PIPELINE_STATE_RUNNING, "執行中") {}
    
    // 析構函數
    ~EAPipelineStateRunning() {}
    
    // 執行流水線
    void Execute(EAPipeline_A* pipeline) override;
    
    // 重置流水線狀態
    void Restore(EAPipeline_A* pipeline) override;
};


void EAPipelineStateRunning::Execute(EAPipeline_A* pipeline)
{
    // 執行中狀態下不應該再次執行
    Print("警告: 流水線 '", pipeline.GetName(), "' 已經在執行中，不能重複執行");
}


void EAPipelineStateRunning::Restore(EAPipeline_A* pipeline)
{
    // 將狀態轉換為待執行
    pipeline.SetState(new EAPipelineStatePending());
    pipeline.Restore();
}
