#property strict

#include "../state/EACompoundPipeline_A/package.mqh"

class EACompoundPipelineStateFactory
{
private:
    static EACompoundPipelineState* s_states[];

public:
    EACompoundPipelineStateFactory()
    {
    }

    static EACompoundPipelineState* GetState(ENUM_EA_PIPELINE_STATE state)
    {
        for(int i = 0; i < ArraySize(s_states); i++)
        {
            if(s_states[i].GetState() == state)
            {
                return s_states[i];
            }
        }

        EACompoundPipelineState* state_obj = NULL;
        switch(state)
        {
            case EA_PIPELINE_STATE_PENDING:
                state_obj = new EACompoundPipelineStatePending();
                break;
            case EA_PIPELINE_STATE_RUNNING:
                state_obj = new EACompoundPipelineStateRunning();
                break;
            case EA_PIPELINE_STATE_COMPLETED:
                state_obj = new EACompoundPipelineStateCompleted();
                break;
            case EA_PIPELINE_STATE_FAILED:
                state_obj = new EACompoundPipelineStateFailed();
                break;
            case EA_PIPELINE_STATE_SKIPPED:
                state_obj = new EACompoundPipelineStateSkipped();
                break;
            default:
                break;
        }

        ArrayResize(s_states, ArraySize(s_states) + 1);
        s_states[ArraySize(s_states) - 1] = state_obj;

        return state_obj;
    }
};

EACompoundPipelineState* EACompoundPipelineStateFactory::s_states[];
