//+------------------------------------------------------------------+
//|                                                       Enum.mqh |
//|                                                      EA_Wizard |
//|                                                                |
//+------------------------------------------------------------------+
#property strict

//+------------------------------------------------------------------+
//| EA事件類型枚舉定義                                               |
//+------------------------------------------------------------------+
enum ENUM_EA_EVENT
{
    ONINIT,                      // OnInit 階段
    ONTICK,                      // OnTick 階段
    ONDEINIT                     // OnDeinit 階段
};

//+------------------------------------------------------------------+
//| EA事件階段類型枚舉                                               |
//+------------------------------------------------------------------+
enum ENUM_EA_SUB_EVENT
{
    // OnInit 階段
    ONINIT_START,                // OnInit 開始階段
    ONINIT_PARAMETER_READ,       // 參數讀取階段
    ONINIT_VARIABLE_INIT,        // 變數初始化階段
    ONINIT_TRADING_ENV_CHECK,    // 交易環境檢查階段
    ONINIT_INDICATOR_INIT,       // 指標初始化階段
    ONINIT_END,                  // OnInit 結束階段

    // OnTick 階段
    ONTICK_DATAFEED,             // 數據饋送階段
    ONTICK_SIGNAL,               // 信號階段
    ONTICK_ORDER,                // 訂單階段
    ONTICK_RISK,                 // 風險階段
    ONTICK_LOG,                  // 日誌階段
    ONTICK_ERROR,                // 錯誤階段

    // OnDeinit 階段
    ONDEINIT_CLEANUP,            // 清理階段
    ONDEINIT_LOGGING             // 日誌記錄階段
};
