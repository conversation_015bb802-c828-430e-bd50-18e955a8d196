#property strict

//+------------------------------------------------------------------+
//| TestITradingPipelineDriverErrorHandler.mqh                      |
//| ITradingPipelineDriver GetErrorHandler() 方法的單元測試         |
//+------------------------------------------------------------------+

#include "../TestFramework.mqh"
#include "../../TradingPipelineDriver.mqh"
#include "../../feature/TradingPipelineDriverLoggerDecorator.mqh"
#include "../../../MQL4Logger/FileLog.mqh"

//+------------------------------------------------------------------+
//| ITradingPipelineDriver GetErrorHandler() 方法測試類             |
//+------------------------------------------------------------------+
class TestITradingPipelineDriverErrorHandler : public TestCase
{
private:
    TestRunner* m_runner;

public:
    TestITradingPipelineDriverErrorHandler(TestRunner* runner = NULL)
        : TestCase("TestITradingPipelineDriverErrorHandler"), m_runner(runner) {}

    virtual void RunTests() override
    {
        Print("開始執行 ITradingPipelineDriver GetErrorHandler() 方法測試...");

        TestTradingPipelineDriverGetErrorHandler();
        TestTradingPipelineDriverLoggerDecoratorGetErrorHandler();
        TestErrorHandlerConsistency();
        TestErrorHandlerFunctionality();
        TestErrorHandlerLifecycle();

        Print("ITradingPipelineDriver GetErrorHandler() 方法測試完成");
    }

private:
    // 測試 TradingPipelineDriver 的 GetErrorHandler 方法
    void TestTradingPipelineDriverGetErrorHandler()
    {
        Print("--- 測試 TradingPipelineDriver GetErrorHandler 方法 ---");

        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();

        if(driver == NULL)
        {
            if(m_runner != NULL)
            {
                m_runner.RecordResult(new TestResult(
                    "TestITradingPipelineDriverErrorHandler::TestTradingPipelineDriverGetErrorHandler - 驅動器實例",
                    false,
                    "無法獲取 TradingPipelineDriver 實例"
                ));
            }
            return;
        }

        // 確保驅動器已初始化
        if(!driver.IsInitialized())
        {
            bool initResult = driver.Initialize();
            if(m_runner != NULL)
            {
                m_runner.RecordResult(new TestResult(
                    "TestITradingPipelineDriverErrorHandler::TestTradingPipelineDriverGetErrorHandler - 驅動器初始化",
                    initResult,
                    initResult ? "驅動器初始化成功" : "驅動器初始化失敗"
                ));
            }

            if(!initResult) return;
        }

        // 測試 GetErrorHandler 方法
        TradingMessageHandler* errorHandler = driver.GetErrorHandler();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestITradingPipelineDriverErrorHandler::TestTradingPipelineDriverGetErrorHandler - 錯誤處理器不為空",
                errorHandler != NULL,
                errorHandler != NULL ? "錯誤處理器獲取成功" : "錯誤處理器為空"
            ));
        }

        if(errorHandler != NULL)
        {
            // 測試錯誤處理器的基本功能
            int initialCount = errorHandler.GetMessageCount();
            errorHandler.AddMessage("測試訊息", "TestTradingPipelineDriver", ERROR_LEVEL_INFO);
            int newCount = errorHandler.GetMessageCount();

            if(m_runner != NULL)
            {
                m_runner.RecordResult(new TestResult(
                    "TestITradingPipelineDriverErrorHandler::TestTradingPipelineDriverGetErrorHandler - 訊息處理器功能正常",
                    newCount == initialCount + 1,
                    "訊息處理器應該能夠正常添加訊息"
                ));

                TradingMessageRecord* lastRecord = errorHandler.GetLastMessage(ERROR_LEVEL_INFO);
                m_runner.RecordResult(new TestResult(
                    "TestITradingPipelineDriverErrorHandler::TestTradingPipelineDriverGetErrorHandler - 最後訊息正確",
                    lastRecord != NULL && lastRecord.m_message == "測試訊息",
                    "訊息處理器應該返回正確的最後訊息"
                ));
                if(lastRecord != NULL) delete lastRecord;
            }
        }
    }

    // 測試 TradingPipelineDriverLoggerDecorator 的 GetErrorHandler 方法
    void TestTradingPipelineDriverLoggerDecoratorGetErrorHandler()
    {
        Print("--- 測試 TradingPipelineDriverLoggerDecorator GetErrorHandler 方法 ---");

        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        if(driver == NULL || !driver.IsInitialized())
        {
            if(m_runner != NULL)
            {
                m_runner.RecordResult(new TestResult(
                    "TestITradingPipelineDriverErrorHandler::TestTradingPipelineDriverLoggerDecoratorGetErrorHandler - 前置條件",
                    false,
                    "TradingPipelineDriver 未正確初始化"
                ));
            }
            return;
        }

        // 創建日誌記錄器
        CFileLog* logger = new CFileLog("TestErrorHandlerDecorator.log", INFO, true, true);
        if(logger == NULL)
        {
            if(m_runner != NULL)
            {
                m_runner.RecordResult(new TestResult(
                    "TestITradingPipelineDriverErrorHandler::TestTradingPipelineDriverLoggerDecoratorGetErrorHandler - 日誌創建",
                    false,
                    "無法創建 CFileLog 實例"
                ));
            }
            return;
        }

        // 創建裝飾者
        TradingPipelineDriverLoggerDecorator* decorator =
            new TradingPipelineDriverLoggerDecorator(driver, logger, true);

        if(decorator == NULL)
        {
            if(m_runner != NULL)
            {
                m_runner.RecordResult(new TestResult(
                    "TestITradingPipelineDriverErrorHandler::TestTradingPipelineDriverLoggerDecoratorGetErrorHandler - 裝飾者創建",
                    false,
                    "無法創建 TradingPipelineDriverLoggerDecorator 實例"
                ));
            }
            delete logger;
            return;
        }

        // 初始化裝飾者
        bool initResult = decorator.Initialize();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestITradingPipelineDriverErrorHandler::TestTradingPipelineDriverLoggerDecoratorGetErrorHandler - 裝飾者初始化",
                initResult,
                initResult ? "裝飾者初始化成功" : "裝飾者初始化失敗"
            ));
        }

        if(initResult)
        {
            // 測試裝飾者的 GetErrorHandler 方法
            TradingMessageHandler* decoratorErrorHandler = decorator.GetErrorHandler();
            TradingMessageHandler* originalErrorHandler = driver.GetErrorHandler();

            if(m_runner != NULL)
            {
                m_runner.RecordResult(new TestResult(
                    "TestITradingPipelineDriverErrorHandler::TestTradingPipelineDriverLoggerDecoratorGetErrorHandler - 裝飾者錯誤處理器不為空",
                    decoratorErrorHandler != NULL,
                    decoratorErrorHandler != NULL ? "裝飾者錯誤處理器獲取成功" : "裝飾者錯誤處理器為空"
                ));

                m_runner.RecordResult(new TestResult(
                    "TestITradingPipelineDriverErrorHandler::TestTradingPipelineDriverLoggerDecoratorGetErrorHandler - 錯誤處理器一致性",
                    decoratorErrorHandler == originalErrorHandler,
                    decoratorErrorHandler == originalErrorHandler ? "裝飾者和原始驅動器返回相同的錯誤處理器" : "錯誤處理器不一致"
                ));
            }
        }

        delete decorator;
        delete logger;
    }

    // 測試錯誤處理器的一致性
    void TestErrorHandlerConsistency()
    {
        Print("--- 測試錯誤處理器一致性 ---");

        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        if(driver == NULL || !driver.IsInitialized())
        {
            if(m_runner != NULL)
            {
                m_runner.RecordResult(new TestResult(
                    "TestITradingPipelineDriverErrorHandler::TestErrorHandlerConsistency - 前置條件",
                    false,
                    "TradingPipelineDriver 未正確初始化"
                ));
            }
            return;
        }

        // 多次獲取錯誤處理器，應該返回相同的實例
        TradingMessageHandler* handler1 = driver.GetErrorHandler();
        TradingMessageHandler* handler2 = driver.GetErrorHandler();
        TradingMessageHandler* handler3 = driver.GetErrorHandler();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestITradingPipelineDriverErrorHandler::TestErrorHandlerConsistency - 多次獲取一致性1",
                handler1 == handler2,
                "多次調用 GetErrorHandler 應該返回相同實例"
            ));

            m_runner.RecordResult(new TestResult(
                "TestITradingPipelineDriverErrorHandler::TestErrorHandlerConsistency - 多次獲取一致性2",
                handler2 == handler3,
                "多次調用 GetErrorHandler 應該返回相同實例"
            ));

            m_runner.RecordResult(new TestResult(
                "TestITradingPipelineDriverErrorHandler::TestErrorHandlerConsistency - 所有實例一致",
                handler1 == handler2 && handler2 == handler3,
                "所有獲取的錯誤處理器實例應該相同"
            ));
        }
    }

    // 測試錯誤處理器功能
    void TestErrorHandlerFunctionality()
    {
        Print("--- 測試錯誤處理器功能 ---");

        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        if(driver == NULL || !driver.IsInitialized())
        {
            if(m_runner != NULL)
            {
                m_runner.RecordResult(new TestResult(
                    "TestITradingPipelineDriverErrorHandler::TestErrorHandlerFunctionality - 前置條件",
                    false,
                    "TradingPipelineDriver 未正確初始化"
                ));
            }
            return;
        }

        TradingMessageHandler* errorHandler = driver.GetErrorHandler();
        if(errorHandler == NULL)
        {
            if(m_runner != NULL)
            {
                m_runner.RecordResult(new TestResult(
                    "TestITradingPipelineDriverErrorHandler::TestErrorHandlerFunctionality - 錯誤處理器獲取",
                    false,
                    "無法獲取錯誤處理器"
                ));
            }
            return;
        }

        // 清除現有訊息
        errorHandler.ClearMessages();

        // 測試添加不同級別的訊息
        errorHandler.AddMessage("信息級別訊息", "TestFunctionality", ERROR_LEVEL_INFO);
        errorHandler.AddMessage("警告級別訊息", "TestFunctionality", ERROR_LEVEL_WARNING);
        errorHandler.AddMessage("錯誤級別訊息", "TestFunctionality", ERROR_LEVEL_ERROR);
        errorHandler.AddMessage("嚴重級別訊息", "TestFunctionality", ERROR_LEVEL_CRITICAL);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestITradingPipelineDriverErrorHandler::TestErrorHandlerFunctionality - 訊息數量統計",
                errorHandler.GetMessageCount() == 4,
                "應該正確統計訊息數量"
            ));

            m_runner.RecordResult(new TestResult(
                "TestITradingPipelineDriverErrorHandler::TestErrorHandlerFunctionality - 嚴重訊息檢測",
                errorHandler.HasMessages(ERROR_LEVEL_CRITICAL),
                "應該檢測到嚴重訊息"
            ));

            m_runner.RecordResult(new TestResult(
                "TestITradingPipelineDriverErrorHandler::TestErrorHandlerFunctionality - 訊息摘要生成",
                errorHandler.GetMessageSummary() != "",
                "應該能夠生成訊息摘要"
            ));

            TradingMessageRecord* lastRecord = errorHandler.GetLastMessage(ERROR_LEVEL_CRITICAL);
            m_runner.RecordResult(new TestResult(
                "TestITradingPipelineDriverErrorHandler::TestErrorHandlerFunctionality - 最後訊息正確",
                lastRecord != NULL && lastRecord.m_message == "嚴重級別訊息",
                "最後訊息應該是最新添加的訊息"
            ));
            if(lastRecord != NULL) delete lastRecord;
        }
    }

    // 測試錯誤處理器生命週期
    void TestErrorHandlerLifecycle()
    {
        Print("--- 測試錯誤處理器生命週期 ---");

        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        if(driver == NULL)
        {
            if(m_runner != NULL)
            {
                m_runner.RecordResult(new TestResult(
                    "TestITradingPipelineDriverErrorHandler::TestErrorHandlerLifecycle - 驅動器獲取",
                    false,
                    "無法獲取 TradingPipelineDriver 實例"
                ));
            }
            return;
        }

        // 測試初始化前的狀態
        if(!driver.IsInitialized())
        {
            TradingMessageHandler* preInitHandler = driver.GetErrorHandler();
            if(m_runner != NULL)
            {
                m_runner.RecordResult(new TestResult(
                    "TestITradingPipelineDriverErrorHandler::TestErrorHandlerLifecycle - 初始化前錯誤處理器",
                    preInitHandler != NULL,
                    "初始化前應該能夠獲取錯誤處理器"
                ));
            }

            // 初始化驅動器
            bool initResult = driver.Initialize();
            if(m_runner != NULL)
            {
                m_runner.RecordResult(new TestResult(
                    "TestITradingPipelineDriverErrorHandler::TestErrorHandlerLifecycle - 驅動器初始化",
                    initResult,
                    initResult ? "驅動器初始化成功" : "驅動器初始化失敗"
                ));
            }

            if(initResult)
            {
                TradingMessageHandler* postInitHandler = driver.GetErrorHandler();
                if(m_runner != NULL)
                {
                    m_runner.RecordResult(new TestResult(
                        "TestITradingPipelineDriverErrorHandler::TestErrorHandlerLifecycle - 初始化後錯誤處理器一致性",
                        preInitHandler == postInitHandler,
                        "初始化前後應該返回相同的錯誤處理器實例"
                    ));
                }
            }
        }

        // 測試驅動器組件驗證
        bool componentsValid = driver.IsInitialized();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestITradingPipelineDriverErrorHandler::TestErrorHandlerLifecycle - 組件驗證包含錯誤處理器",
                componentsValid,
                "驅動器組件驗證應該包含錯誤處理器檢查"
            ));
        }
    }
};
