//+------------------------------------------------------------------+
//|                                    TestTradingPipelineRegistry.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                    |
//+------------------------------------------------------------------+
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipelineRegistry.mqh"
#include "TestTradingPipeline.mqh"  // 使用 MockTradingPipeline

//+------------------------------------------------------------------+
//| TradingPipelineRegistry 單元測試類                               |
//+------------------------------------------------------------------+
class TestTradingPipelineRegistry : public TestCase
{
private:
    TestRunner* m_runner;

public:
    // 構造函數
    TestTradingPipelineRegistry(TestRunner* runner = NULL)
        : TestCase("TestTradingPipelineRegistry"), m_runner(runner) {}

    // 析構函數
    virtual ~TestTradingPipelineRegistry() {}

    // 運行所有測試
    virtual void RunTests() override
    {
        Print("=== 開始執行 TradingPipelineRegistry 單元測試 ===");

        TestConstructor();
        TestBasicProperties();
        TestStageRegistration();
        TestEventRegistration();
        TestPipelineRegistration();
        TestUnregistration();
        TestStatusMethods();

        Print("=== TradingPipelineRegistry 單元測試完成 ===");
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        Print("--- 測試 TradingPipelineRegistry 構造函數 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("TestManager");

        // 測試基本構造函數
        TradingPipelineRegistry* registry1 = new TradingPipelineRegistry(manager);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestConstructor - 基本構造函數",
                registry1 != NULL,
                registry1 != NULL ? "構造函數成功" : "構造函數失敗"
            ));
        }

        // 測試帶參數的構造函數
        TradingPipelineRegistry* registry2 = new TradingPipelineRegistry(
            manager, "TestRegistry", "TestType", 10, false);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestConstructor - 帶參數構造函數",
                registry2 != NULL && registry2.GetName() == "TestRegistry",
                registry2 != NULL ? "構造函數成功，名稱正確" : "構造函數失敗"
            ));
        }

        // 測試空管理器構造函數
        TradingPipelineRegistry* registry3 = new TradingPipelineRegistry(NULL);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestConstructor - 空管理器構造函數",
                registry3 != NULL,
                "空管理器構造函數成功（應該有警告）"
            ));
        }

        delete registry1;
        delete registry2;
        delete registry3;
        delete manager;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("--- 測試 TradingPipelineRegistry 基本屬性 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("PropManager");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(
            manager, "PropTest", "TestType", 5, false);

        // 測試 GetName
        string name = registry.GetName();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestBasicProperties - GetName",
                name == "PropTest",
                name == "PropTest" ? "名稱正確" : "名稱錯誤: " + name
            ));
        }

        // 測試 GetType
        string type = registry.GetType();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestBasicProperties - GetType",
                type == "TestType",
                type == "TestType" ? "類型正確" : "類型錯誤: " + type
            ));
        }

        // 測試初始狀態
        bool enabled = registry.IsEnabled();
        bool empty = registry.IsEmpty();
        bool full = registry.IsFull();
        int maxReg = registry.GetMaxRegistrations();
        int totalReg = registry.GetTotalRegistrations();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestBasicProperties - 初始狀態",
                enabled && empty && !full && maxReg == 5 && totalReg == 0,
                "初始狀態正確"
            ));
        }

        // 測試 GetManager
        TradingPipelineContainerManager* retrievedManager = registry.GetManager();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestBasicProperties - GetManager",
                retrievedManager == manager,
                "管理器指針正確"
            ));
        }

        delete registry;
        delete manager;
    }

    // 測試階段註冊
    void TestStageRegistration()
    {
        Print("--- 測試 TradingPipelineRegistry 階段註冊 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("StageManager");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "StageTest");

        // 創建測試容器
        TradingPipelineContainer* container = new TradingPipelineContainer("StageContainer");

        // 測試註冊階段
        bool registered = registry.Register(INIT_START, container);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestStageRegistration - 註冊階段",
                registered,
                registered ? "階段註冊成功" : "階段註冊失敗"
            ));
        }

        // 測試檢查階段是否已註冊
        bool isRegistered = registry.IsStageRegistered(INIT_START);
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestStageRegistration - 檢查階段註冊",
                isRegistered,
                "階段註冊狀態正確"
            ));
        }

        // 測試獲取已註冊的階段容器
        TradingPipelineContainer* retrieved = registry.GetRegisteredStageContainer(INIT_START);
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestStageRegistration - 獲取階段容器",
                retrieved == container,
                "階段容器獲取正確"
            ));
        }

        // 測試重複註冊（應該失敗）
        TradingPipelineContainer* container2 = new TradingPipelineContainer("DuplicateContainer");
        bool duplicateRegistered = registry.Register(INIT_START, container2);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestStageRegistration - 重複註冊防護",
                !duplicateRegistered,
                "重複註冊被正確拒絕"
            ));
        }

        // 測試註冊空容器（應該失敗）
        bool nullRegistered = registry.Register(INIT_COMPLETE, (TradingPipelineContainer*)NULL);
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestStageRegistration - 空容器註冊防護",
                !nullRegistered,
                "空容器註冊被正確拒絕"
            ));
        }

        delete registry;
        delete manager;
        delete container;
        delete container2;
    }

    // 測試事件註冊
    void TestEventRegistration()
    {
        Print("--- 測試 TradingPipelineRegistry 事件註冊 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("EventManager");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "EventTest");

        // 創建測試容器
        TradingPipelineContainer* container = new TradingPipelineContainer("EventContainer");

        // 測試註冊事件
        bool registered = registry.Register(TRADING_TICK, container);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestEventRegistration - 註冊事件",
                registered,
                registered ? "事件註冊成功" : "事件註冊失敗"
            ));
        }

        // 測試檢查事件是否已註冊
        bool isRegistered = registry.IsEventRegistered(TRADING_TICK);
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestEventRegistration - 檢查事件註冊",
                isRegistered,
                "事件註冊狀態正確"
            ));
        }

        // 測試獲取已註冊的事件容器
        TradingPipelineContainer* retrieved = registry.GetRegisteredEventContainer(TRADING_TICK);
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestEventRegistration - 獲取事件容器",
                retrieved == container,
                "事件容器獲取正確"
            ));
        }

        delete registry;
        delete manager;
        delete container;
    }

    // 測試流水線註冊
    void TestPipelineRegistration()
    {
        Print("--- 測試 TradingPipelineRegistry 流水線註冊 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("PipelineManager");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "PipelineTest");

        // 先註冊一個容器
        TradingPipelineContainer* container = new TradingPipelineContainer("PipelineContainer");
        registry.Register(INIT_START, container);

        // 創建測試流水線
        MockTradingPipeline* pipeline = new MockTradingPipeline("TestPipeline", "Mock", INIT_START);

        // 測試註冊流水線
        bool registered = registry.Register(pipeline);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestPipelineRegistration - 註冊流水線",
                registered,
                registered ? "流水線註冊成功" : "流水線註冊失敗"
            ));
        }

        // 檢查流水線是否被添加到容器中
        bool hasInContainer = container.HasPipeline(pipeline);
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestPipelineRegistration - 流水線添加到容器",
                hasInContainer,
                "流水線被正確添加到容器"
            ));
        }

        delete registry;
        delete manager;
        delete container;
        delete pipeline;
    }

    // 測試取消註冊
    void TestUnregistration()
    {
        Print("--- 測試 TradingPipelineRegistry 取消註冊 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("UnregManager");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "UnregTest");

        // 註冊階段和事件
        TradingPipelineContainer* stageContainer = new TradingPipelineContainer("StageContainer");
        TradingPipelineContainer* eventContainer = new TradingPipelineContainer("EventContainer");

        registry.Register(INIT_START, stageContainer);
        registry.Register(TRADING_TICK, eventContainer);

        int countBefore = registry.GetTotalRegistrations();

        // 測試取消註冊階段
        bool stageUnregistered = registry.UnregisterStage(INIT_START);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestUnregistration - 取消註冊階段",
                stageUnregistered && !registry.IsStageRegistered(INIT_START),
                "階段取消註冊成功"
            ));
        }

        // 測試取消註冊事件
        bool eventUnregistered = registry.UnregisterEvent(TRADING_TICK);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestUnregistration - 取消註冊事件",
                eventUnregistered && !registry.IsEventRegistered(TRADING_TICK),
                "事件取消註冊成功"
            ));
        }

        int countAfter = registry.GetTotalRegistrations();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestUnregistration - 註冊數量變化",
                countBefore == 2 && countAfter == 0,
                "註冊數量正確變化"
            ));
        }

        delete registry;
        delete manager;
        delete stageContainer;
        delete eventContainer;
    }

    // 測試狀態方法
    void TestStatusMethods()
    {
        Print("--- 測試 TradingPipelineRegistry 狀態方法 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("StatusManager");
        TradingPipelineRegistry* registry = new TradingPipelineRegistry(manager, "StatusTest", "Test", 2);

        // 測試啟用/禁用
        registry.SetEnabled(false);
        bool disabledState = registry.IsEnabled();

        registry.SetEnabled(true);
        bool enabledState = registry.IsEnabled();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestStatusMethods - SetEnabled/IsEnabled",
                !disabledState && enabledState,
                "啟用/禁用功能正確"
            ));
        }

        // 測試滿狀態
        TradingPipelineContainer* container1 = new TradingPipelineContainer("Container1");
        TradingPipelineContainer* container2 = new TradingPipelineContainer("Container2");

        bool emptyBefore = registry.IsEmpty();
        bool fullBefore = registry.IsFull();

        registry.Register(INIT_START, container1);
        registry.Register(TRADING_TICK, container2);

        bool emptyAfter = registry.IsEmpty();
        bool fullAfter = registry.IsFull();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineRegistry::TestStatusMethods - IsEmpty/IsFull",
                emptyBefore && !fullBefore && !emptyAfter && fullAfter,
                "空/滿狀態檢查正確"
            ));
        }

        delete registry;
        delete manager;
        delete container1;
        delete container2;
    }
};
