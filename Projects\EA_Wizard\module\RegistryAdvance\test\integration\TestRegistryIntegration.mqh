//+------------------------------------------------------------------+
//|                                   TestRegistryIntegration.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../ItemRegistry.mqh"
#include "../mock/MockRegistry.mqh"

//+------------------------------------------------------------------+
//| 註冊器整合測試類別                                               |
//+------------------------------------------------------------------+
class TestRegistryIntegration : public TestCase
{
private:
    TestRunner* m_runner;    // 測試運行器引用

public:
    // 構造函數
    TestRegistryIntegration(TestRunner* runner)
    : TestCase("TestRegistryIntegration"), m_runner(runner) {}

    // 運行所有測試
    void RunTests() override
    {
        TestCompleteWorkflow();
        TestMultipleRegistries();
        TestLargeScaleOperations();
        TestErrorRecovery();
        TestConcurrentOperations();
        TestDataIntegrity();
        TestPerformanceScenarios();
        TestEdgeCases();
    }

private:
    // 測試完整工作流程
    void TestCompleteWorkflow()
    {
        SetUp();

        // 創建註冊器
        ItemRegistry<string>* registry = new ItemRegistry<string>("工作流程測試", "WorkflowTest", 10);

        // 階段1：註冊多個項目
        RegistryResult<string>* result1 = registry.Register("config", "配置項目", "config_value");
        RegistryResult<string>* result2 = registry.Register("data", "數據項目", "data_value");
        RegistryResult<string>* result3 = registry.Register("log", "日誌項目", "log_value");

        m_runner.RecordResult(Assert::AssertTrue("完整工作流程_註冊階段成功",
            result1.IsSuccess() && result2.IsSuccess() && result3.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("完整工作流程_註冊後大小", 3, registry.Size()));

        // 階段2：查詢和驗證
        RegistryItem<string>* configItem = registry.Find("config");
        RegistryItem<string>* dataItem = registry.Find("data");
        RegistryItem<string>* logItem = registry.Find("log");

        m_runner.RecordResult(Assert::AssertNotNull("完整工作流程_配置項目存在", configItem));
        m_runner.RecordResult(Assert::AssertNotNull("完整工作流程_數據項目存在", dataItem));
        m_runner.RecordResult(Assert::AssertNotNull("完整工作流程_日誌項目存在", logItem));

        if(configItem != NULL)
            m_runner.RecordResult(Assert::AssertEquals("完整工作流程_配置值正確", "config_value", configItem.GetValue()));

        // 階段3：修改操作（取消註冊和重新註冊）
        bool unregResult = registry.Unregister("data");
        m_runner.RecordResult(Assert::AssertTrue("完整工作流程_取消註冊成功", unregResult));
        m_runner.RecordResult(Assert::AssertEquals("完整工作流程_取消註冊後大小", 2, registry.Size()));

        RegistryResult<string>* newResult = registry.Register("newdata", "新數據項目", "new_data_value");
        m_runner.RecordResult(Assert::AssertTrue("完整工作流程_重新註冊成功", newResult.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("完整工作流程_重新註冊後大小", 3, registry.Size()));

        // 階段4：最終驗證
        m_runner.RecordResult(Assert::AssertNull("完整工作流程_舊數據已移除", (void*)registry.Find("data")));
        m_runner.RecordResult(Assert::AssertNotNull("完整工作流程_新數據存在", (void*)registry.Find("newdata")));

        // 清理
        delete result1;
        delete result2;
        delete result3;
        delete newResult;
        delete registry;

        TearDown();
    }

    // 測試多個註冊器
    void TestMultipleRegistries()
    {
        SetUp();

        // 創建不同類型的註冊器
        ItemRegistry<string>* stringRegistry = new ItemRegistry<string>("字符串註冊器", "StringRegistry", 5);
        ItemRegistry<int>* intRegistry = new ItemRegistry<int>("整數註冊器", "IntRegistry", 5);
        ItemRegistry<double>* doubleRegistry = new ItemRegistry<double>("雙精度註冊器", "DoubleRegistry", 5);

        // 在每個註冊器中註冊項目
        stringRegistry.Register("str1", "字符串1", "value1");
        stringRegistry.Register("str2", "字符串2", "value2");

        intRegistry.Register("int1", "整數1", 100);
        intRegistry.Register("int2", "整數2", 200);

        doubleRegistry.Register("double1", "雙精度1", 1.1);
        doubleRegistry.Register("double2", "雙精度2", 2.2);

        // 驗證每個註冊器獨立工作
        m_runner.RecordResult(Assert::AssertEquals("多註冊器_字符串大小", 2, stringRegistry.Size()));
        m_runner.RecordResult(Assert::AssertEquals("多註冊器_整數大小", 2, intRegistry.Size()));
        m_runner.RecordResult(Assert::AssertEquals("多註冊器_雙精度大小", 2, doubleRegistry.Size()));

        // 驗證相同鍵在不同註冊器中可以共存
        stringRegistry.Register("common", "字符串通用", "string_common");
        intRegistry.Register("common", "整數通用", 999);
        doubleRegistry.Register("common", "雙精度通用", 9.99);

        RegistryItem<string>* stringCommon = stringRegistry.Find("common");
        RegistryItem<int>* intCommon = intRegistry.Find("common");
        RegistryItem<double>* doubleCommon = doubleRegistry.Find("common");

        m_runner.RecordResult(Assert::AssertNotNull("多註冊器_字符串通用項目", (void*)stringCommon));
        m_runner.RecordResult(Assert::AssertNotNull("多註冊器_整數通用項目", (void*)intCommon));
        m_runner.RecordResult(Assert::AssertNotNull("多註冊器_雙精度通用項目", (void*)doubleCommon));

        if(stringCommon != NULL)
            m_runner.RecordResult(Assert::AssertEquals("多註冊器_字符串通用值", "string_common", stringCommon.GetValue()));
        if(intCommon != NULL)
            m_runner.RecordResult(Assert::AssertEquals("多註冊器_整數通用值", 999, intCommon.GetValue()));

        // 清理
        delete stringRegistry;
        delete intRegistry;
        delete doubleRegistry;

        TearDown();
    }

    // 測試大規模操作
    void TestLargeScaleOperations()
    {
        SetUp();

        ItemRegistry<int>* registry = new ItemRegistry<int>("大規模測試", "LargeScaleTest", 100);

        // 大量註冊操作
        int successCount = 0;
        for(int i = 0; i < 50; i++)
        {
            string key = StringFormat("item_%d", i);
            string name = StringFormat("項目_%d", i);
            RegistryResult<string>* result = registry.Register(key, name, i * 10);

            if(result.IsSuccess())
                successCount++;

            delete result;
        }

        m_runner.RecordResult(Assert::AssertEquals("大規模_註冊成功數", 50, successCount));
        m_runner.RecordResult(Assert::AssertEquals("大規模_最終大小", 50, registry.Size()));

        // 大量查詢操作
        int foundCount = 0;
        for(int i = 0; i < 50; i++)
        {
            string key = StringFormat("item_%d", i);
            RegistryItem<int>* item = registry.Find(key);

            if(item != NULL && item.GetValue() == i * 10)
                foundCount++;
        }

        m_runner.RecordResult(Assert::AssertEquals("大規模_查詢成功數", 50, foundCount));

        // 大量取消註冊操作
        int unregisterCount = 0;
        for(int i = 0; i < 25; i++) // 只取消註冊一半
        {
            string key = StringFormat("item_%d", i * 2); // 取消註冊偶數項目
            if(registry.Unregister(key))
                unregisterCount++;
        }

        m_runner.RecordResult(Assert::AssertEquals("大規模_取消註冊成功數", 25, unregisterCount));
        m_runner.RecordResult(Assert::AssertEquals("大規模_取消註冊後大小", 25, registry.Size()));

        // 驗證剩餘項目
        int remainingCount = 0;
        for(int i = 0; i < 50; i++)
        {
            string key = StringFormat("item_%d", i);
            RegistryItem<int>* item = registry.Find(key);

            if(i % 2 == 0) // 偶數項目應該被移除
            {
                m_runner.RecordResult(Assert::AssertNull(StringFormat("大規模_偶數項目%d已移除", i), (void*)item));
            }
            else // 奇數項目應該仍然存在
            {
                if(item != NULL)
                    remainingCount++;
            }
        }

        m_runner.RecordResult(Assert::AssertEquals("大規模_剩餘項目數", 25, remainingCount));

        delete registry;

        TearDown();
    }

    // 測試錯誤恢復
    void TestErrorRecovery()
    {
        SetUp();

        ItemRegistry<string>* registry = new ItemRegistry<string>("錯誤恢復測試", "ErrorRecoveryTest", 3);

        // 正常註冊
        RegistryResult<string>* result1 = registry.Register("normal1", "正常項目1", "值1");
        RegistryResult<string>* result2 = registry.Register("normal2", "正常項目2", "值2");
        m_runner.RecordResult(Assert::AssertTrue("錯誤恢復_正常註冊成功",
            result1.IsSuccess() && result2.IsSuccess()));

        // 嘗試重複鍵（錯誤情況）
        RegistryResult<string>* duplicateResult = registry.Register("normal1", "重複項目", "重複值");
        m_runner.RecordResult(Assert::AssertFalse("錯誤恢復_重複鍵失敗", duplicateResult.IsSuccess()));

        // 驗證原項目未受影響
        RegistryItem<string>* originalItem = registry.Find("normal1");
        m_runner.RecordResult(Assert::AssertNotNull("錯誤恢復_原項目仍存在", (void*)originalItem));
        if(originalItem != NULL)
        {
            m_runner.RecordResult(Assert::AssertEquals("錯誤恢復_原項目值未變", "值1", originalItem.GetValue()));
        }

        // 繼續正常操作
        RegistryResult<string>* result3 = registry.Register("normal3", "正常項目3", "值3");
        m_runner.RecordResult(Assert::AssertTrue("錯誤恢復_錯誤後繼續註冊成功", result3.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("錯誤恢復_最終大小正確", 3, registry.Size()));

        // 嘗試超過容量（錯誤情況）
        RegistryResult<string>* overflowResult = registry.Register("overflow", "溢出項目", "溢出值");
        m_runner.RecordResult(Assert::AssertFalse("錯誤恢復_容量溢出失敗", overflowResult.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("錯誤恢復_溢出後大小不變", 3, registry.Size()));

        // 驗證系統仍然可以正常工作（取消註冊後重新註冊）
        bool unregResult = registry.Unregister("normal2");
        m_runner.RecordResult(Assert::AssertTrue("錯誤恢復_錯誤後取消註冊成功", unregResult));

        RegistryResult<string>* newResult = registry.Register("new_item", "新項目", "新值");
        m_runner.RecordResult(Assert::AssertTrue("錯誤恢復_錯誤後新註冊成功", newResult.IsSuccess()));

        // 清理
        delete result1;
        delete result2;
        delete result3;
        delete duplicateResult;
        delete overflowResult;
        delete newResult;
        delete registry;

        TearDown();
    }

    // 測試併發操作（模擬）
    void TestConcurrentOperations()
    {
        SetUp();

        ItemRegistry<int>* registry = new ItemRegistry<int>("併發測試", "ConcurrentTest", 20);

        // 模擬併發註冊（實際上是順序執行，但測試邏輯一致性）
        bool allSuccessful = true;
        for(int batch = 0; batch < 3; batch++)
        {
            // 每批註冊5個項目
            for(int i = 0; i < 5; i++)
            {
                string key = StringFormat("batch_%d_item_%d", batch, i);
                string name = StringFormat("批次%d項目%d", batch, i);
                RegistryResult<string>* result = registry.Register(key, name, batch * 100 + i);

                if(!result.IsSuccess())
                    allSuccessful = false;

                delete result;
            }
        }

        m_runner.RecordResult(Assert::AssertTrue("併發操作_所有註冊成功", allSuccessful));
        m_runner.RecordResult(Assert::AssertEquals("併發操作_最終大小", 15, registry.Size()));

        // 模擬併發查詢
        int querySuccessCount = 0;
        for(int batch = 0; batch < 3; batch++)
        {
            for(int i = 0; i < 5; i++)
            {
                string key = StringFormat("batch_%d_item_%d", batch, i);
                RegistryItem<int>* item = registry.Find(key);

                if(item != NULL && item.GetValue() == batch * 100 + i)
                    querySuccessCount++;
            }
        }

        m_runner.RecordResult(Assert::AssertEquals("併發操作_查詢成功數", 15, querySuccessCount));

        // 模擬併發取消註冊
        int unregisterSuccessCount = 0;
        for(int batch = 0; batch < 2; batch++) // 只取消前兩批
        {
            for(int i = 0; i < 5; i++)
            {
                string key = StringFormat("batch_%d_item_%d", batch, i);
                if(registry.Unregister(key))
                    unregisterSuccessCount++;
            }
        }

        m_runner.RecordResult(Assert::AssertEquals("併發操作_取消註冊成功數", 10, unregisterSuccessCount));
        m_runner.RecordResult(Assert::AssertEquals("併發操作_取消註冊後大小", 5, registry.Size()));

        delete registry;

        TearDown();
    }

    // 測試數據完整性
    void TestDataIntegrity()
    {
        SetUp();

        ItemRegistry<string>* registry = new ItemRegistry<string>("數據完整性測試", "IntegrityTest", 10);

        // 註冊包含特殊字符的數據
        string specialKey = "special_key_!@#$%^&*()";
        string specialName = "特殊名稱：包含中文、英文、數字123和符號!@#";
        string specialValue = "特殊值：包含換行\n製表符\t和引號\"'";

        RegistryResult<string>* specialResult = registry.Register(specialKey, specialName, specialValue);
        m_runner.RecordResult(Assert::AssertTrue("數據完整性_特殊字符註冊成功", specialResult.IsSuccess()));

        // 驗證特殊字符數據的完整性
        RegistryItem<string>* specialItem = registry.Find(specialKey);
        m_runner.RecordResult(Assert::AssertNotNull("數據完整性_特殊字符項目存在", (void*)specialItem));

        if(specialItem != NULL)
        {
            m_runner.RecordResult(Assert::AssertEquals("數據完整性_特殊字符名稱", specialName, specialItem.GetName()));
            m_runner.RecordResult(Assert::AssertEquals("數據完整性_特殊字符值", specialValue, specialItem.GetValue()));
        }

        // 測試長數據的完整性
        string longKey = "long_data_key";
        string longName = "";
        string longValue = "";

        // 生成長字符串
        for(int i = 0; i < 100; i++)
        {
            longName += StringFormat("名稱部分%d ", i);
            longValue += StringFormat("值部分%d ", i);
        }

        RegistryResult<string>* longResult = registry.Register(longKey, longName, longValue);
        m_runner.RecordResult(Assert::AssertTrue("數據完整性_長數據註冊成功", longResult.IsSuccess()));

        RegistryItem<string>* longItem = registry.Find(longKey);
        m_runner.RecordResult(Assert::AssertNotNull("數據完整性_長數據項目存在", (void*)longItem));

        if(longItem != NULL)
        {
            m_runner.RecordResult(Assert::AssertEquals("數據完整性_長數據名稱", longName, longItem.GetName()));
            m_runner.RecordResult(Assert::AssertEquals("數據完整性_長數據值", longValue, longItem.GetValue()));
        }

        // 清理
        delete specialResult;
        delete longResult;
        delete registry;

        TearDown();
    }

    // 測試性能場景
    void TestPerformanceScenarios()
    {
        SetUp();

        ItemRegistry<int>* registry = new ItemRegistry<int>("性能測試", "PerformanceTest", 1000);

        uint startTime = GetTickCount();

        // 批量註冊
        for(int i = 0; i < 100; i++)
        {
            string key = StringFormat("perf_item_%d", i);
            RegistryResult<string>* result = registry.Register(key, "性能測試項目", i);
            delete result;
        }

        uint registerTime = GetTickCount() - startTime;

        // 批量查詢
        startTime = GetTickCount();
        int foundCount = 0;
        for(int i = 0; i < 100; i++)
        {
            string key = StringFormat("perf_item_%d", i);
            RegistryItem<int>* item = registry.Find(key);
            if(item != NULL) foundCount++;
        }

        uint queryTime = GetTickCount() - startTime;

        m_runner.RecordResult(Assert::AssertEquals("性能測試_註冊數量", 100, registry.Size()));
        m_runner.RecordResult(Assert::AssertEquals("性能測試_查詢成功數", 100, foundCount));
        m_runner.RecordResult(Assert::AssertTrue("性能測試_註冊時間合理", registerTime < 1000)); // 小於1秒
        m_runner.RecordResult(Assert::AssertTrue("性能測試_查詢時間合理", queryTime < 500));   // 小於0.5秒

        Print(StringFormat("性能測試結果 - 註冊時間: %d ms, 查詢時間: %d ms", registerTime, queryTime));

        delete registry;

        TearDown();
    }

    // 測試邊界情況
    void TestEdgeCases()
    {
        SetUp();

        // 測試最小容量註冊器
        ItemRegistry<string>* minRegistry = new ItemRegistry<string>("最小容量", "MinCapacity", 1);

        RegistryResult<string>* result1 = minRegistry.Register("only", "唯一項目", "唯一值");
        m_runner.RecordResult(Assert::AssertTrue("邊界情況_最小容量註冊成功", result1.IsSuccess()));

        RegistryResult<string>* result2 = minRegistry.Register("second", "第二項目", "第二值");
        m_runner.RecordResult(Assert::AssertFalse("邊界情況_最小容量溢出失敗", result2.IsSuccess()));

        // 測試零容量註冊器
        ItemRegistry<string>* zeroRegistry = new ItemRegistry<string>("零容量", "ZeroCapacity", 0);

        RegistryResult<string>* zeroResult = zeroRegistry.Register("any", "任何項目", "任何值");
        m_runner.RecordResult(Assert::AssertFalse("邊界情況_零容量註冊失敗", zeroResult.IsSuccess()));

        // 測試空鍵和空值
        ItemRegistry<string>* emptyRegistry = new ItemRegistry<string>("空值測試", "EmptyTest", 5);

        RegistryResult<string>* emptyKeyResult = emptyRegistry.Register("", "空鍵項目", "空鍵值");
        m_runner.RecordResult(Assert::AssertTrue("邊界情況_空鍵註冊成功", emptyKeyResult.IsSuccess()));

        RegistryItem<string>* emptyKeyItem = emptyRegistry.Find("");
        m_runner.RecordResult(Assert::AssertNotNull("邊界情況_空鍵項目存在", (void*)emptyKeyItem));

        // 清理
        delete result1;
        delete result2;
        delete minRegistry;
        delete zeroResult;
        delete zeroRegistry;
        delete emptyKeyResult;
        delete emptyRegistry;

        TearDown();
    }
};
