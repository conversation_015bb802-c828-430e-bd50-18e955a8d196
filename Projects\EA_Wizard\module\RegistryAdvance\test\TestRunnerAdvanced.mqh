//+------------------------------------------------------------------+
//|                                           TestRunnerAdvanced.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "TestFramework.mqh"

//+------------------------------------------------------------------+
//| 增強版測試運行器 - 支持詳細結果收集和文檔輸出                     |
//+------------------------------------------------------------------+
class TestRunnerAdvanced : public TestRunner
{
private:
    TestResultDetail* m_results[];   // 詳細測試結果數組
    int m_resultCount;               // 結果數量
    string m_currentTestClass;       // 當前測試類別
    datetime m_testStartTime;        // 測試開始時間
    bool m_collectDetails;           // 是否收集詳細信息

public:
    // 構造函數
    TestRunnerAdvanced(bool collectDetails = true)
    : TestRunner(), m_resultCount(0), m_currentTestClass(""),
      m_testStartTime(0), m_collectDetails(collectDetails)
    {
        ArrayResize(m_results, 0);
    }

    // 析構函數
    ~TestRunnerAdvanced()
    {
        ClearResults();
    }

    // 重寫記錄測試結果方法
    virtual void RecordResult(TestResult* result)
    {
        if(result == NULL) return;

        datetime endTime = TimeCurrent();

        // 先保存需要的信息（在基類刪除 result 之前）
        string testName = "";
        bool passed = false;
        string message = "";

        if(m_collectDetails)
        {
            testName = result.GetTestName();
            passed = result.IsPassed();
            message = result.GetMessage();
        }

        // 調用基類方法進行基本記錄（這會刪除 result）
        TestRunner::RecordResult(result);

        // 如果需要收集詳細信息，則使用保存的信息記錄詳細結果
        if(m_collectDetails)
        {
            TestResultDetail* detail = new TestResultDetail(
                testName,
                m_currentTestClass,
                passed,
                message,
                m_testStartTime,
                endTime
            );

            AddResultDetail(detail);
        }

        // 重置測試開始時間
        m_testStartTime = TimeCurrent();
    }

    // 重寫運行測試類別方法
    virtual void RunTestCase(TestCase* testCase)
    {
        if(testCase == NULL) return;

        m_currentTestClass = testCase.GetClassName();
        m_testStartTime = TimeCurrent();

        // 調用基類方法
        TestRunner::RunTestCase(testCase);
    }

    // 獲取詳細結果數量
    int GetResultCount() const { return m_resultCount; }

    // 獲取指定索引的詳細結果
    TestResultDetail* GetResult(int index) const
    {
        if(index >= 0 && index < m_resultCount)
            return m_results[index];
        return NULL;
    }

    // 獲取失敗的測試結果
    void GetFailedResults(TestResultDetail* &failedResults[], int &count)
    {
        count = 0;
        ArrayResize(failedResults, 0);

        for(int i = 0; i < m_resultCount; i++)
        {
            if(m_results[i] != NULL && !m_results[i].IsPassed())
            {
                ArrayResize(failedResults, count + 1);
                failedResults[count] = m_results[i];
                count++;
            }
        }
    }

    // 獲取通過的測試結果
    void GetPassedResults(TestResultDetail* &passedResults[], int &count)
    {
        count = 0;
        ArrayResize(passedResults, 0);

        for(int i = 0; i < m_resultCount; i++)
        {
            if(m_results[i] != NULL && m_results[i].IsPassed())
            {
                ArrayResize(passedResults, count + 1);
                passedResults[count] = m_results[i];
                count++;
            }
        }
    }

    // 按測試類別分組獲取結果
    void GetResultsByClass(string className, TestResultDetail* &classResults[], int &count)
    {
        count = 0;
        ArrayResize(classResults, 0);

        for(int i = 0; i < m_resultCount; i++)
        {
            if(m_results[i] != NULL && m_results[i].GetTestClass() == className)
            {
                ArrayResize(classResults, count + 1);
                classResults[count] = m_results[i];
                count++;
            }
        }
    }

    // 獲取所有測試類別名稱
    void GetTestClasses(string &classes[], int &count)
    {
        count = 0;
        ArrayResize(classes, 0);

        for(int i = 0; i < m_resultCount; i++)
        {
            if(m_results[i] != NULL)
            {
                string className = m_results[i].GetTestClass();
                bool found = false;
                
                for(int j = 0; j < count; j++)
                {
                    if(classes[j] == className)
                    {
                        found = true;
                        break;
                    }
                }
                
                if(!found)
                {
                    ArrayResize(classes, count + 1);
                    classes[count] = className;
                    count++;
                }
            }
        }
    }

    // 獲取執行時間統計
    void GetExecutionTimeStats(int &totalTime, int &avgTime, int &minTime, int &maxTime)
    {
        totalTime = 0;
        avgTime = 0;
        minTime = INT_MAX;
        maxTime = 0;

        if(m_resultCount == 0)
        {
            minTime = 0;
            return;
        }

        for(int i = 0; i < m_resultCount; i++)
        {
            if(m_results[i] != NULL)
            {
                int execTime = m_results[i].GetExecutionTimeMilliseconds();
                totalTime += execTime;
                
                if(execTime < minTime) minTime = execTime;
                if(execTime > maxTime) maxTime = execTime;
            }
        }

        avgTime = m_resultCount > 0 ? totalTime / m_resultCount : 0;
    }

    // 獲取成功率
    double GetSuccessRate() const
    {
        if(GetTotalTests() == 0) return 0.0;
        return (double)GetPassedTests() / GetTotalTests() * 100.0;
    }

    // 設置是否收集詳細信息
    void SetCollectDetails(bool collect) { m_collectDetails = collect; }

    // 清除所有結果
    void ClearResults()
    {
        for(int i = 0; i < m_resultCount; i++)
        {
            if(m_results[i] != NULL)
            {
                delete m_results[i];
                m_results[i] = NULL;
            }
        }
        ArrayResize(m_results, 0);
        m_resultCount = 0;
    }

private:
    // 添加詳細結果
    void AddResultDetail(TestResultDetail* detail)
    {
        if(detail == NULL) return;

        ArrayResize(m_results, m_resultCount + 1);
        m_results[m_resultCount] = detail;
        m_resultCount++;
    }
};
