//+------------------------------------------------------------------+
//|                                PipelineAdvance_v1_Integration.mq4 |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../../Projects/EA_Wizard/module/PipelineAdvance_v1/test/RunAllTests.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("\n" + StringRepeat("=", 80));
    Print("  PipelineAdvance_v1 整合測試演示腳本");
    Print(StringRepeat("=", 80));
    Print("腳本位置: Scripts/test/PipelineAdvance_v1_Integration.mq4");
    Print("開始時間: " + TimeToString(TimeCurrent()));
    Print(StringRepeat("=", 80));

    // 顯示可用的測試選項
    ShowTestOptions();

    // 運行默認的整合測試
    Print("\n🚀 開始運行 PipelineAdvance_v1 完整測試套件...");
    RunAllPipelineAdvanceV1Tests();

    Print("\n" + StringRepeat("=", 80));
    Print("  PipelineAdvance_v1 整合測試演示完成");
    Print("結束時間: " + TimeToString(TimeCurrent()));
    Print(StringRepeat("=", 80));
}

//+------------------------------------------------------------------+
//| 顯示測試選項                                                       |
//+------------------------------------------------------------------+
void ShowTestOptions()
{
    Print("\n📋 可用的測試選項：");
    Print("1. RunAllPipelineAdvanceV1Tests() - 運行所有測試（單元測試 + 整合測試）");
    Print("2. RunPipelineAdvanceV1UnitTests() - 只運行單元測試");
    Print("3. RunPipelineAdvanceV1IntegrationTests() - 只運行整合測試");
    Print("4. QuickPipelineAdvanceV1Check() - 快速檢查");
    Print("");
    Print("📊 測試覆蓋範圍：");
    Print("   - 單元測試：CompositePipeline 類的所有方法");
    Print("   - 整合測試：基本工作流程、錯誤處理、邊界情況、大規模場景");
    Print("");
}

//+------------------------------------------------------------------+
//| 字符串重複函數                                                     |
//+------------------------------------------------------------------+
string StringRepeat(string str, int count)
{
    string result = "";
    for(int i = 0; i < count; i++)
    {
        result += str;
    }
    return result;
}
