//+------------------------------------------------------------------+
//|                                         TestRegistryResult.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../model/RegistryResult.mqh"

//+------------------------------------------------------------------+
//| RegistryResult測試類別                                           |
//+------------------------------------------------------------------+
class TestRegistryResult : public TestCase
{
private:
    TestRunner* m_runner;    // 測試運行器引用
    
public:
    // 構造函數
    TestRegistryResult(TestRunner* runner) 
    : TestCase("TestRegistryResult"), m_runner(runner) {}
    
    // 運行所有測試
    void RunTests() override
    {
        TestConstructor();
        TestIsSuccess();
        TestGetMessage();
        TestGetSource();
        TestGetKey();
        TestSuccessfulResult();
        TestFailedResult();
        TestEmptyValues();
        TestDifferentKeyTypes();
    }
    
private:
    // 測試構造函數
    void TestConstructor()
    {
        SetUp();
        
        // 測試成功結果的構造（字符串鍵）
        RegistryResult<string>* successResult = new RegistryResult<string>(true, "註冊成功", "test_key", "測試來源");
        m_runner.RecordResult(Assert::AssertNotNull("構造函數_成功結果不為空", successResult));
        
        // 測試失敗結果的構造（整數鍵）
        RegistryResult<int>* failResult = new RegistryResult<int>(false, "註冊失敗", 0, "測試來源");
        m_runner.RecordResult(Assert::AssertNotNull("構造函數_失敗結果不為空", failResult));
        
        // 清理
        delete successResult;
        delete failResult;
        
        TearDown();
    }
    
    // 測試IsSuccess方法
    void TestIsSuccess()
    {
        SetUp();
        
        // 測試成功情況
        RegistryResult<string>* successResult = new RegistryResult<string>(true, "成功", "key", "來源");
        m_runner.RecordResult(Assert::AssertTrue("IsSuccess_成功情況", successResult.IsSuccess()));
        
        // 測試失敗情況
        RegistryResult<string>* failResult = new RegistryResult<string>(false, "失敗", "key", "來源");
        m_runner.RecordResult(Assert::AssertFalse("IsSuccess_失敗情況", failResult.IsSuccess()));
        
        // 清理
        delete successResult;
        delete failResult;
        
        TearDown();
    }
    
    // 測試GetMessage方法
    void TestGetMessage()
    {
        SetUp();
        
        string testMessage = "這是測試消息";
        RegistryResult<string>* result = new RegistryResult<string>(true, testMessage, "key", "來源");
        
        m_runner.RecordResult(Assert::AssertEquals("GetMessage_消息內容", 
            testMessage, result.GetMessage()));
        
        // 測試空消息
        RegistryResult<string>* emptyResult = new RegistryResult<string>(true, "", "key", "來源");
        m_runner.RecordResult(Assert::AssertEquals("GetMessage_空消息", 
            "", emptyResult.GetMessage()));
        
        // 清理
        delete result;
        delete emptyResult;
        
        TearDown();
    }
    
    // 測試GetSource方法
    void TestGetSource()
    {
        SetUp();
        
        string testSource = "測試註冊器來源";
        RegistryResult<string>* result = new RegistryResult<string>(true, "消息", "key", testSource);
        
        m_runner.RecordResult(Assert::AssertEquals("GetSource_來源內容", 
            testSource, result.GetSource()));
        
        // 測試空來源
        RegistryResult<string>* emptyResult = new RegistryResult<string>(true, "消息", "key", "");
        m_runner.RecordResult(Assert::AssertEquals("GetSource_空來源", 
            "", emptyResult.GetSource()));
        
        // 清理
        delete result;
        delete emptyResult;
        
        TearDown();
    }
    
    // 測試GetKey方法
    void TestGetKey()
    {
        SetUp();
        
        // 測試字符串鍵
        string testKey = "test_registry_key";
        RegistryResult<string>* stringResult = new RegistryResult<string>(true, "消息", testKey, "來源");
        m_runner.RecordResult(Assert::AssertEquals("GetKey_字符串鍵", 
            testKey, stringResult.GetKey()));
        
        // 測試整數鍵
        int intKey = 12345;
        RegistryResult<int>* intResult = new RegistryResult<int>(true, "消息", intKey, "來源");
        m_runner.RecordResult(Assert::AssertEquals("GetKey_整數鍵", 
            intKey, intResult.GetKey()));
        
        // 清理
        delete stringResult;
        delete intResult;
        
        TearDown();
    }
    
    // 測試成功結果的完整場景
    void TestSuccessfulResult()
    {
        SetUp();
        
        RegistryResult<string>* result = new RegistryResult<string>(true, "項目註冊成功", "main_item", "主註冊器");
        
        m_runner.RecordResult(Assert::AssertTrue("成功結果_IsSuccess", result.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("成功結果_消息", 
            "項目註冊成功", result.GetMessage()));
        m_runner.RecordResult(Assert::AssertEquals("成功結果_鍵", 
            "main_item", result.GetKey()));
        m_runner.RecordResult(Assert::AssertEquals("成功結果_來源", 
            "主註冊器", result.GetSource()));
        
        delete result;
        
        TearDown();
    }
    
    // 測試失敗結果的完整場景
    void TestFailedResult()
    {
        SetUp();
        
        RegistryResult<string>* result = new RegistryResult<string>(false, "項目註冊失敗：鍵已存在", "duplicate_item", "子註冊器");
        
        m_runner.RecordResult(Assert::AssertFalse("失敗結果_IsSuccess", result.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("失敗結果_消息", 
            "項目註冊失敗：鍵已存在", result.GetMessage()));
        m_runner.RecordResult(Assert::AssertEquals("失敗結果_鍵", 
            "duplicate_item", result.GetKey()));
        m_runner.RecordResult(Assert::AssertEquals("失敗結果_來源", 
            "子註冊器", result.GetSource()));
        
        delete result;
        
        TearDown();
    }
    
    // 測試空值和邊界情況
    void TestEmptyValues()
    {
        SetUp();
        
        // 測試所有參數都為空的情況
        RegistryResult<string>* emptyResult = new RegistryResult<string>(false, "", "", "");
        
        m_runner.RecordResult(Assert::AssertFalse("空值_IsSuccess", emptyResult.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("空值_消息", "", emptyResult.GetMessage()));
        m_runner.RecordResult(Assert::AssertEquals("空值_鍵", "", emptyResult.GetKey()));
        m_runner.RecordResult(Assert::AssertEquals("空值_來源", "", emptyResult.GetSource()));
        
        // 測試長字符串
        string longMessage = "這是一個非常長的消息，用來測試RegistryResult是否能正確處理長字符串內容，包括各種特殊字符和中文字符";
        string longKey = "這是一個非常長的鍵名稱，用來測試系統的鍵處理能力";
        string longSource = "這是一個非常長的來源名稱，用來測試系統的來源處理能力";
        
        RegistryResult<string>* longResult = new RegistryResult<string>(true, longMessage, longKey, longSource);
        
        m_runner.RecordResult(Assert::AssertEquals("長字符串_消息", longMessage, longResult.GetMessage()));
        m_runner.RecordResult(Assert::AssertEquals("長字符串_鍵", longKey, longResult.GetKey()));
        m_runner.RecordResult(Assert::AssertEquals("長字符串_來源", longSource, longResult.GetSource()));
        
        // 清理
        delete emptyResult;
        delete longResult;
        
        TearDown();
    }
    
    // 測試不同鍵類型
    void TestDifferentKeyTypes()
    {
        SetUp();
        
        // 測試字符串鍵
        RegistryResult<string>* stringResult = new RegistryResult<string>(true, "字符串鍵測試", "string_key_123", "字符串註冊器");
        m_runner.RecordResult(Assert::AssertEquals("不同鍵類型_字符串", "string_key_123", stringResult.GetKey()));
        
        // 測試整數鍵
        RegistryResult<int>* intResult = new RegistryResult<int>(true, "整數鍵測試", 98765, "整數註冊器");
        m_runner.RecordResult(Assert::AssertEquals("不同鍵類型_整數", 98765, intResult.GetKey()));
        
        // 測試雙精度鍵（如果支援）
        RegistryResult<double>* doubleResult = new RegistryResult<double>(true, "雙精度鍵測試", 123.456, "雙精度註冊器");
        m_runner.RecordResult(Assert::AssertTrue("不同鍵類型_雙精度", 
            MathAbs(doubleResult.GetKey() - 123.456) < 0.001));
        
        // 測試布爾鍵（如果支援）
        RegistryResult<bool>* boolResult = new RegistryResult<bool>(true, "布爾鍵測試", true, "布爾註冊器");
        m_runner.RecordResult(Assert::AssertEquals("不同鍵類型_布爾", true, boolResult.GetKey()));
        
        // 測試零值鍵
        RegistryResult<int>* zeroResult = new RegistryResult<int>(true, "零值鍵測試", 0, "零值註冊器");
        m_runner.RecordResult(Assert::AssertEquals("不同鍵類型_零值", 0, zeroResult.GetKey()));
        
        // 清理
        delete stringResult;
        delete intResult;
        delete doubleResult;
        delete boolResult;
        delete zeroResult;
        
        TearDown();
    }
};
