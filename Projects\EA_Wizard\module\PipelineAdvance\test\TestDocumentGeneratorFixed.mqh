//+------------------------------------------------------------------+
//|                                   TestDocumentGeneratorFixed.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "TestFramework.mqh"
#include "TestRunnerAdvanced.mqh"

//+------------------------------------------------------------------+
//| 修復版測試文檔生成器 - 負責生成測試報告和文檔                     |
//+------------------------------------------------------------------+
class TestDocumentGeneratorFixed
{
private:
    string m_outputDirectory;    // 輸出目錄
    string m_reportFileName;     // 報告文件名
    bool m_includeTimestamp;     // 是否包含時間戳
    bool m_includeDetails;       // 是否包含詳細信息
    int m_maxPassedTestsDisplay; // 最大顯示通過測試數量 (-1 表示無限制)

public:
    // 構造函數
    TestDocumentGeneratorFixed(string outputDir = "TestReports",
                              string fileName = "PipelineAdvance_TestReport",
                              bool includeTimestamp = true,
                              bool includeDetails = true,
                              int maxPassedTestsDisplay = 10)
    : m_outputDirectory(outputDir), m_reportFileName(fileName),
      m_includeTimestamp(includeTimestamp), m_includeDetails(includeDetails),
      m_maxPassedTestsDisplay(maxPassedTestsDisplay)
    {
        // 確保輸出目錄存在
        CreateOutputDirectory();
    }

    // 生成完整的測試報告
    bool GenerateTestReport(TestRunner* runner, string moduleName = "PipelineAdvance")
    {
        if(runner == NULL) return false;

        string fileName = BuildFileName();
        int fileHandle = FileOpen(fileName, FILE_WRITE|FILE_TXT);

        if(fileHandle == INVALID_HANDLE)
        {
            Print("錯誤: 無法創建測試報告文件 - " + fileName);
            return false;
        }

        // 寫入報告內容
        WriteReportHeader(fileHandle, moduleName);
        WriteExecutionSummary(fileHandle, runner);
        WriteTestStatistics(fileHandle, runner);
        WriteDetailedResults(fileHandle, runner);
        WriteReportFooter(fileHandle);

        FileClose(fileHandle);
        Print("✅ 測試報告已生成: " + fileName);
        return true;
    }

    // 生成增強版測試報告
    bool GenerateAdvancedTestReport(TestRunnerAdvanced* runner, string moduleName = "PipelineAdvance")
    {
        if(runner == NULL) return false;

        string fileName = BuildFileName();
        int fileHandle = FileOpen(fileName, FILE_WRITE|FILE_TXT);

        if(fileHandle == INVALID_HANDLE)
        {
            Print("錯誤: 無法創建測試報告文件 - " + fileName);
            return false;
        }

        // 寫入報告內容
        WriteReportHeader(fileHandle, moduleName);
        WriteAdvancedExecutionSummary(fileHandle, runner);
        WriteAdvancedTestStatistics(fileHandle, runner);
        WriteAdvancedResults(fileHandle, runner);
        WriteReportFooter(fileHandle);

        FileClose(fileHandle);
        Print("✅ 增強版測試報告已生成: " + fileName);
        return true;
    }

    // 生成簡化的測試摘要
    bool GenerateTestSummary(TestRunner* runner, string moduleName = "PipelineAdvance")
    {
        if(runner == NULL) return false;

        string fileName = BuildSummaryFileName();
        int fileHandle = FileOpen(fileName, FILE_WRITE|FILE_TXT);

        if(fileHandle == INVALID_HANDLE)
        {
            Print("錯誤: 無法創建測試摘要文件 - " + fileName);
            return false;
        }

        // 寫入摘要內容
        WriteSummaryHeader(fileHandle, moduleName);
        WriteExecutionSummary(fileHandle, runner);
        WriteSummaryFooter(fileHandle);

        FileClose(fileHandle);
        Print("✅ 測試摘要已生成: " + fileName);
        return true;
    }

    // 設置輸出目錄
    void SetOutputDirectory(string directory) { m_outputDirectory = directory; }

    // 設置報告文件名
    void SetReportFileName(string fileName) { m_reportFileName = fileName; }

    // 設置是否包含時間戳
    void SetIncludeTimestamp(bool include) { m_includeTimestamp = include; }

    // 設置是否包含詳細信息
    void SetIncludeDetails(bool include) { m_includeDetails = include; }

    // 設置最大顯示通過測試數量
    void SetMaxPassedTestsDisplay(int maxDisplay) { m_maxPassedTestsDisplay = maxDisplay; }

    // 設置無限制顯示通過測試
    void SetUnlimitedPassedTestsDisplay() { m_maxPassedTestsDisplay = -1; }

private:
    // 創建輸出目錄
    void CreateOutputDirectory()
    {
        if(m_outputDirectory == "")
        {
            m_outputDirectory = "TestReports";
        }
    }

    // 構建完整的文件名
    string BuildFileName()
    {
        string fileName = m_outputDirectory + "\\" + m_reportFileName;

        if(m_includeTimestamp)
        {
            string timestamp = TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES);
            StringReplace(timestamp, ":", "");
            StringReplace(timestamp, ".", "");
            StringReplace(timestamp, " ", "_");
            fileName += "_" + timestamp;
        }

        fileName += ".txt";
        return fileName;
    }

    // 構建摘要文件名
    string BuildSummaryFileName()
    {
        string fileName = m_outputDirectory + "\\" + m_reportFileName + "_Summary";

        if(m_includeTimestamp)
        {
            string timestamp = TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES);
            StringReplace(timestamp, ":", "");
            StringReplace(timestamp, ".", "");
            StringReplace(timestamp, " ", "_");
            fileName += "_" + timestamp;
        }

        fileName += ".txt";
        return fileName;
    }

    // 寫入報告標題
    void WriteReportHeader(int fileHandle, string moduleName)
    {
        FileWrite(fileHandle, "================================================================");
        FileWrite(fileHandle, "                    EA_WIZARD 測試報告");
        FileWrite(fileHandle, "================================================================");
        FileWrite(fileHandle, "");
        FileWrite(fileHandle, "模組名稱: " + moduleName);
        FileWrite(fileHandle, "報告生成時間: " + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS));
        FileWrite(fileHandle, "測試框架版本: EA_Wizard TestFramework v1.0");
        FileWrite(fileHandle, "");
        FileWrite(fileHandle, "================================================================");
        FileWrite(fileHandle, "");
    }

    // 寫入執行摘要
    void WriteExecutionSummary(int fileHandle, TestRunner* runner)
    {
        FileWrite(fileHandle, "📊 測試執行摘要");
        FileWrite(fileHandle, "----------------------------------------------------------------");
        FileWrite(fileHandle, "總測試數量: " + IntegerToString(runner.GetTotalTests()));
        FileWrite(fileHandle, "通過測試: " + IntegerToString(runner.GetPassedTests()));
        FileWrite(fileHandle, "失敗測試: " + IntegerToString(runner.GetFailedTests()));

        double successRate = runner.GetTotalTests() > 0 ?
            (double)runner.GetPassedTests() / runner.GetTotalTests() * 100.0 : 0.0;
        FileWrite(fileHandle, "成功率: " + DoubleToString(successRate, 2) + "%");

        FileWrite(fileHandle, "");
        FileWrite(fileHandle, "測試結果: " + (runner.AllTestsPassed() ? "✅ 全部通過" : "❌ 有失敗"));
        FileWrite(fileHandle, "");
    }

    // 寫入增強版執行摘要
    void WriteAdvancedExecutionSummary(int fileHandle, TestRunnerAdvanced* runner)
    {
        FileWrite(fileHandle, "📊 測試執行摘要 (增強版)");
        FileWrite(fileHandle, "----------------------------------------------------------------");
        FileWrite(fileHandle, "總測試數量: " + IntegerToString(runner.GetTotalTests()));
        FileWrite(fileHandle, "通過測試: " + IntegerToString(runner.GetPassedTests()));
        FileWrite(fileHandle, "失敗測試: " + IntegerToString(runner.GetFailedTests()));
        FileWrite(fileHandle, "詳細結果數量: " + IntegerToString(runner.GetResultCount()));

        double successRate = runner.GetTotalTests() > 0 ?
            (double)runner.GetPassedTests() / runner.GetTotalTests() * 100.0 : 0.0;
        FileWrite(fileHandle, "成功率: " + DoubleToString(successRate, 2) + "%");

        FileWrite(fileHandle, "");
        FileWrite(fileHandle, "測試結果: " + (runner.AllTestsPassed() ? "✅ 全部通過" : "❌ 有失敗"));
        FileWrite(fileHandle, "");
    }

    // 寫入測試統計
    void WriteTestStatistics(int fileHandle, TestRunner* runner)
    {
        FileWrite(fileHandle, "📈 詳細統計信息");
        FileWrite(fileHandle, "----------------------------------------------------------------");

        if(runner.GetTotalTests() > 0)
        {
            double passRate = (double)runner.GetPassedTests() / runner.GetTotalTests() * 100.0;
            double failRate = (double)runner.GetFailedTests() / runner.GetTotalTests() * 100.0;

            FileWrite(fileHandle, "通過率: " + DoubleToString(passRate, 2) + "%");
            FileWrite(fileHandle, "失敗率: " + DoubleToString(failRate, 2) + "%");
        }
        else
        {
            FileWrite(fileHandle, "沒有執行任何測試");
        }

        FileWrite(fileHandle, "");
    }

    // 寫入增強版測試統計
    void WriteAdvancedTestStatistics(int fileHandle, TestRunnerAdvanced* runner)
    {
        FileWrite(fileHandle, "📈 詳細統計信息 (增強版)");
        FileWrite(fileHandle, "----------------------------------------------------------------");

        if(runner.GetTotalTests() > 0)
        {
            double passRate = (double)runner.GetPassedTests() / runner.GetTotalTests() * 100.0;
            double failRate = (double)runner.GetFailedTests() / runner.GetTotalTests() * 100.0;

            FileWrite(fileHandle, "通過率: " + DoubleToString(passRate, 2) + "%");
            FileWrite(fileHandle, "失敗率: " + DoubleToString(failRate, 2) + "%");
            FileWrite(fileHandle, "詳細記錄覆蓋率: 100% (增強版運行器)");

            // 執行時間統計
            int totalTime, avgTime, maxTime, minTime;
            runner.GetExecutionStats(totalTime, avgTime, maxTime, minTime);
            FileWrite(fileHandle, "總執行時間: " + IntegerToString(totalTime) + " ms");
            FileWrite(fileHandle, "平均執行時間: " + IntegerToString(avgTime) + " ms");
        }
        else
        {
            FileWrite(fileHandle, "沒有執行任何測試");
        }

        FileWrite(fileHandle, "");
    }

    // 寫入詳細結果
    void WriteDetailedResults(int fileHandle, TestRunner* runner)
    {
        FileWrite(fileHandle, "📋 詳細測試結果");
        FileWrite(fileHandle, "----------------------------------------------------------------");
        FileWrite(fileHandle, "注意: 基本版本只提供摘要信息");
        FileWrite(fileHandle, "如需詳細結果，請使用 TestRunnerAdvanced");
        FileWrite(fileHandle, "");
    }

    // 寫入增強版詳細結果
    void WriteAdvancedResults(int fileHandle, TestRunnerAdvanced* runner)
    {
        FileWrite(fileHandle, "📋 詳細測試結果 (增強版)");
        FileWrite(fileHandle, "----------------------------------------------------------------");

        // 寫入失敗測試詳情
        WriteFailedTestDetails(fileHandle, runner);

        // 寫入通過測試列表
        WritePassedTestsList(fileHandle, runner);

        FileWrite(fileHandle, "");
    }

    // 寫入失敗測試詳情
    void WriteFailedTestDetails(int fileHandle, TestRunnerAdvanced* runner)
    {
        TestResultDetail* failedResults[];
        int failedCount;
        runner.GetFailedResults(failedResults, failedCount);

        FileWrite(fileHandle, "❌ 失敗測試詳情:");

        if(failedCount == 0)
        {
            FileWrite(fileHandle, "沒有失敗的測試 - 恭喜！");
        }
        else
        {
            for(int i = 0; i < failedCount; i++)
            {
                if(failedResults[i] != NULL)
                {
                    FileWrite(fileHandle, "  " + failedResults[i].ToString());
                }
            }
        }

        FileWrite(fileHandle, "");
    }

    // 寫入通過測試列表
    void WritePassedTestsList(int fileHandle, TestRunnerAdvanced* runner)
    {
        TestResultDetail* passedResults[];
        int passedCount;
        runner.GetPassedResults(passedResults, passedCount);

        FileWrite(fileHandle, "✅ 通過測試列表:");

        if(passedCount == 0)
        {
            FileWrite(fileHandle, "沒有通過的測試");
        }
        else
        {
            int displayCount = GetDisplayCount(passedCount);

            // 顯示測試結果
            for(int i = 0; i < displayCount; i++)
            {
                if(passedResults[i] != NULL)
                {
                    FileWrite(fileHandle, "  " + passedResults[i].ToString());
                }
            }

            // 如果有限制且還有更多測試，顯示省略信息
            if(m_maxPassedTestsDisplay > 0 && passedCount > m_maxPassedTestsDisplay)
            {
                int remainingCount = passedCount - m_maxPassedTestsDisplay;
                FileWrite(fileHandle, "  ... 還有 " + IntegerToString(remainingCount) + " 個通過的測試");
                FileWrite(fileHandle, "  💡 提示: 使用 SetUnlimitedPassedTestsDisplay() 可顯示所有測試");
            }

            // 顯示總計信息
            if(m_maxPassedTestsDisplay == -1)
            {
                FileWrite(fileHandle, "  📊 總計顯示: " + IntegerToString(passedCount) + " 個通過的測試 (無限制模式)");
            }
            else if(passedCount <= m_maxPassedTestsDisplay)
            {
                FileWrite(fileHandle, "  📊 總計顯示: " + IntegerToString(passedCount) + " 個通過的測試 (全部顯示)");
            }
            else
            {
                FileWrite(fileHandle, "  📊 總計顯示: " + IntegerToString(displayCount) + "/" + IntegerToString(passedCount) + " 個通過的測試");
            }
        }

        FileWrite(fileHandle, "");
    }

    // 獲取實際顯示數量
    int GetDisplayCount(int totalCount)
    {
        if(m_maxPassedTestsDisplay == -1)  // 無限制
        {
            return totalCount;
        }
        else if(m_maxPassedTestsDisplay <= 0)  // 不顯示
        {
            return 0;
        }
        else  // 有限制
        {
            return MathMin(totalCount, m_maxPassedTestsDisplay);
        }
    }

    // 寫入報告尾部
    void WriteReportFooter(int fileHandle)
    {
        FileWrite(fileHandle, "================================================================");
        FileWrite(fileHandle, "報告結束");
        FileWrite(fileHandle, "生成工具: EA_Wizard TestDocumentGeneratorFixed");
        FileWrite(fileHandle, "================================================================");
    }

    // 寫入摘要標題
    void WriteSummaryHeader(int fileHandle, string moduleName)
    {
        FileWrite(fileHandle, "================================");
        FileWrite(fileHandle, "    " + moduleName + " 測試摘要");
        FileWrite(fileHandle, "================================");
        FileWrite(fileHandle, "");
        FileWrite(fileHandle, "生成時間: " + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS));
        FileWrite(fileHandle, "");
    }

    // 寫入摘要尾部
    void WriteSummaryFooter(int fileHandle)
    {
        FileWrite(fileHandle, "");
        FileWrite(fileHandle, "================================");
        FileWrite(fileHandle, "摘要結束");
        FileWrite(fileHandle, "================================");
    }
};
