//+------------------------------------------------------------------+
//|                                      EACompoundPipelineStateSkipped.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "EACompoundPipelineState.mqh"
#include "EACompoundPipelineStatePending.mqh"

//+------------------------------------------------------------------+
//| 流水線已跳過狀態 - 實現已跳過狀態的行為                           |
//+------------------------------------------------------------------+

class EACompoundPipelineStateSkipped : public EACompoundPipelineState
{
public:
    // 構造函數
    EACompoundPipelineStateSkipped()
    : EACompoundPipelineState(EA_PIPELINE_STATE_SKIPPED, "已跳過") {}
    
    // 析構函數
    ~EACompoundPipelineStateSkipped() {}
    
    // 執行流水線
    void Execute(EACompoundPipeline_A* pipeline) override;
    
    // 重置流水線狀態
    void Restore(EACompoundPipeline_A* pipeline) override;
};


void EACompoundPipelineStateSkipped::Execute(EACompoundPipeline_A* pipeline)
{
    // 已跳過狀態下不應該再次執行
    Print("警告: 流水線 '", pipeline.GetName(), "' 已被跳過，不能執行");
}


void EACompoundPipelineStateSkipped::Restore(EACompoundPipeline_A* pipeline)
{
    // 將狀態轉換為待執行
    pipeline.SetState(new EACompoundPipelineStatePending());
    pipeline.GetComposite().Restore();
}
