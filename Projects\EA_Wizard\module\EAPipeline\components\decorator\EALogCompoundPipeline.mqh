#property strict

#include "../EACompoundPipeline.mqh"
#include "../EAFileLog.mqh"

//+------------------------------------------------------------------+
//| EALogCompoundPipeline 類 - 日誌裝飾者模式實現的 EACompoundPipeline |
//+------------------------------------------------------------------+
class EALogCompoundPipeline : public EACompoundPipeline
{
private:
    EAFileLog* m_logger;  // 日誌記錄器

public:
    // 構造函數
    EALogCompoundPipeline(PipelineComposite* composite, string type = "EALogCompoundPipeline", EAFileLog* logger = NULL)
        : EACompoundPipeline(composite, type),
          m_logger(logger?logger:EAFileLog::GetInstance())
    {
    }
    EALogCompoundPipeline(EACompoundPipeline* composite, string type = "EALogCompoundPipeline", EAFileLog* logger = NULL)
        : EACompoundPipeline(composite, type),
          m_logger(logger?logger:EAFileLog::GetInstance())
    {
        EACompoundPipeline::SetComposite(composite.GetComposite());
    }
    EALogCompoundPipeline(string name, string type = "EALogCompoundPipeline", EAFileLog* logger = NULL, int maxItems = 100)
        : EACompoundPipeline(name, type, maxItems),
          m_logger(logger?logger:EAFileLog::GetInstance())
    {
    }

    // 析構函數
    virtual ~EALogCompoundPipeline()
    {
    }

    // 執行流水線
    virtual void Execute() override
    {
        m_logger.Log(DEBUG, "開始執行 EALogCompoundPipeline: " + GetName());
        EACompoundPipeline::Execute();
        m_logger.Log(DEBUG, "完成執行 EALogCompoundPipeline: " + GetName());
    }

    // 獲取流水線名稱
    virtual string GetName() override
    {
        return EACompoundPipeline::GetName();
    }

    // 獲取流水線類型
    virtual string GetType() override
    {
        return EACompoundPipeline::GetType();
    }

    // 獲取流水線執行結果
    virtual PipelineResult* GetResult() override
    {
        PipelineResult* result = EACompoundPipeline::GetResult();
        if(result != NULL)
        {
            m_logger.Log(DEBUG, "EALogCompoundPipeline - " + GetName() + "\r\n" + " 結果: " + (result.IsSuccess() ? "成功" : "失敗") + "\r\n" + " 訊息: " + result.GetMessage());
        }
        if(!result.IsSuccess())
        {
            m_logger.Log(WARNING, "EALogCompoundPipeline - " + GetName() + "\r\n" + " 結果: " + "失敗" + "\r\n" + " 訊息: " + result.GetMessage());
        }
        return result;
    }

    // 重置流水線狀態
    virtual void Restore() override
    {
        m_logger.Log(INFO, "重置 EALogCompoundPipeline: " + GetName());
        EACompoundPipeline::Restore();
    }

    // 檢查流水線是否已執行
    virtual bool IsExecuted() override
    {
        return EACompoundPipeline::IsExecuted();
    }

    // 獲取被裝飾的流水線
    virtual PipelineComposite* GetComposite() override
    {
        return EACompoundPipeline::GetComposite();
    }

    // 添加子流水線
    virtual PipelineResult* Add(IPipeline* child) override
    {
        PipelineResult* result = EACompoundPipeline::Add(child);
        if(child != NULL)
        {
            m_logger.Log(DEBUG, "EALogCompoundPipeline - " + GetName() + "\r\n" + " 結果: " + (result.IsSuccess() ? "成功" : "失敗") + "\r\n" + " 訊息: " + result.GetMessage());
        }
        if(!result.IsSuccess())
        {
            m_logger.Log(WARNING, "EALogCompoundPipeline - " + GetName() + "\r\n" + " 結果: " + "失敗" + "\r\n" + " 訊息: " + result.GetMessage());
        }
        return result;
    }

    // 移除子流水線
    virtual PipelineResult* Remove(IPipeline* child) override
    {
        PipelineResult* result = EACompoundPipeline::Remove(child);
        if(child != NULL)
        {
            m_logger.Log(DEBUG, "EALogCompoundPipeline - " + GetName() + "\r\n" + " 結果: " + (result.IsSuccess() ? "成功" : "失敗") + "\r\n" + " 訊息: " + result.GetMessage());
        }
        if(!result.IsSuccess())
        {
            m_logger.Log(WARNING, "EALogCompoundPipeline - " + GetName() + "\r\n" + " 結果: " + "失敗" + "\r\n" + " 訊息: " + result.GetMessage());
        }
        return result;
    }

    // 獲取最大子流水線數量
    virtual int GetMaxItems() override
    {
        return EACompoundPipeline::GetMaxItems();
    }

    // 獲取子流水線數量
    virtual int GetCount() override
    {
        return EACompoundPipeline::GetCount();
    }

    // 獲取所有子流水線
    virtual int GetAllItems(IPipeline* &items[]) override
    {
        return EACompoundPipeline::GetAllItems(items);
    }

protected:
    // 設置被裝飾的流水線
    virtual void SetComposite(PipelineComposite* composite) override
    {
        m_logger.Log(INFO, "設置 EALogCompoundPipeline 的複合流水線: " + GetName());
        EACompoundPipeline::SetComposite(composite);
    }
};
