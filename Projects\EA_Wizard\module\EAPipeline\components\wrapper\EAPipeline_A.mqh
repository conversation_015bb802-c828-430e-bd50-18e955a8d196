#property strict

#include "../EAPipelineBase.mqh"
#include "../../interface/IEAPipelineState.mqh"

//+------------------------------------------------------------------+
//| EAPipeline_A 類 - 包裝流水線，提供流水線狀態的管理功能 |
//+------------------------------------------------------------------+

class EAPipeline_A : public EAPipelineBase
{
private:
    IEAPipelineState<EAPipeline_A*>* m_state; // 流水線狀態

public:
    // 構造函數
    EAPipeline_A(Pipeline* pipeline, string type = "EAPipeline_A")
        : EAPipelineBase(pipeline, type),
          m_state(NULL)
    {
        if(!this.IsExecuted())
            m_state = EAPipelineStateFactory::GetState(EA_PIPELINE_STATE_PENDING);
        else if(this.GetResult().IsSuccess())
            m_state = EAPipelineStateFactory::GetState(EA_PIPELINE_STATE_COMPLETED);
        else
            m_state = EAPipelineStateFactory::GetState(EA_PIPELINE_STATE_FAILED);
    }
    
    EAPipeline_A(EAPipelineBase* pipeline, string type = "EAPipeline_A")
        : EAPipelineBase(pipeline, type),
          m_state(NULL)
    {
        EAPipelineBase::SetPipeline(pipeline.GetPipeline());

        if(!this.IsExecuted())
            m_state = EAPipelineStateFactory::GetState(EA_PIPELINE_STATE_PENDING);
        else if(this.GetResult().IsSuccess())
            m_state = EAPipelineStateFactory::GetState(EA_PIPELINE_STATE_COMPLETED);
        else
            m_state = EAPipelineStateFactory::GetState(EA_PIPELINE_STATE_FAILED);
    }
    
    // 析構函數
    virtual ~EAPipeline_A()
    {
    }

    // 執行流水線
    virtual void Execute() override
    {
        if(m_state != NULL)
        {
            m_state.Execute(GetPointer(this));
        }
    }

    // 重置流水線狀態
    virtual void Restore() override
    {
        if(m_state != NULL)
        {
            m_state.Restore(GetPointer(this));
        }
    }

    // 獲取流水線狀態
    ENUM_EA_PIPELINE_STATE GetState()
    {
        return m_state != NULL ? m_state.GetState() : EA_PIPELINE_STATE_PENDING;
    }

    // 獲取流水線狀態描述
    string GetStateDescription()
    {
        return m_state != NULL ? m_state.GetStateDescription() : "待執行";
    }

    // 設置流水線狀態
    virtual void SetState(IEAPipelineState<EAPipeline_A*>* state)
    {
        m_state = state;
    }
};

#include "../state/EAPipeline_A/package.mqh"
#include "../factory/EAPipelineStateFactory.mqh"
