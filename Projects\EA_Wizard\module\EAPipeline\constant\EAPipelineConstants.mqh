//+------------------------------------------------------------------+
//|                                           PipelineConstants.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#define PIPELINE_ONINIT "OnInitPipeline"
#define PIPELINE_ONTICK "OnTickPipeline"
#define PIPELINE_ONDEINIT "OnDeinitPipeline"

//+------------------------------------------------------------------+
//| OnInit流水線類型常量                                              |
//+------------------------------------------------------------------+

// 初始化開始流水線
#define PIPELINE_INIT_START "OnInitStartPipeline"

// 參數讀取流水線
#define PIPELINE_PARAMETER_READ "ParameterReadPipeline"

// 變數初始化流水線
#define PIPELINE_VARIABLE_INIT "VariableInitPipeline"

// 交易環境檢查流水線
#define PIPELINE_TRADING_ENV_CHECK "TradingEnvironmentCheckPipeline"

// 指標初始化流水線
#define PIPELINE_INDICATOR_INIT "IndicatorInitPipeline"

// 初始化結束流水線
#define PIPELINE_INIT_END "OnInitEndPipeline"

//+------------------------------------------------------------------+
//| OnTick流水線類型常量                                              |
//+------------------------------------------------------------------+

// 數據饋送流水線
#define PIPELINE_DATAFEED "DataFeedPipeline"

// 信號流水線
#define PIPELINE_SIGNAL "SignalPipeline"

// 訂單流水線
#define PIPELINE_ORDER "OrderPipeline"

// 風險流水線
#define PIPELINE_RISK "RiskPipeline"

// 日誌流水線
#define PIPELINE_LOG "LogPipeline"

// 錯誤流水線
#define PIPELINE_ERROR "ErrorPipeline"

//+------------------------------------------------------------------+
//| OnDeinit流水線類型常量                                            |
//+------------------------------------------------------------------+

// 清理流水線
#define PIPELINE_CLEANUP "CleanupPipeline"

// 日誌記錄流水線
#define PIPELINE_DEINIT_LOGGING "DeinitLoggingPipeline"
