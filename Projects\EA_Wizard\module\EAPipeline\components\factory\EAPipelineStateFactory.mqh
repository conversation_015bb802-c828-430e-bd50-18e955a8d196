#property strict

#include "../state/EAPipeline_A/package.mqh"

class EAPipelineStateFactory
{
private:
    static EAPipelineState* s_states[];

public:
    EAPipelineStateFactory()
    {
    }

    static EAPipelineState* GetState(ENUM_EA_PIPELINE_STATE state)
    {
        for(int i = 0; i < ArraySize(s_states); i++)
        {
            if(s_states[i].GetState() == state)
            {
                return s_states[i];
            }
        }

        EAPipelineState* state_obj = NULL;
        switch(state)
        {
            case EA_PIPELINE_STATE_PENDING:
                state_obj = new EAPipelineStatePending();
                break;
            case EA_PIPELINE_STATE_RUNNING:
                state_obj = new EAPipelineStateRunning();
                break;
            case EA_PIPELINE_STATE_COMPLETED:
                state_obj = new EAPipelineStateCompleted();
                break;
            case EA_PIPELINE_STATE_FAILED:
                state_obj = new EAPipelineStateFailed();
                break;
            case EA_PIPELINE_STATE_SKIPPED:
                state_obj = new EAPipelineStateSkipped();
                break;
            default:
                break;
        }

        ArrayResize(s_states, ArraySize(s_states) + 1);
        s_states[ArraySize(s_states) - 1] = state_obj;

        return state_obj;
    }
};

EAPipelineState* EAPipelineStateFactory::s_states[];
