//+------------------------------------------------------------------+
//|                                             OrderIdentifier.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property strict

// 防止重複包含
#ifndef _ORDER_IDENTIFIER_MQH_
#define _ORDER_IDENTIFIER_MQH_

#include "../mql4-lib-master/Lang/String.mqh"

//+------------------------------------------------------------------+
//| 訂單識別策略介面 - 定義訂單識別的核心策略                          |
//+------------------------------------------------------------------+
interface IOrderIdentifierStrategy
{
public:
    // 生成訂單識別代碼
    virtual string GenerateIdentifier(int type, const int &tags[], string name = "", string symbol = "") = 0;
    
    // 從訂單註釋中解析識別代碼
    virtual bool ParseIdentifier(string comment, int &type, int &tags[], string &name, string &symbol) = 0;
    
    // 檢查註釋是否包含識別信息
    virtual bool HasIdentifier(string comment) = 0;
    
    // 從完整註釋中提取用戶註釋部分
    virtual string ExtractUserComment(string comment) = 0;
};

//+------------------------------------------------------------------+
//| 訂單識別基礎類 - 實現通用功能                                      |
//+------------------------------------------------------------------+
class OrderIdentifierBase : public IOrderIdentifierStrategy
{
protected:
    string m_prefix;  // 識別前綴
    
public:
    // 建構函式
    OrderIdentifierBase(string prefix = "EG")
        : m_prefix(prefix)
    {
    }
    
    // 虛擬析構函數
    virtual ~OrderIdentifierBase() {};
    
    // 檢查註釋是否包含識別信息
    virtual bool HasIdentifier(string comment) override
    {
        string prefixPattern = "[" + m_prefix + ":";
        return StringStartsWith(comment, prefixPattern) && StringFind(comment, "]", 0) != -1;
    }
    
    // 從完整註釋中提取用戶註釋部分
    virtual string ExtractUserComment(string comment) override
    {
        if(!HasIdentifier(comment))
            return comment;
        
        int endPos = StringFind(comment, "]", 0);
        if(endPos == -1 || endPos + 1 >= StringLen(comment))
            return "";
        
        return StringSubstr(comment, endPos + 1);
    }
};

#endif // _ORDER_IDENTIFIER_MQH_
